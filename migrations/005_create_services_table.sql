-- Migration: Create services table for creator service offerings
-- This migration creates the services table that allows creators to list their service offerings
-- with pricing information for brand collaboration opportunities

-- Create services table
CREATE TABLE IF NOT EXISTS services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    creator_id TEXT NOT NULL, -- References Clerk user ID from profiles table
    type VARCHAR(10) NOT NULL CHECK (type IN ('embedded','custom')),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    price_min NUMERIC(10,2) NOT NULL CHECK (price_min >= 0),
    price_max NUMERIC(10,2) NOT NULL CHECK (price_max >= price_min),
    status VARCHAR(10) NOT NULL DEFAULT 'active' CHECK (status IN ('active','inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add foreign key constraint to profiles table
ALTER TABLE services ADD CONSTRAINT fk_services_creator_id 
    FOREIGN KEY (creator_id) REFERENCES profiles(clerk_user_id) ON DELETE CASCADE;

-- <PERSON><PERSON> indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_services_creator_id ON services(creator_id);
CREATE INDEX IF NOT EXISTS idx_services_type ON services(type);
CREATE INDEX IF NOT EXISTS idx_services_status ON services(status);
CREATE INDEX IF NOT EXISTS idx_services_created_at ON services(created_at);
CREATE INDEX IF NOT EXISTS idx_services_price_range ON services(price_min, price_max);

-- Create composite index for frequently queried combinations
CREATE INDEX IF NOT EXISTS idx_services_creator_type_status ON services(creator_id, type, status);

-- Add table and column comments
COMMENT ON TABLE services IS 'Creator service offerings for brand collaboration opportunities';

COMMENT ON COLUMN services.id IS 'Unique identifier for the service';
COMMENT ON COLUMN services.creator_id IS 'Clerk user ID of the creator offering the service';
COMMENT ON COLUMN services.type IS 'Service type: embedded (植入推广) or custom (定制推广)';
COMMENT ON COLUMN services.title IS 'Service title/name';
COMMENT ON COLUMN services.description IS 'Detailed description of the service offering';
COMMENT ON COLUMN services.price_min IS 'Minimum price in USDT for the service';
COMMENT ON COLUMN services.price_max IS 'Maximum price in USDT for the service';
COMMENT ON COLUMN services.status IS 'Service status: active (上架) or inactive (下架)';
COMMENT ON COLUMN services.created_at IS 'Timestamp when the service was created';
COMMENT ON COLUMN services.updated_at IS 'Timestamp when the service was last updated';

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_services_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at := now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at on service updates
CREATE TRIGGER update_services_updated_at
    BEFORE UPDATE ON services
    FOR EACH ROW
    EXECUTE FUNCTION update_services_updated_at();

-- Insert default service templates for creators to use
INSERT INTO services (creator_id, type, title, description, price_min, price_max, status)
SELECT 
    'template_user',  -- This will be replaced when creators copy templates
    type,
    title,
    description,
    price_min,
    price_max,
    'inactive'  -- Templates start as inactive
FROM (VALUES
    ('embedded', '短视频口播', '60-90秒品牌宣传视频，包含品牌口播和Logo展示', 300.00, 600.00),
    ('embedded', '社交图文', 'Twitter/小红书图文推广，专业内容创作', 200.00, 400.00),
    ('custom', '定制视频', '脚本+拍摄+剪辑完整制作，专业视频内容', 1000.00, 2500.00),
    ('custom', '品牌合作', '深度品牌内容合作，长期合作关系', 1500.00, 3000.00)
) AS templates(type, title, description, price_min, price_max);

-- Create function to get service templates for creators
CREATE OR REPLACE FUNCTION get_service_templates()
RETURNS TABLE (
    type VARCHAR(10),
    title TEXT,
    description TEXT,
    price_min NUMERIC(10,2),
    price_max NUMERIC(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.type, s.title, s.description, s.price_min, s.price_max
    FROM services s
    WHERE s.creator_id = 'template_user'
    ORDER BY s.type, s.title;
END;
$$ LANGUAGE plpgsql;

-- Create function to copy template to creator's services
CREATE OR REPLACE FUNCTION copy_service_template(
    p_creator_id TEXT,
    p_template_title TEXT
)
RETURNS UUID AS $$
DECLARE
    new_service_id UUID;
    template_record services%ROWTYPE;
BEGIN
    -- Get template record
    SELECT * INTO template_record
    FROM services
    WHERE creator_id = 'template_user' AND title = p_template_title;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Template not found: %', p_template_title;
    END IF;
    
    -- Insert new service based on template
    INSERT INTO services (creator_id, type, title, description, price_min, price_max, status)
    VALUES (p_creator_id, template_record.type, template_record.title, 
            template_record.description, template_record.price_min, template_record.price_max, 'active')
    RETURNING id INTO new_service_id;
    
    RETURN new_service_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to get creator's services grouped by type
CREATE OR REPLACE FUNCTION get_creator_services(p_creator_id TEXT)
RETURNS TABLE (
    service_type VARCHAR(10),
    services JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.type as service_type,
        jsonb_agg(
            jsonb_build_object(
                'id', s.id,
                'title', s.title,
                'description', s.description,
                'priceMin', s.price_min,
                'priceMax', s.price_max,
                'status', s.status,
                'createdAt', s.created_at,
                'updatedAt', s.updated_at
            ) ORDER BY s.created_at DESC
        ) as services
    FROM services s
    WHERE s.creator_id = p_creator_id
    GROUP BY s.type
    ORDER BY s.type;
END;
$$ LANGUAGE plpgsql;

-- Grant appropriate permissions (adjust based on your user setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON services TO app_user;
-- GRANT USAGE ON SEQUENCE services_id_seq TO app_user; 