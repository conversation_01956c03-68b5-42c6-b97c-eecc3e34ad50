-- Migration: Create tools and user_tools tables
-- This migration creates the tools management system for VGee Creator platform
-- Allows creators to discover, favorite, and track usage of creative tools

-- Create tools table for storing creative tools information
CREATE TABLE IF NOT EXISTS tools (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_url VARCHAR(255),
    website_url VARCHAR(255),
    category VARCHAR(50) NOT NULL,
    platform VARCHAR(20)[] NOT NULL DEFAULT '{}', -- Array of platforms: Web, Windows, Mac, iOS, Android
    is_free BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create user_tools table for tracking user interactions with tools
CREATE TABLE IF NOT EXISTS user_tools (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL, -- Clerk user ID
    tool_id UUID NOT NULL,
    is_favorite BOOLEAN DEFAULT false NOT NULL,
    use_count INTEGER DEFAULT 0 NOT NULL,
    last_used_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Foreign key constraint
    CONSTRAINT fk_user_tools_tool_id 
        FOREIGN KEY (tool_id) 
        REFERENCES tools(id) 
        ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate user-tool relationships
    CONSTRAINT uk_user_tools_user_tool 
        UNIQUE (user_id, tool_id)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tools_category ON tools(category);
CREATE INDEX IF NOT EXISTS idx_tools_platform ON tools USING GIN(platform);
CREATE INDEX IF NOT EXISTS idx_tools_is_free ON tools(is_free);
CREATE INDEX IF NOT EXISTS idx_tools_created_at ON tools(created_at);

CREATE INDEX IF NOT EXISTS idx_user_tools_user_id ON user_tools(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tools_tool_id ON user_tools(tool_id);
CREATE INDEX IF NOT EXISTS idx_user_tools_favorite ON user_tools(user_id, is_favorite) WHERE is_favorite = true;
CREATE INDEX IF NOT EXISTS idx_user_tools_last_used ON user_tools(user_id, last_used_at);
CREATE INDEX IF NOT EXISTS idx_user_tools_use_count ON user_tools(user_id, use_count);

-- Add check constraints for data validation
ALTER TABLE tools 
ADD CONSTRAINT chk_tools_name_not_empty 
    CHECK (LENGTH(TRIM(name)) > 0);

ALTER TABLE tools 
ADD CONSTRAINT chk_tools_category_valid 
    CHECK (category IN ('video', 'image', 'audio', 'ai', 'design', 'productivity', 'analytics', 'other'));

ALTER TABLE user_tools 
ADD CONSTRAINT chk_user_tools_use_count_non_negative 
    CHECK (use_count >= 0);

-- Add comments for documentation
COMMENT ON TABLE tools IS 'Creative tools available for creators to discover and use';
COMMENT ON TABLE user_tools IS 'User interactions with tools including favorites and usage tracking';

COMMENT ON COLUMN tools.id IS 'Unique identifier for the tool';
COMMENT ON COLUMN tools.name IS 'Display name of the tool';
COMMENT ON COLUMN tools.description IS 'Detailed description of the tool and its features';
COMMENT ON COLUMN tools.icon_url IS 'URL to the tool''s icon/logo image';
COMMENT ON COLUMN tools.website_url IS 'Official website URL of the tool';
COMMENT ON COLUMN tools.category IS 'Tool category: video, image, audio, ai, design, productivity, analytics, other';
COMMENT ON COLUMN tools.platform IS 'Array of supported platforms: Web, Windows, Mac, iOS, Android';
COMMENT ON COLUMN tools.is_free IS 'Whether the tool is free to use';

COMMENT ON COLUMN user_tools.user_id IS 'Clerk user ID who interacts with the tool';
COMMENT ON COLUMN user_tools.tool_id IS 'Reference to the tool';
COMMENT ON COLUMN user_tools.is_favorite IS 'Whether user has favorited this tool';
COMMENT ON COLUMN user_tools.use_count IS 'Number of times user has used this tool';
COMMENT ON COLUMN user_tools.last_used_at IS 'Timestamp of last tool usage';

-- Create trigger functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_tools_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_user_tools_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update updated_at timestamps
CREATE TRIGGER update_tools_updated_at
    BEFORE UPDATE ON tools
    FOR EACH ROW
    EXECUTE FUNCTION update_tools_updated_at();

CREATE TRIGGER update_user_tools_updated_at
    BEFORE UPDATE ON user_tools
    FOR EACH ROW
    EXECUTE FUNCTION update_user_tools_updated_at();

-- Insert sample tools data for testing and initial content
INSERT INTO tools (name, description, icon_url, website_url, category, platform, is_free) VALUES
-- Video editing tools
('DaVinci Resolve', '专业级视频剪辑软件，支持调色、特效、音频后期制作', 'https://example.com/davinci-icon.png', 'https://www.blackmagicdesign.com/products/davinciresolve', 'video', ARRAY['Windows', 'Mac'], true),
('Adobe Premiere Pro', '行业标准视频编辑软件，功能强大的专业剪辑工具', 'https://example.com/premiere-icon.png', 'https://www.adobe.com/products/premiere.html', 'video', ARRAY['Windows', 'Mac'], false),
('CapCut', '简单易用的视频剪辑应用，适合短视频制作', 'https://example.com/capcut-icon.png', 'https://www.capcut.com/', 'video', ARRAY['Web', 'iOS', 'Android', 'Windows', 'Mac'], true),

-- Image design tools
('Canva', '在线设计平台，提供丰富的模板和设计元素', 'https://example.com/canva-icon.png', 'https://www.canva.com/', 'design', ARRAY['Web', 'iOS', 'Android'], true),
('Adobe Photoshop', '专业图像编辑软件，图像处理行业标准', 'https://example.com/photoshop-icon.png', 'https://www.adobe.com/products/photoshop.html', 'image', ARRAY['Windows', 'Mac'], false),
('Figma', '协作式界面设计工具，支持实时协作', 'https://example.com/figma-icon.png', 'https://www.figma.com/', 'design', ARRAY['Web', 'Windows', 'Mac'], true),

-- AI tools
('ChatGPT', 'AI对话助手，帮助内容创作和文案写作', 'https://example.com/chatgpt-icon.png', 'https://chat.openai.com/', 'ai', ARRAY['Web'], true),
('Midjourney', 'AI图像生成工具，创造独特的艺术作品', 'https://example.com/midjourney-icon.png', 'https://www.midjourney.com/', 'ai', ARRAY['Web'], false),
('Runway ML', 'AI视频编辑和生成平台', 'https://example.com/runway-icon.png', 'https://runwayml.com/', 'ai', ARRAY['Web'], false),

-- Audio tools
('Audacity', '免费开源音频编辑软件', 'https://example.com/audacity-icon.png', 'https://www.audacityteam.org/', 'audio', ARRAY['Windows', 'Mac'], true),
('Adobe Audition', '专业音频编辑和混音软件', 'https://example.com/audition-icon.png', 'https://www.adobe.com/products/audition.html', 'audio', ARRAY['Windows', 'Mac'], false),

-- Analytics tools
('Google Analytics', '网站和应用分析工具', 'https://example.com/ga-icon.png', 'https://analytics.google.com/', 'analytics', ARRAY['Web'], true),
('YouTube Analytics', 'YouTube频道数据分析工具', 'https://example.com/yt-analytics-icon.png', 'https://studio.youtube.com/', 'analytics', ARRAY['Web'], true);
