-- Migration: Migrate profiles table to work with <PERSON> authentication
-- This migration adapts the profiles table to work with Clerk user management
-- Clerk manages users externally, so we only need application-specific profile data

-- First, backup existing data if any (optional step)
-- CREATE TABLE profiles_backup AS SELECT * FROM profiles;

-- Drop foreign key constraint to users table since we're removing NextAuth users table
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS fk_profiles_user_id;

-- Rename id column to clerk_user_id for clarity and change its purpose
-- This will now store Clerk's user ID instead of referencing our local users table
ALTER TABLE profiles RENAME COLUMN id TO clerk_user_id;

-- Add clerk_username for Clerk's username field (optional but useful)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS clerk_username TEXT;

-- Add email field since we no longer have users table to reference
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS email TEXT;

-- Add email_verified field for tracking verification status
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT false;

-- Add external_accounts field to store connected provider info as JSON
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS external_accounts JSONB DEFAULT '{}';

-- Add clerk_created_at to track when the Clerk user was created
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS clerk_created_at TIMESTAMPTZ;

-- Add clerk_updated_at to track when the Clerk user was last updated
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS clerk_updated_at TIMESTAMPTZ;

-- Add profile_completion_percentage for gamification
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS profile_completion_percentage INTEGER DEFAULT 0;

-- Add last_login_at for activity tracking
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMPTZ;

-- Add preferred_language for internationalization
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS preferred_language TEXT DEFAULT 'en';

-- Add notification_preferences as JSONB for flexible notification settings
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"email": true, "push": true, "in_app": true}';

-- Add social_links as JSONB for flexible social media integration
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS social_links JSONB DEFAULT '{}';

-- Update indexes
DROP INDEX IF EXISTS idx_profiles_user_numeric_id;
DROP INDEX IF EXISTS idx_profiles_role;
DROP INDEX IF EXISTS idx_profiles_level;
DROP INDEX IF EXISTS idx_profiles_youtube_id;
DROP INDEX IF EXISTS idx_profiles_wallet_address;
DROP INDEX IF EXISTS idx_profiles_created_at;

-- Create new indexes for Clerk integration
CREATE INDEX IF NOT EXISTS idx_profiles_clerk_user_id ON profiles(clerk_user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_clerk_username ON profiles(clerk_username);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_user_numeric_id ON profiles(user_numeric_id);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_level ON profiles(level);
CREATE INDEX IF NOT EXISTS idx_profiles_youtube_id ON profiles(youtube_id);
CREATE INDEX IF NOT EXISTS idx_profiles_wallet_address ON profiles(wallet_address);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_profiles_last_login_at ON profiles(last_login_at);

-- Update constraints
ALTER TABLE profiles ADD CONSTRAINT IF NOT EXISTS chk_profiles_email_format 
    CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE profiles ADD CONSTRAINT IF NOT EXISTS chk_profiles_completion_percentage 
    CHECK (profile_completion_percentage >= 0 AND profile_completion_percentage <= 100);

ALTER TABLE profiles ADD CONSTRAINT IF NOT EXISTS chk_profiles_language_code 
    CHECK (preferred_language ~ '^[a-z]{2}(-[A-Z]{2})?$');

-- Update comments for new structure
COMMENT ON TABLE profiles IS 'User profile information for CreatorCoin application integrated with Clerk authentication';

COMMENT ON COLUMN profiles.clerk_user_id IS 'Clerk user ID - primary identifier from Clerk authentication service';
COMMENT ON COLUMN profiles.clerk_username IS 'Username from Clerk (if available)';
COMMENT ON COLUMN profiles.email IS 'User email address from Clerk';
COMMENT ON COLUMN profiles.email_verified IS 'Whether the email has been verified through Clerk';
COMMENT ON COLUMN profiles.external_accounts IS 'JSON object storing connected external accounts (Google, GitHub, etc.)';
COMMENT ON COLUMN profiles.clerk_created_at IS 'When the user was created in Clerk';
COMMENT ON COLUMN profiles.clerk_updated_at IS 'When the user was last updated in Clerk';
COMMENT ON COLUMN profiles.profile_completion_percentage IS 'Percentage of profile completion for gamification';
COMMENT ON COLUMN profiles.last_login_at IS 'Timestamp of user''s last login';
COMMENT ON COLUMN profiles.preferred_language IS 'User''s preferred language (ISO 639-1 code)';
COMMENT ON COLUMN profiles.notification_preferences IS 'JSON object with notification preferences';
COMMENT ON COLUMN profiles.social_links IS 'JSON object with social media links';

-- Create function to calculate profile completion percentage
CREATE OR REPLACE FUNCTION calculate_profile_completion(profile_record profiles)
RETURNS INTEGER AS $$
DECLARE
    completion_score INTEGER := 0;
    total_fields INTEGER := 12; -- Total number of optional fields we're checking
BEGIN
    -- Basic information (4 fields)
    IF profile_record.full_name IS NOT NULL AND profile_record.full_name != '' THEN
        completion_score := completion_score + 1;
    END IF;
    
    IF profile_record.avatar_url IS NOT NULL AND profile_record.avatar_url != '' THEN
        completion_score := completion_score + 1;
    END IF;
    
    IF profile_record.motto IS NOT NULL AND profile_record.motto != '' THEN
        completion_score := completion_score + 1;
    END IF;
    
    IF profile_record.preferred_language IS NOT NULL AND profile_record.preferred_language != 'en' THEN
        completion_score := completion_score + 1;
    END IF;
    
    -- YouTube integration (3 fields)
    IF profile_record.youtube_id IS NOT NULL AND profile_record.youtube_id != '' THEN
        completion_score := completion_score + 1;
    END IF;
    
    IF profile_record.youtube_title IS NOT NULL AND profile_record.youtube_title != '' THEN
        completion_score := completion_score + 1;
    END IF;
    
    IF profile_record.subscribers > 0 THEN
        completion_score := completion_score + 1;
    END IF;
    
    -- Wallet integration (1 field)
    IF profile_record.wallet_address IS NOT NULL AND profile_record.wallet_address != '' THEN
        completion_score := completion_score + 1;
    END IF;
    
    -- Social links (1 field)
    IF profile_record.social_links IS NOT NULL AND jsonb_typeof(profile_record.social_links) = 'object' AND profile_record.social_links != '{}' THEN
        completion_score := completion_score + 1;
    END IF;
    
    -- External accounts (1 field) 
    IF profile_record.external_accounts IS NOT NULL AND jsonb_typeof(profile_record.external_accounts) = 'object' AND profile_record.external_accounts != '{}' THEN
        completion_score := completion_score + 1;
    END IF;
    
    -- Notification preferences customized (1 field)
    IF profile_record.notification_preferences != '{"email": true, "push": true, "in_app": true}' THEN
        completion_score := completion_score + 1;
    END IF;
    
    -- Premium membership (1 field)
    IF profile_record.is_premium_member = true THEN
        completion_score := completion_score + 1;
    END IF;
    
    RETURN (completion_score * 100) / total_fields;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update profile completion percentage
CREATE OR REPLACE FUNCTION update_profile_completion()
RETURNS TRIGGER AS $$
BEGIN
    NEW.profile_completion_percentage := calculate_profile_completion(NEW);
    NEW.updated_at := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profiles_completion 
    BEFORE INSERT OR UPDATE ON profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_profile_completion();

-- Create function to sync user data from Clerk webhook
CREATE OR REPLACE FUNCTION upsert_profile_from_clerk(
    p_clerk_user_id TEXT,
    p_email TEXT,
    p_clerk_username TEXT DEFAULT NULL,
    p_full_name TEXT DEFAULT NULL,
    p_avatar_url TEXT DEFAULT NULL,
    p_email_verified BOOLEAN DEFAULT false,
    p_external_accounts JSONB DEFAULT '{}',
    p_clerk_created_at TIMESTAMPTZ DEFAULT NULL,
    p_clerk_updated_at TIMESTAMPTZ DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
    result_id TEXT;
BEGIN
    INSERT INTO profiles (
        clerk_user_id,
        email,
        clerk_username,
        full_name,
        avatar_url,
        email_verified,
        external_accounts,
        clerk_created_at,
        clerk_updated_at,
        last_login_at
    ) VALUES (
        p_clerk_user_id,
        p_email,
        p_clerk_username,
        p_full_name,
        p_avatar_url,
        p_email_verified,
        p_external_accounts,
        COALESCE(p_clerk_created_at, NOW()),
        COALESCE(p_clerk_updated_at, NOW()),
        NOW()
    )
    ON CONFLICT (clerk_user_id) DO UPDATE SET
        email = EXCLUDED.email,
        clerk_username = EXCLUDED.clerk_username,
        full_name = COALESCE(EXCLUDED.full_name, profiles.full_name),
        avatar_url = COALESCE(EXCLUDED.avatar_url, profiles.avatar_url),
        email_verified = EXCLUDED.email_verified,
        external_accounts = EXCLUDED.external_accounts,
        clerk_updated_at = EXCLUDED.clerk_updated_at,
        last_login_at = NOW(),
        updated_at = NOW()
    RETURNING clerk_user_id INTO result_id;
    
    RETURN result_id;
END;
$$ LANGUAGE plpgsql; 