-- Migration: Simplify tools tables for MVP version
-- This migration simplifies the tools system to focus on core functionality

-- Drop existing user_tools table and recreate with simplified structure
DROP TABLE IF EXISTS user_tools CASCADE;

-- Create simplified user_tools table
CREATE TABLE IF NOT EXISTS user_tools (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL, -- Clerk user ID
    tool_id UUID NOT NULL,
    is_selected BOOLEAN DEFAULT true NOT NULL, -- Simplified from is_favorite
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Foreign key constraint
    CONSTRAINT fk_user_tools_tool_id 
        FOREIGN KEY (tool_id) 
        REFERENCES tools(id) 
        ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate user-tool relationships
    CONSTRAINT uk_user_tools_user_tool 
        UNIQUE (user_id, tool_id)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_tools_user_id ON user_tools(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tools_tool_id ON user_tools(tool_id);
CREATE INDEX IF NOT EXISTS idx_user_tools_selected ON user_tools(user_id, is_selected) WHERE is_selected = true;

-- Add check constraints for data validation
ALTER TABLE user_tools 
ADD CONSTRAINT chk_user_tools_user_id_not_empty 
    CHECK (LENGTH(TRIM(user_id)) > 0);

-- Add comments for documentation
COMMENT ON TABLE user_tools IS 'Simplified user tool selections for MVP version';
COMMENT ON COLUMN user_tools.user_id IS 'Clerk user ID who selected the tool';
COMMENT ON COLUMN user_tools.tool_id IS 'Reference to the selected tool';
COMMENT ON COLUMN user_tools.is_selected IS 'Whether user has selected this tool for their toolkit';

-- Create trigger function for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_user_tools_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at timestamps
CREATE TRIGGER update_user_tools_updated_at
    BEFORE UPDATE ON user_tools
    FOR EACH ROW
    EXECUTE FUNCTION update_user_tools_updated_at();

-- Update tools table to remove unnecessary sample data and keep only essential tools
DELETE FROM tools;

-- Insert essential creator tools for MVP
INSERT INTO tools (name, description, icon_url, website_url, category, platform, is_free) VALUES
-- Video editing tools
('DaVinci Resolve', '专业免费视频剪辑软件', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/davinci/davinci-original.svg', 'https://www.blackmagicdesign.com/products/davinciresolve', 'video', ARRAY['Windows', 'Mac'], true),
('CapCut', '简单易用的视频剪辑应用', 'https://lf16-capcut.faceulv.com/obj/capcutpc-packages-sg/capcut_pc_icon.ico', 'https://www.capcut.com/', 'video', ARRAY['Web', 'iOS', 'Android', 'Windows', 'Mac'], true),
('Adobe Premiere Pro', '专业视频编辑软件', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/premierepro/premierepro-original.svg', 'https://www.adobe.com/products/premiere.html', 'video', ARRAY['Windows', 'Mac'], false),

-- Design tools
('Canva', '在线设计平台', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/canva/canva-original.svg', 'https://www.canva.com/', 'design', ARRAY['Web', 'iOS', 'Android'], true),
('Figma', '协作式界面设计工具', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/figma/figma-original.svg', 'https://www.figma.com/', 'design', ARRAY['Web', 'Windows', 'Mac'], true),
('Adobe Photoshop', '专业图像编辑软件', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/photoshop/photoshop-original.svg', 'https://www.adobe.com/products/photoshop.html', 'image', ARRAY['Windows', 'Mac'], false),

-- AI tools
('ChatGPT', 'AI对话助手', 'https://upload.wikimedia.org/wikipedia/commons/0/04/ChatGPT_logo.svg', 'https://chat.openai.com/', 'ai', ARRAY['Web'], true),
('Midjourney', 'AI图像生成工具', 'https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons/svg/midjourney.svg', 'https://www.midjourney.com/', 'ai', ARRAY['Web'], false),

-- Productivity tools
('Notion', '全能工作空间', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/notion/notion-original.svg', 'https://www.notion.so/', 'productivity', ARRAY['Web', 'Windows', 'Mac', 'iOS', 'Android'], true),
('OBS Studio', '直播录制软件', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/obs/obs-original.svg', 'https://obsproject.com/', 'video', ARRAY['Windows', 'Mac'], true),

-- Analytics tools
('YouTube Analytics', 'YouTube数据分析', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/youtube/youtube-original.svg', 'https://studio.youtube.com/', 'analytics', ARRAY['Web'], true),
('Google Analytics', '网站分析工具', 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/google/google-original.svg', 'https://analytics.google.com/', 'analytics', ARRAY['Web'], true);
