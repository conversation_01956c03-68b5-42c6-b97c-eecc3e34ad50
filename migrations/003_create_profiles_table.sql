-- Migration: Create profiles table
-- This migration creates the profiles table for storing extended user information
-- Based on the schema defined in src/lib/schema.ts

-- Create profiles table (application-specific user data)
CREATE TABLE IF NOT EXISTS profiles (
    id TEXT PRIMARY KEY,
    full_name TEXT,
    avatar_url TEXT,
    role TEXT DEFAULT 'creator' NOT NULL,
    experience_points INTEGER DEFAULT 0 NOT NULL,
    level INTEGER DEFAULT 1 NOT NULL,
    wallet_address TEXT,
    is_premium_member BOOLEAN DEFAULT false NOT NULL,
    youtube_id TEXT,
    youtube_title TEXT,
    subscribers INTEGER DEFAULT 0 NOT NULL,
    views INTEGER DEFAULT 0 NOT NULL,
    motto TEXT,
    user_numeric_id INTEGER UNIQUE,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT fk_profiles_user_id 
        FOREIGN KEY (id) 
        REFERENCES users(id) 
        ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_user_numeric_id ON profiles(user_numeric_id);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_level ON profiles(level);
CREATE INDEX IF NOT EXISTS idx_profiles_youtube_id ON profiles(youtube_id);
CREATE INDEX IF NOT EXISTS idx_profiles_wallet_address ON profiles(wallet_address);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);

-- Create check constraints for data validation
ALTER TABLE profiles 
ADD CONSTRAINT chk_profiles_level_positive 
    CHECK (level >= 1);

ALTER TABLE profiles 
ADD CONSTRAINT chk_profiles_experience_non_negative 
    CHECK (experience_points >= 0);

ALTER TABLE profiles 
ADD CONSTRAINT chk_profiles_subscribers_non_negative 
    CHECK (subscribers >= 0);

ALTER TABLE profiles 
ADD CONSTRAINT chk_profiles_views_non_negative 
    CHECK (views >= 0);

ALTER TABLE profiles 
ADD CONSTRAINT chk_profiles_role_valid 
    CHECK (role IN ('creator', 'project_owner', 'admin', 'moderator'));

-- Add comments for documentation
COMMENT ON TABLE profiles IS 'Extended user profile information for CreatorCoin application';

COMMENT ON COLUMN profiles.id IS 'References users.id - one-to-one relationship with NextAuth users';
COMMENT ON COLUMN profiles.full_name IS 'User''s display name';
COMMENT ON COLUMN profiles.avatar_url IS 'URL to user''s profile picture';
COMMENT ON COLUMN profiles.role IS 'User role: creator, project_owner, admin, or moderator';
COMMENT ON COLUMN profiles.experience_points IS 'Total experience points earned by user';
COMMENT ON COLUMN profiles.level IS 'User level calculated from experience points';
COMMENT ON COLUMN profiles.wallet_address IS 'Cryptocurrency wallet address for payments';
COMMENT ON COLUMN profiles.is_premium_member IS 'Whether user has premium membership';
COMMENT ON COLUMN profiles.youtube_id IS 'Connected YouTube channel ID';
COMMENT ON COLUMN profiles.youtube_title IS 'Connected YouTube channel title';
COMMENT ON COLUMN profiles.subscribers IS 'YouTube subscriber count (cached)';
COMMENT ON COLUMN profiles.views IS 'YouTube total view count (cached)';
COMMENT ON COLUMN profiles.motto IS 'User''s personal motto or tagline';
COMMENT ON COLUMN profiles.user_numeric_id IS 'Unique 6-digit numeric ID for display purposes';

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 