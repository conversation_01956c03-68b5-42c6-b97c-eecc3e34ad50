{"name": "vgee-creator", "version": "0.1.0", "private": true, "packageManager": "yarn@1.22.22", "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.20.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.18.1", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@types/pg": "^8.15.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "d3": "^7.9.0", "framer-motion": "^12.9.1", "lucide-react": "^0.323.0", "next": "^15.3.3", "next-themes": "^0.2.1", "pg": "^8.16.0", "postgres": "^3.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-tsparticles": "^2.12.2", "sonner": "^1.4.0", "svix": "^1.66.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "tsparticles-slim": "^2.12.0", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/d3": "^7.4.3", "@types/node": "^20", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "^15.3.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}