"use client";

import React, { useState, useRef } from "react";
import { motion } from "framer-motion";
import {
  Edit3,
  Camera,
  Check,
  X,
  Upload,
  User,
  Calendar,
  Star,
  Youtube,
  Users,
  Eye,
  Video,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useProfile, type UserProfile } from "@/hooks/useProfile";
import { AvatarService } from "@/services/avatarService";

// Types
interface ProfileHeaderProps {
  userProfile?: UserProfile | null;
  onProfileUpdate?: (updates: Partial<UserProfile>) => void;
  className?: string;
  editable?: boolean;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  userProfile: externalProfile,
  onProfileUpdate,
  className = "",
  editable = true,
}) => {
  const { user } = useAuth();
  const { profile: internalProfile, updateProfile } = useProfile();

  // Use external profile if provided, otherwise use internal profile
  const userProfile = externalProfile || internalProfile;

  // Helper function to format numbers
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // State management
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [isEditingMotto, setIsEditingMotto] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [tempUsername, setTempUsername] = useState(
    userProfile?.full_name || ""
  );
  const [tempMotto, setTempMotto] = useState(userProfile?.motto || "");
  const [previewAvatar, setPreviewAvatar] = useState<string | null>(null);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const usernameInputRef = useRef<HTMLInputElement>(null);
  const mottoInputRef = useRef<HTMLInputElement>(null);

  // Helper functions


  const formatJoinDate = (date: Date | null): string => {
    if (!date) return "未知";
    try {
      return date
        .toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "2-digit",
        })
        .replace("/", "/");
    } catch {
      return "未知";
    }
  };

  const formatUserNumericId = (id: number | null): string => {
    if (!id) return "未分配";
    return `#${id.toString().padStart(8, "0")}`;
  };

  // Username editing handlers
  const handleStartEditUsername = () => {
    setIsEditingUsername(true);
    setTempUsername(userProfile?.full_name || "");
    setTimeout(() => usernameInputRef.current?.focus(), 100);
  };

  const handleSaveUsername = async () => {
    if (tempUsername.trim() && tempUsername !== userProfile?.full_name) {
      try {
        await updateProfile({ full_name: tempUsername.trim() });
        onProfileUpdate?.({
          ...userProfile,
          full_name: tempUsername.trim(),
        } as UserProfile);
      } catch (error) {
        console.error("Error updating username:", error);
      }
    }
    setIsEditingUsername(false);
  };

  const handleCancelEditUsername = () => {
    setTempUsername(userProfile?.full_name || "");
    setIsEditingUsername(false);
  };

  // Motto editing handlers
  const handleStartEditMotto = () => {
    setIsEditingMotto(true);
    setTempMotto(userProfile?.motto || "");
    setTimeout(() => mottoInputRef.current?.focus(), 100);
  };

  const handleSaveMotto = async () => {
    try {
      await updateProfile({ motto: tempMotto });
      onProfileUpdate?.({ ...userProfile, motto: tempMotto } as UserProfile);
    } catch (error) {
      console.error("Error updating motto:", error);
    }
    setIsEditingMotto(false);
  };

  const handleCancelEditMotto = () => {
    setTempMotto(userProfile?.motto || "");
    setIsEditingMotto(false);
  };

  // Avatar upload handlers
  const handleAvatarClick = () => {
    if (editable) {
      fileInputRef.current?.click();
    }
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    setIsUploadingAvatar(true);

    try {
      const avatarService = AvatarService.getInstance();

      // Generate thumbnail preview
      const thumbnail = await avatarService.generateThumbnail(file);
      setPreviewAvatar(thumbnail);

      // Resize image if needed
      const resizedFile = await avatarService.resizeImage(file, 400);

      // Upload the resized image
      const newAvatarUrl = await avatarService.uploadAvatar(
        resizedFile,
        user.id
      );

      // Update profile with new avatar URL
      await updateProfile({ avatar_url: newAvatarUrl });
      onProfileUpdate?.({
        ...userProfile,
        avatar_url: newAvatarUrl,
      } as UserProfile);
    } catch (error) {
      console.error("Error uploading avatar:", error);
      const errorMessage =
        error instanceof Error ? error.message : "头像上传失败，请重试";
      alert(errorMessage);
    } finally {
      setIsUploadingAvatar(false);
      setPreviewAvatar(null);
    }
  };

  // Keyboard handlers
  const handleUsernameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveUsername();
    } else if (e.key === "Escape") {
      handleCancelEditUsername();
    }
  };

  const handleMottoKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveMotto();
    } else if (e.key === "Escape") {
      handleCancelEditMotto();
    }
  };

  // Don't render if no profile data
  if (!userProfile) {
    return (
      <div className={`relative ${className}`}>
        <div className="relative bg-gradient-to-br from-[#1a1a2e] via-[#2a2a3e] to-[#1a1a2e] rounded-2xl p-8 shadow-xl overflow-hidden border border-white/10">
          <div className="text-center text-white/60">加载用户资料中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748] rounded-xl p-3 shadow-xl overflow-hidden border border-[#4ecdc4]/20 backdrop-blur-sm"
      >
        {/* Enhanced Background Effects - Hexagonal Honeycomb Pattern */}
        <div
          className="absolute inset-0 opacity-25"
          style={{
            backgroundImage: `
              radial-gradient(circle at 25% 25%, rgba(78,205,196,0.12) 2px, transparent 3px),
              radial-gradient(circle at 75% 25%, rgba(96,239,255,0.10) 2px, transparent 3px),
              radial-gradient(circle at 50% 75%, rgba(78,205,196,0.08) 2px, transparent 3px),
              radial-gradient(circle at 12.5% 50%, rgba(96,239,255,0.06) 1px, transparent 2px),
              radial-gradient(circle at 87.5% 50%, rgba(78,205,196,0.06) 1px, transparent 2px),
              radial-gradient(circle at 37.5% 12.5%, rgba(96,239,255,0.04) 1px, transparent 2px),
              radial-gradient(circle at 62.5% 87.5%, rgba(78,205,196,0.04) 1px, transparent 2px)
            `,
            backgroundSize: '40px 35px',
            backgroundPosition: '0 0, 20px 0, 10px 17.5px, 30px 17.5px, -10px 17.5px, 5px -8.75px, 15px 26.25px'
          }}
        />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(78,205,196,0.15)_0%,transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(96,239,255,0.1)_0%,transparent_40%)]" />

        {/* Animated Corner Accents */}
        <div className="absolute top-0 left-0 w-20 h-20 border-t-2 border-l-2 border-[#4ecdc4]/30 rounded-tl-2xl" />
        <div className="absolute top-0 right-0 w-20 h-20 border-t-2 border-r-2 border-[#60efff]/30 rounded-tr-2xl" />
        <div className="absolute bottom-0 left-0 w-20 h-20 border-b-2 border-l-2 border-[#60efff]/30 rounded-bl-2xl" />
        <div className="absolute bottom-0 right-0 w-20 h-20 border-b-2 border-r-2 border-[#4ecdc4]/30 rounded-br-2xl" />

        {/* New 6-Column Desktop Layout */}
        <div className="grid grid-cols-6 gap-4 items-center">
          {/* Column 1: Avatar */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 200, damping: 15 }}
            className="relative group flex-shrink-0"
          >
            <div
              className={`relative w-20 h-20 rounded-lg border-2 border-[#4ecdc4]/30 overflow-hidden shadow-lg shadow-[#4ecdc4]/20 ${
                editable ? "cursor-pointer" : ""
              }`}
              onClick={handleAvatarClick}
            >
              <img
                src={
                  previewAvatar ||
                  userProfile.avatar_url ||
                  "/default-avatar.png"
                }
                alt={userProfile.full_name || "用户头像"}
                className="w-full h-full object-cover transition-all duration-300 group-hover:scale-110"
              />

              {/* Upload Overlay */}
              {editable && (
                <div className="absolute inset-0 bg-gradient-to-br from-[#4ecdc4]/80 to-[#60efff]/80 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center backdrop-blur-sm">
                  {isUploadingAvatar ? (
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-white" />
                  ) : (
                    <Camera className="w-6 h-6 text-white drop-shadow-lg" />
                  )}
                </div>
              )}

              {/* Premium Badge */}
              {userProfile.is_premium_member && (
                <div className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-gradient-to-r from-[#FFD700] to-[#FFA500] flex items-center justify-center shadow-lg shadow-[#FFD700]/30 border border-white/20">
                  <Star className="w-2.5 h-2.5 text-white" />
                </div>
              )}
            </div>

            {/* Hidden File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
            />
          </motion.div>

          {/* Column 2: User Name & ID */}
          <div className="flex flex-col gap-1">
            {/* Top Row: Username */}
            {isEditingUsername ? (
              <div className="flex items-center gap-2">
                <input
                  ref={usernameInputRef}
                  type="text"
                  value={tempUsername}
                  onChange={(e) => setTempUsername(e.target.value)}
                  onKeyDown={handleUsernameKeyDown}
                  className="bg-[#2a2a3e]/50 text-white text-lg font-bold rounded-lg px-2 py-1 border border-[#4ecdc4]/30 focus:outline-none focus:border-[#4ecdc4] focus:ring-1 focus:ring-[#4ecdc4]/20 backdrop-blur-sm w-full"
                  maxLength={50}
                />
                <button
                  onClick={handleSaveUsername}
                  className="p-1 rounded-md bg-[#4ecdc4] text-white hover:bg-[#4ecdc4]/80 transition-all"
                >
                  <Check className="w-3 h-3" />
                </button>
                <button
                  onClick={handleCancelEditUsername}
                  className="p-1 rounded-md bg-white/10 text-white hover:bg-white/20 transition-all"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ) : (
              <div className="flex items-center gap-1 group">
                <h2 className="text-lg font-black text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.5)] bg-gradient-to-r from-white to-[#4ecdc4] bg-clip-text text-transparent">
                  {userProfile.full_name || "未設置用戶名"}
                </h2>
                {editable && (
                  <button
                    onClick={handleStartEditUsername}
                    className="opacity-0 group-hover:opacity-100 p-1 rounded-md bg-white/10 text-white hover:bg-white/20 transition-all"
                  >
                    <Edit3 className="w-3 h-3" />
                  </button>
                )}
                {userProfile.is_premium_member && (
                  <div className="flex items-center gap-1 text-[#FFD700] text-xs bg-[#FFD700]/10 rounded-md px-2 py-1 backdrop-blur-sm border border-[#FFD700]/20 ml-2">
                    <Star className="w-3 h-3" />
                    <span className="font-medium">Premium</span>
                  </div>
                )}
              </div>
            )}

            {/* Bottom Row: User ID */}
            <div className="flex items-center gap-1 text-[#4ecdc4]/80 text-sm">
              <User className="w-3 h-3" />
              <span className="font-medium">
                #{formatUserNumericId(userProfile.user_numeric_id)}
              </span>
            </div>
          </div>

          {/* Column 3: USDT Balance & Wallet */}
          <div className="flex flex-col gap-1">
            {/* Top Row: USDT Balance */}
            <div className="flex items-center gap-1">
              <span className="text-[#f39c12] text-sm">💰</span>
              <span className="text-white font-bold text-sm">
                {(Math.random() * 10000).toFixed(2)} USDT
              </span>
            </div>

            {/* Bottom Row: Wallet Address */}
            <div className="text-[#4ecdc4]/80 text-xs font-mono">
              {userProfile.wallet_address ? (
                `${userProfile.wallet_address.slice(0, 6)}......${userProfile.wallet_address.slice(-4)}`
              ) : (
                "0x312h......76f5"
              )}
            </div>
          </div>

          {/* Column 4: YouTube Subscribers */}
          <div className="flex flex-col items-center text-center">
            <div className="flex items-center gap-1 mb-1">
              <Users className="w-4 h-4 text-[#ff0000]" />
              <span className="text-white/60 text-xs">订阅数</span>
            </div>
            <span className="text-white font-bold text-sm">
              {formatNumber(userProfile.subscribers || 0)}
            </span>
          </div>

          {/* Column 5: YouTube Views */}
          <div className="flex flex-col items-center text-center">
            <div className="flex items-center gap-1 mb-1">
              <Eye className="w-4 h-4 text-[#ff0000]" />
              <span className="text-white/60 text-xs">播放量</span>
            </div>
            <span className="text-white font-bold text-sm">
              {formatNumber(userProfile.views || 0)}
            </span>
          </div>

          {/* Column 6: YouTube Videos */}
          <div className="flex flex-col items-center text-center">
            <div className="flex items-center gap-1 mb-1">
              <Video className="w-4 h-4 text-[#ff0000]" />
              <span className="text-white/60 text-xs">视频数</span>
            </div>
            <span className="text-white font-bold text-sm">
              {userProfile.youtube_id ? "156" : "0"}
            </span>
          </div>
        </div>





        {/* Join Date */}
        <div className="mt-3 text-center">
          <span className="text-white/60 text-xs">
            加入 {Math.floor((new Date().getTime() - new Date(userProfile.created_at).getTime()) / (1000 * 60 * 60 * 24))} 天
            {userProfile.last_login_at && (
              <> • 最後活躍: {new Date(userProfile.last_login_at).toLocaleDateString()}</>
            )}
          </span>
        </div>
      </motion.div>
    </div>
  );
};

export default ProfileHeader;
