# VGee Creator

A platform for VGee Creators built with Next.js 15 and modern web technologies.

## Getting Started

First, install the dependencies:

```bash
npm i -g yarn

yarn install
```

Then, create a `.env.local` file in the root directory with the required environment variables (see `env.example` for reference):

```env
# Neon Database
DATABASE_URL=your_neon_database_url

# YouTube API Configuration  
YOUTUBE_API_KEY=your_youtube_api_key
NEXT_PUBLIC_YOUTUBE_CLIENT_ID=your_youtube_client_id
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
```

Finally, run the development server:

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Features

- Authentication with <PERSON> (Google, GitHub, and more)
- Creator profiles and coin management
- YouTube integration
- Task management system
- Leaderboard functionality
- Dashboard analytics
- Protected routes
- Responsive design with dark mode support
- Modern UI components
- Database management with migrations

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Radix UI, Framer Motion
- **Authentication**: Clerk
- **Database**: Neon (PostgreSQL)
- **State Management**: Zustand, React Query (TanStack Query)
- **Charts**: Recharts, ECharts, D3.js
- **Forms**: React Hook Form, Zod validation
- **UI Components**: Radix UI primitives, Lucide React icons

## Project Structure

```
src/
├── app/                 # App router pages
│   ├── api/            # API routes
│   ├── auth/           # Authentication pages
│   ├── creator/        # Creator-specific pages
│   ├── dashboard/      # Dashboard pages
│   ├── leaderboard/    # Leaderboard pages
│   ├── oauth/          # OAuth handling
│   ├── profile/        # Profile pages
│   └── tasks/          # Task management pages
├── components/          # React components
├── contexts/           # React contexts
├── data/               # Static data and configurations
├── hooks/              # Custom hooks
├── lib/                # Utility functions
├── public/             # Static assets
├── services/           # API services
└── types/              # TypeScript type definitions
scripts/                # Database and utility scripts
migrations/             # Database migration files
```

## Available Scripts

- `dev` - Start development server
- `build` - Build for production
- `start` - Start production server
- `lint` - Run ESLint

## Learn More

To learn more about the technologies used in this project, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs)
- [Clerk Authentication](https://clerk.com/docs)
- [Neon Database](https://neon.tech/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Radix UI Documentation](https://www.radix-ui.com/docs)
- [TanStack Query](https://tanstack.com/query/latest)
- [Zustand State Management](https://github.com/pmndrs/zustand) 