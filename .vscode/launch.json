{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "env": {"NODE_OPTIONS": "--inspect"}}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "sourceMapPathOverrides": {"webpack://_N_E/*": "${webRoot}/*", "webpack:///./*": "${webRoot}/*", "webpack:///./~/*": "${webRoot}/node_modules/*", "webpack://?:*/*": "${webRoot}/*"}}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "serverReadyAction": {"action": "debugWithChrome", "pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "webRoot": "${workspaceFolder}"}, "env": {"NODE_OPTIONS": "--inspect"}}, {"name": "Debug Database Script", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/test-db-connection.js", "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "envFile": "${workspaceFolder}/.env.local"}, {"name": "Debug Migration <PERSON>t", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/run-migrations.js", "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "envFile": "${workspaceFolder}/.env.local"}], "compounds": [{"name": "Next.js: debug full stack (compound)", "configurations": ["Next.js: debug server-side", "Next.js: debug client-side"], "stopAll": true}]}