# VGee Creator 项目总览

## 🎯 项目简介

VGee Creator 是一个基于 Web3 的去中心化创作者平台，通过任务驱动和 USDT 代币激励机制，连接项目方与创作者，打造高效的内容创作与项目推广生态。

### 核心特性
- 🎬 **内容创作**: 短视频、图文、社区互动等多种内容形式
- 💰 **代币激励**: USDT 奖励机制，透明的收益分配
- 📈 **成长体系**: 经验值系统和等级提升机制
- 🛠️ **工具集成**: 丰富的创作工具和平台集成
- 🤝 **服务交易**: 创作者服务展示和交易平台

## 🏗️ 技术架构概览

### 前端技术栈
```
Next.js 15 (App Router)
├── React 19 + TypeScript
├── Tailwind CSS + Radix UI
├── Framer Motion (动画)
├── Zustand (状态管理)
├── TanStack Query (服务端状态)
└── D3.js (数据可视化)
```

### 后端技术栈
```
Next.js API Routes
├── Clerk (认证服务)
├── Neon PostgreSQL (数据库)
├── YouTube API (平台集成)
├── IPFS (去中心化存储)
└── Blockchain Networks (代币支付)
```

### 部署架构
```
AWS Amplify
├── CloudFront CDN
├── GitHub Actions (CI/CD)
├── Neon Database (云数据库)
└── Clerk Auth Service
```

## 📊 核心功能模块

### 1. 用户系统
- **认证**: Clerk 提供的安全认证
- **角色**: 创作者、项目方、管理员、版主
- **资料**: 完整的用户资料管理
- **成长**: 经验值和等级系统

### 2. 任务系统
- **普通任务**: 1-3 USDT 奖励，无平台抽成
- **悬赏任务**: 500-3000 USDT 奖励，30% 平台抽成
- **任务类型**: 短视频创作、项目推广、社区互动
- **审核机制**: 24-48 小时审核周期

### 3. 服务交易
- **服务管理**: 创作者可发布和管理服务
- **价格设置**: 灵活的价格区间设置
- **公开展示**: 创作者服务的公开展示页面
- **合作撮合**: 便于品牌方找到合适的创作者

### 4. 工具生态
- **工具集成**: 丰富的创作和营销工具
- **用户工具**: 个人工具配置和管理
- **平台支持**: YouTube、Twitter、Discord 等
- **API 集成**: 第三方服务无缝集成

### 5. 代币经济
- **USDT 支付**: 统一的代币结算体系
- **资产池**: 透明的资金池管理
- **多链支持**: ETH、BSC、Polygon 等
- **智能合约**: 自动化的奖励发放

## 📁 项目文件结构

```
vgee-creator/
├── 📄 配置文件
│   ├── package.json           # 依赖管理
│   ├── next.config.js         # Next.js 配置
│   ├── tailwind.config.ts     # Tailwind 配置
│   └── tsconfig.json          # TypeScript 配置
├── 📂 src/                    # 源代码目录
│   ├── app/                   # Next.js App Router
│   ├── components/            # React 组件
│   ├── lib/                   # 工具库
│   ├── hooks/                 # 自定义 Hooks
│   ├── services/              # API 服务
│   ├── types/                 # 类型定义
│   └── middleware.ts          # 认证中间件
├── 📂 docs/                   # 项目文档
│   ├── PRD/                   # 产品需求文档
│   └── *.md                   # 技术文档
├── 📂 migrations/             # 数据库迁移
├── 📂 scripts/                # 工具脚本
├── 📂 public/                 # 静态资源
└── 📂 .vscode/                # VS Code 配置
    ├── architecture.md        # 架构文档
    ├── technical-diagrams.md  # 技术架构图
    ├── development-guide.md   # 开发指南
    ├── api-reference.md       # API 参考
    └── project-overview.md    # 项目总览 (本文档)
```

## 🚀 快速开始

### 环境要求
- Node.js >= 20.0.0
- Yarn 1.22.22
- PostgreSQL (Neon)

### 安装步骤
```bash
# 1. 克隆项目
git clone https://github.com/wjllance/vgee-creator.git
cd vgee-creator

# 2. 安装依赖
npm i -g yarn
yarn install

# 3. 环境配置
cp env.example .env.local
# 编辑 .env.local 填入必要配置

# 4. 数据库设置
node scripts/test-db-connection.js
node scripts/run-migrations.js

# 5. 启动开发服务器
yarn dev
```

### 核心环境变量
```env
# 数据库
DATABASE_URL=postgresql://...

# 认证
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...

# YouTube API
YOUTUBE_API_KEY=...
NEXT_PUBLIC_YOUTUBE_CLIENT_ID=...
YOUTUBE_CLIENT_SECRET=...
```

## 🎨 设计系统

### 视觉风格
- **主色调**: 渐变紫色 (#4A148C → #311B92)
- **强调色**: 青色系 (#00CEC9)
- **辅助色**: YouTube 红 (#ff0000)
- **背景**: 半透明毛玻璃效果

### UI 组件
- **基础组件**: Radix UI primitives
- **图标**: Lucide React
- **动画**: Framer Motion
- **布局**: 响应式网格系统

### 交互设计
- **卡片式设计**: 圆角、阴影、悬浮效果
- **动画效果**: 页面过渡、按钮交互
- **响应式**: 移动端优先的设计理念

## 📈 数据模型

### 核心实体
```
User (用户)
├── Profile (资料)
├── Services (服务)
├── Tools (工具)
└── Tasks (任务)

Project (项目)
├── Tasks (任务)
├── Rewards (奖励)
└── Analytics (分析)
```

### 关键指标
- **用户增长**: 注册用户数、活跃用户数
- **任务完成**: 任务完成率、平均完成时间
- **收益分配**: USDT 发放总额、平均收益
- **平台健康**: 用户留存率、满意度评分

## 🔧 开发工具

### 代码质量
- **TypeScript**: 严格类型检查
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks

### 测试工具
- **Jest**: 单元测试框架
- **Testing Library**: React 组件测试
- **Playwright**: E2E 测试

### 开发工具
- **VS Code**: 推荐编辑器
- **GitHub**: 版本控制
- **Yarn**: 包管理器

## 🚀 部署流程

### 开发环境
```bash
yarn dev          # 启动开发服务器
yarn build        # 构建检查
yarn lint         # 代码检查
```

### 生产环境
```bash
yarn build        # 构建生产版本
yarn start        # 启动生产服务器
```

### CI/CD 流程
```
GitHub Push → GitHub Actions → 自动测试 → 构建部署 → AWS Amplify
```

## 📚 文档资源

### 项目文档
- **架构文档**: `.vscode/architecture.md`
- **开发指南**: `.vscode/development-guide.md`
- **API 参考**: `.vscode/api-reference.md`
- **技术架构图**: `.vscode/technical-diagrams.md`

### 业务文档
- **产品需求**: `docs/PRD.md`
- **功能实现**: `docs/PRD/服务报价模块实现总结.md`
- **用户同步**: `docs/USER_PROFILE_SYNC.md`
- **YouTube 集成**: `docs/YOUTUBE_SIMPLE_IMPLEMENTATION.md`

### 外部资源
- [Next.js 文档](https://nextjs.org/docs)
- [Clerk 认证](https://clerk.com/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Radix UI](https://www.radix-ui.com/docs)

## 🎯 发展路线图

### 第一阶段 (2025 Q2) - MVP
- ✅ 基础用户系统
- ✅ 服务管理模块
- ✅ 工具集成系统
- 🔄 普通任务系统
- 🔄 基础数据分析

### 第二阶段 (2025 Q3) - 增强
- 📋 超级创作者计划
- 📋 悬赏任务系统
- 📋 高级工具集成
- 📋 社区治理功能
- 📋 跨链支持

### 第三阶段 (2025 Q4+) - 扩展
- 📋 AI 创作助手
- 📋 元宇宙展示
- 📋 去中心化存储
- 📋 跨平台整合

## 🤝 贡献指南

### 开发流程
1. Fork 项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request
5. 代码审查和合并

### 提交规范
```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式
refactor: 代码重构
test: 测试相关
chore: 构建工具
```

---

*最后更新: 2025-07-26*
*项目版本: v0.1.0*
