# VGee Creator 技术架构图

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph "前端层 (Frontend)"
        A[Next.js 15 App Router]
        B[React 19 Components]
        C[Tailwind CSS + Radix UI]
        D[Framer Motion]
    end
    
    subgraph "状态管理层 (State Management)"
        E[Zustand Store]
        F[TanStack Query]
        G[React Context]
    end
    
    subgraph "API 层 (API Layer)"
        H[Next.js API Routes]
        I[RESTful APIs]
        J[Webhook Handlers]
    end
    
    subgraph "认证层 (Authentication)"
        K[Clerk Provider]
        L[JWT Middleware]
        M[Route Protection]
    end
    
    subgraph "数据层 (Data Layer)"
        N[PostgreSQL Database]
        O[Connection Pool]
        P[Migration Scripts]
    end
    
    subgraph "外部服务 (External Services)"
        Q[YouTube API]
        R[Clerk Auth Service]
        S[IPFS Storage]
        T[Blockchain Networks]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G
    
    A --> H
    H --> I
    H --> J
    
    A --> K
    K --> L
    L --> M
    
    I --> N
    N --> O
    N --> P
    
    H --> Q
    K --> R
    H --> S
    H --> T
```

## 🔄 数据流架构

```mermaid
sequenceDiagram
    participant U as User
    participant C as Client (React)
    participant M as Middleware
    participant A as API Routes
    participant D as Database
    participant E as External APIs
    
    U->>C: 用户操作
    C->>M: 请求认证
    M->>M: 验证 JWT Token
    M->>A: 转发请求
    A->>D: 数据库查询
    D->>A: 返回数据
    A->>E: 调用外部 API (可选)
    E->>A: 返回外部数据
    A->>C: 返回响应
    C->>U: 更新 UI
```

## 📦 组件架构

```mermaid
graph TD
    subgraph "页面层 (Pages)"
        P1[Dashboard Page]
        P2[Profile Page]
        P3[Services Page]
        P4[Creator Page]
    end
    
    subgraph "布局组件 (Layout Components)"
        L1[DashboardLayout]
        L2[ThreePanelLayout]
        L3[NavigationSidebar]
        L4[DashboardHeader]
    end
    
    subgraph "业务组件 (Business Components)"
        B1[ServicesManager]
        B2[ServicesList]
        B3[ServiceForm]
        B4[ProfileHeader]
        B5[TaskCard]
        B6[TokenBubbleMap]
    end
    
    subgraph "UI 组件 (UI Components)"
        U1[Button]
        U2[Card]
        U3[Dialog]
        U4[Form]
        U5[Toast]
        U6[Avatar]
    end
    
    P1 --> L1
    P2 --> L2
    P3 --> L1
    P4 --> L2
    
    L1 --> L3
    L1 --> L4
    L2 --> L3
    
    L1 --> B1
    L1 --> B5
    L2 --> B4
    L2 --> B6
    
    B1 --> B2
    B1 --> B3
    B2 --> U1
    B2 --> U2
    B3 --> U4
    B4 --> U6
    B5 --> U2
    
    U4 --> U1
    U4 --> U5
```

## 🗄️ 数据库关系图

```mermaid
erDiagram
    PROFILES {
        string clerk_user_id PK
        string email
        string clerk_username
        boolean email_verified
        json external_accounts
        datetime clerk_created_at
        datetime clerk_updated_at
        string full_name
        string avatar_url
        string motto
        enum role
        integer experience_points
        integer level
        boolean is_premium_member
        integer user_numeric_id
        string youtube_id
        string youtube_title
        integer subscribers
        integer views
        string wallet_address
        integer profile_completion_percentage
        datetime last_login_at
        string preferred_language
        json notification_preferences
        json social_links
        datetime created_at
        datetime updated_at
    }
    
    SERVICES {
        uuid id PK
        string clerk_user_id FK
        enum type
        string title
        text description
        integer min_price
        integer max_price
        enum status
        datetime created_at
        datetime updated_at
    }
    
    TOOLS {
        uuid id PK
        string name
        text description
        enum category
        enum platform
        string icon_url
        string website_url
        json features
        boolean is_featured
        datetime created_at
        datetime updated_at
    }
    
    USER_TOOLS {
        uuid id PK
        string clerk_user_id FK
        uuid tool_id FK
        enum status
        json configuration
        datetime added_at
        datetime last_used_at
    }
    
    PROFILES ||--o{ SERVICES : "creates"
    PROFILES ||--o{ USER_TOOLS : "uses"
    TOOLS ||--o{ USER_TOOLS : "belongs_to"
```

## 🔐 认证流程图

```mermaid
flowchart TD
    A[用户访问] --> B{是否已登录?}
    B -->|否| C[重定向到 /auth]
    B -->|是| D[检查路由权限]
    
    C --> E[Clerk 登录页面]
    E --> F[用户输入凭据]
    F --> G[Clerk 验证]
    G -->|成功| H[生成 JWT Token]
    G -->|失败| E
    
    H --> I[设置 Cookie]
    I --> J[重定向到目标页面]
    
    D --> K{路由是否受保护?}
    K -->|否| L[允许访问]
    K -->|是| M[验证 JWT Token]
    
    M -->|有效| N[检查用户角色]
    M -->|无效| C
    
    N -->|有权限| L
    N -->|无权限| O[403 错误页面]
```

## 📱 响应式设计架构

```mermaid
graph LR
    subgraph "移动端 (Mobile)"
        M1[单列布局]
        M2[底部导航]
        M3[全屏模态]
    end
    
    subgraph "平板端 (Tablet)"
        T1[双列布局]
        T2[侧边导航]
        T3[抽屉模态]
    end
    
    subgraph "桌面端 (Desktop)"
        D1[三列布局]
        D2[固定侧边栏]
        D3[浮层模态]
    end
    
    subgraph "Tailwind 断点"
        BP1[sm: 640px]
        BP2[md: 768px]
        BP3[lg: 1024px]
        BP4[xl: 1280px]
    end
    
    BP1 --> M1
    BP1 --> M2
    BP1 --> M3
    
    BP2 --> T1
    BP2 --> T2
    BP2 --> T3
    
    BP3 --> D1
    BP3 --> D2
    BP3 --> D3
```

## 🚀 部署架构

```mermaid
graph TB
    subgraph "开发环境 (Development)"
        DEV1[本地开发服务器]
        DEV2[本地数据库]
        DEV3[Mock 外部服务]
    end
    
    subgraph "生产环境 (Production)"
        PROD1[AWS Amplify]
        PROD2[CloudFront CDN]
        PROD3[Neon PostgreSQL]
        PROD4[Clerk 认证服务]
        PROD5[IPFS 存储]
    end
    
    subgraph "CI/CD 流程"
        CI1[GitHub Actions]
        CI2[自动测试]
        CI3[构建部署]
        CI4[健康检查]
    end
    
    DEV1 --> CI1
    CI1 --> CI2
    CI2 --> CI3
    CI3 --> PROD1
    
    PROD1 --> PROD2
    PROD1 --> PROD3
    PROD1 --> PROD4
    PROD1 --> PROD5
    
    CI3 --> CI4
```

## 🔧 开发工具链

```mermaid
mindmap
  root((开发工具链))
    代码编辑
      VS Code
      TypeScript
      ESLint
      Prettier
    版本控制
      Git
      GitHub
      Conventional Commits
    包管理
      Yarn
      Node.js 20+
    构建工具
      Next.js
      Webpack
      SWC
    测试工具
      Jest
      Testing Library
      Playwright
    部署工具
      AWS Amplify
      GitHub Actions
      Docker
```

---

*最后更新: 2025-07-26*
