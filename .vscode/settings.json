{
    // VGee Creator 项目配置
    "workbench.colorCustomizations": {
      "activityBar.activeBackground": "#99ad9f",
      "activityBar.background": "#99ad9f",
      "activityBar.foreground": "#15202b",
      "activityBar.inactiveForeground": "#15202b99",
      "activityBarBadge.background": "#7958c7",
      "activityBarBadge.foreground": "#e7e7e7",
      "commandCenter.border": "#15202b99",
      "sash.hoverBorder": "#99ad9f",
      "statusBar.background": "#7d9685",
      "statusBar.foreground": "#15202b",
      "statusBarItem.hoverBackground": "#647c6c",
      "statusBarItem.remoteBackground": "#7d9685",
      "statusBarItem.remoteForeground": "#15202b",
      "titleBar.activeBackground": "#7d9685",
      "titleBar.activeForeground": "#15202b",
      "titleBar.inactiveBackground": "#7d968599",
      "titleBar.inactiveForeground": "#15202b99"
    },
    "peacock.color": "#7d9685",

    // TypeScript 配置
    "typescript.preferences.importModuleSpecifier": "relative",
    "typescript.suggest.autoImports": true,
    "typescript.updateImportsOnFileMove.enabled": "always",

    // ESLint 配置
    "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
    "eslint.format.enable": true,

    // Prettier 配置
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },

    // 文件关联
    "files.associations": {
        "*.css": "tailwindcss",
        "*.md": "markdown"
    },

    // Tailwind CSS 配置
    "tailwindCSS.includeLanguages": {
        "typescript": "javascript",
        "typescriptreact": "javascript"
    },
    "tailwindCSS.experimental.classRegex": [
        ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
        ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
    ],

    // 编辑器配置
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.wordWrap": "on",
    "editor.minimap.enabled": true,
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": true,

    // 文件浏览器配置
    "explorer.fileNesting.enabled": true,
    "explorer.fileNesting.patterns": {
        "*.ts": "${capture}.js",
        "*.tsx": "${capture}.js",
        "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml",
        "tailwind.config.*": "tailwind.config.*",
        "tsconfig.json": "tsconfig.*.json",
        "next.config.*": "next-env.d.ts"
    },

    // Git 配置
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,

    // 搜索配置
    "search.exclude": {
        "**/node_modules": true,
        "**/dist": true,
        "**/.next": true,
        "**/coverage": true,
        "**/.nyc_output": true
    },

    // 推荐扩展
    "extensions.recommendations": [
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-typescript-next",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",
        "ms-vscode.vscode-json",
        "yzhang.markdown-all-in-one",
        "bierner.markdown-mermaid",
        "johnpapa.vscode-peacock"
    ]
}