# BountyGo 快速开始指南

## 🚀 3-4 小时完成 MVP

### 📋 开发流程

#### 第1步: 环境配置 (30分钟)
1. **配置第三方服务** 👉 [环境配置指南](./bountygo-setup-guide.md)
   - Neon Database (数据库)
   - Clerk Authentication (用户认证)
   - Vercel (部署)
   - GitHub (代码托管)

#### 第2步: 项目初始化 (15分钟)
```bash
# 创建 Next.js 项目
npx create-next-app@latest bountygo --typescript --tailwind --app

# 安装依赖
cd bountygo
npm install @clerk/nextjs postgres

# 配置环境变量
cp .env.example .env.local
# 填入第1步获得的配置信息
```

#### 第3步: 数据库设置 (15分钟)
```bash
# 创建数据库初始化脚本
# 参考: bountygo-mvp-guide.md 中的数据库设计

# 运行初始化
node scripts/setup-bountygo-db.js

# 测试连接
node scripts/test-db-connection.js
```

#### 第4步: 后端 API 开发 (90分钟)
实现 7 个核心 API 接口：

1. **GET /api/bounties** - 获取任务列表
2. **POST /api/bounties** - 创建任务
3. **GET /api/bounties/[id]** - 任务详情
4. **POST /api/user-bounties** - 加入任务
5. **GET /api/user-bounties** - 我的任务
6. **GET /api/profile** - 用户资料
7. **POST /api/ai/parse** - AI 解析链接

详细代码参考: [MVP 开发指南](./bountygo-mvp-guide.md)

#### 第5步: 前端测试页面 (60分钟)
创建简单的 API 测试页面，确保所有接口都能正常工作：

```typescript
// app/test/page.tsx
// 创建按钮测试每个 API 接口
// 显示返回结果，验证业务逻辑
```

**重点**: 先确保后端逻辑完全正确，再考虑前端美化。

#### 第6步: 前端美化 (60分钟)
API 测试通过后，创建正式的用户界面：
- 任务列表页面
- 创建任务表单
- 我的任务页面
- 基础样式和布局

#### 第7步: 部署上线 (30分钟)
```bash
# 推送到 GitHub
git add .
git commit -m "BountyGo MVP"
git push origin main

# Vercel 自动部署
# 配置生产环境变量
# 测试生产环境功能
```

## 🎯 MVP 成功标准

### 核心功能验证
- [ ] 用户可以注册登录
- [ ] 用户可以发布任务
- [ ] 用户可以浏览任务
- [ ] 用户可以加入任务
- [ ] AI 可以解析链接
- [ ] 用户可以查看自己的任务

### 技术指标
- [ ] 所有 API 接口正常工作
- [ ] 数据库读写正常
- [ ] 用户认证流程完整
- [ ] 生产环境部署成功

## 🔧 关键文件清单

### 必需的配置文件
```
bountygo/
├── .env.local              # 环境变量配置
├── package.json            # 依赖管理
├── next.config.js          # Next.js 配置
└── scripts/
    ├── setup-bountygo-db.js    # 数据库初始化
    └── test-db-connection.js   # 连接测试
```

### 核心代码文件
```
app/
├── api/                    # 后端 API
│   ├── bounties/
│   ├── user-bounties/
│   ├── profile/
│   └── ai/parse/
├── test/                   # API 测试页面
│   └── page.tsx
└── dashboard/              # 正式前端页面
    └── page.tsx
```

### 工具库文件
```
lib/
├── db.ts                   # 数据库连接
├── utils.ts                # 工具函数
└── validations.ts          # 数据验证
```

## 🐛 常见问题

### 数据库连接失败
- 检查 DATABASE_URL 是否正确
- 确保包含 `?sslmode=require` 参数
- 验证 Neon 数据库状态

### Clerk 认证问题
- 检查 API 密钥是否正确
- 确认域名配置 (localhost:3000)
- 验证环境变量名称

### API 接口报错
- 检查请求格式是否正确
- 验证数据库表结构
- 查看服务器日志错误信息

### 部署失败
- 确认环境变量已配置
- 检查构建日志错误
- 验证依赖安装正确

## 📚 详细文档

- **[环境配置指南](./bountygo-setup-guide.md)** - 第三方服务配置
- **[MVP 开发指南](./bountygo-mvp-guide.md)** - 完整开发教程
- **[项目模板](./bountygo-project-template.md)** - 项目结构参考

## 🎉 完成后的成果

MVP 完成后，你将拥有：

- ✅ **可用的产品**: 用户可以发布和参与赏金任务
- ✅ **完整的后端**: 7 个 API 接口支持所有核心功能
- ✅ **简洁的前端**: 基础但完整的用户界面
- ✅ **在线部署**: 可以分享给用户测试的网址
- ✅ **扩展基础**: 为后续功能迭代打好基础

现在就开始你的 BountyGo 开发之旅吧！

---

*专注实用性，快速验证产品概念*
