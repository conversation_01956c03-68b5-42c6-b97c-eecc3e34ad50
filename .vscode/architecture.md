# VGee Creator 项目架构文档

## 📋 项目概述

VGee Creator 是一个基于 Web3 的去中心化创作者平台，通过任务驱动和代币激励机制，连接项目方与创作者，打造高效的内容创作与项目推广生态。

### 核心价值
- 为项目方提供高效的内容营销和社区建设解决方案
- 为创作者提供稳定的收入来源和成长路径
- 通过 USDT 代币经济模型实现多方共赢

## 🏗️ 技术架构

### 技术栈
- **前端框架**: Next.js 15 (App Router)
- **UI 框架**: React 19 + TypeScript
- **样式系统**: Tailwind CSS + Radix UI + Framer Motion
- **认证系统**: Clerk Authentication
- **数据库**: Neon PostgreSQL
- **状态管理**: Zustand + TanStack Query (React Query)
- **数据可视化**: D3.js + Recharts + ECharts
- **包管理**: Yarn (v1.22.22)
- **部署**: AWS Amplify + CloudFront + IPFS

### 架构模式
- **前端**: 基于 Next.js App Router 的全栈应用
- **后端**: API Routes + Server Components
- **数据库**: PostgreSQL with connection pooling
- **认证**: JWT-based authentication via Clerk
- **状态管理**: Client-side state (Zustand) + Server state (React Query)

## 📁 项目结构

```
vgee-creator/
├── src/
│   ├── app/                    # Next.js App Router 页面
│   │   ├── api/               # API 路由
│   │   │   ├── creator/       # 创作者相关 API
│   │   │   ├── services/      # 服务管理 API
│   │   │   ├── tools/         # 工具管理 API
│   │   │   ├── profile/       # 用户资料 API
│   │   │   ├── public/        # 公开 API (无需认证)
│   │   │   └── webhooks/      # Webhook 处理
│   │   ├── auth/              # 认证页面
│   │   ├── dashboard/         # 仪表板页面
│   │   ├── creator/           # 创作者页面
│   │   ├── profile/           # 用户资料页面
│   │   ├── services/          # 服务管理页面
│   │   ├── tasks/             # 任务管理页面
│   │   └── leaderboard/       # 排行榜页面
│   ├── components/            # React 组件
│   │   ├── ui/               # 基础 UI 组件 (Radix UI)
│   │   ├── dashboard/        # 仪表板组件
│   │   ├── services/         # 服务管理组件
│   │   ├── tools/            # 工具组件
│   │   ├── content/          # 内容组件
│   │   ├── progress/         # 进度组件
│   │   └── providers/        # Context Providers
│   ├── contexts/             # React Contexts
│   │   ├── AuthContext.tsx   # 认证上下文
│   │   └── LayoutContext.tsx # 布局上下文
│   ├── hooks/                # 自定义 Hooks
│   │   ├── useAuth.ts        # 认证 Hook
│   │   ├── useProfile.ts     # 用户资料 Hook
│   │   └── useToast.ts       # 通知 Hook
│   ├── lib/                  # 工具库
│   │   ├── database/         # 数据库操作
│   │   ├── db.ts            # 数据库连接
│   │   ├── schema.ts        # 数据库模式
│   │   └── utils.ts         # 工具函数
│   ├── services/             # API 服务层
│   │   ├── servicesApi.ts   # 服务 API
│   │   ├── profileService.ts # 用户资料服务
│   │   └── avatarService.ts  # 头像服务
│   ├── stores/               # Zustand 状态管理
│   │   └── profileSyncStore.ts
│   ├── types/                # TypeScript 类型定义
│   │   ├── index.ts         # 通用类型
│   │   ├── services.ts      # 服务类型
│   │   ├── tools.ts         # 工具类型
│   │   └── dashboard.ts     # 仪表板类型
│   ├── constants/            # 常量定义
│   └── middleware.ts         # Next.js 中间件
├── migrations/               # 数据库迁移文件
├── scripts/                  # 工具脚本
├── docs/                     # 项目文档
│   ├── PRD/                 # 产品需求文档
│   └── *.md                 # 技术文档
├── public/                   # 静态资源
└── 配置文件
    ├── package.json         # 依赖管理
    ├── next.config.js       # Next.js 配置
    ├── tailwind.config.ts   # Tailwind 配置
    └── tsconfig.json        # TypeScript 配置
```

## 🔐 认证与权限

### 认证系统 (Clerk)
- **认证提供商**: Clerk
- **认证方式**: JWT Token
- **保护路由**: 通过 middleware.ts 实现
- **公开路由**: `/`, `/auth/*`
- **受保护路由**: 所有其他路由需要认证

### 用户角色
- **creator**: 普通创作者
- **project_owner**: 项目方
- **admin**: 管理员
- **moderator**: 版主

## 💾 数据库设计

### 核心表结构
- **profiles**: 用户资料表 (基于 Clerk 用户)
- **services**: 服务与报价表
- **tools**: 工具表
- **user_tools**: 用户工具关联表

### 数据库特性
- **连接池**: PostgreSQL connection pooling
- **SSL**: 生产环境强制 SSL
- **迁移**: SQL 迁移文件管理
- **健康检查**: 连接状态监控

## 🎨 UI/UX 设计系统

### 视觉风格
- **主色调**: 渐变紫色 (#4A148C 到 #311B92)
- **强调色**: 青色系 (#00CEC9)
- **辅助色**: YouTube 红 (#ff0000)
- **背景**: 半透明效果 (backdrop-blur)

### 组件系统
- **基础组件**: Radix UI primitives
- **图标**: Lucide React
- **动画**: Framer Motion
- **样式**: Tailwind CSS + CSS-in-JS

## 🔄 状态管理

### 客户端状态 (Zustand)
- **全局状态**: 用户资料同步状态
- **轻量级**: 简单的状态管理
- **TypeScript**: 完整类型支持

### 服务端状态 (TanStack Query)
- **数据获取**: API 数据缓存和同步
- **缓存策略**: 智能缓存和重新验证
- **错误处理**: 统一错误处理机制

## 📡 API 设计

### RESTful API
- **认证 API**: `/api/profile/*`
- **服务 API**: `/api/services/*`
- **工具 API**: `/api/tools/*`
- **公开 API**: `/api/public/*`

### API 特性
- **类型安全**: 完整的 TypeScript 类型
- **错误处理**: 统一的错误响应格式
- **认证**: Clerk 中间件保护
- **验证**: 输入数据验证

## 🚀 部署与运维

### 环境配置
- **开发环境**: `yarn dev`
- **构建**: `yarn build`
- **生产环境**: `yarn start`
- **代码检查**: `yarn lint`

### 环境变量
- **数据库**: `DATABASE_URL`
- **认证**: `CLERK_*` 变量
- **YouTube API**: `YOUTUBE_*` 变量

### 性能优化
- **代码分割**: Next.js 自动代码分割
- **图片优化**: Next.js Image 组件
- **缓存策略**: React Query 缓存
- **懒加载**: 组件懒加载

## 📈 核心功能模块

### 1. 任务系统
- **普通任务**: 1-3 USDT 奖励
- **悬赏任务**: 500-3000 USDT 奖励
- **经验值系统**: XP 积累和等级提升

### 2. 服务与报价模块
- **服务管理**: CRUD 操作
- **价格设置**: 灵活的价格区间
- **公开展示**: 创作者服务展示页

### 3. 创作者成长体系
- **等级系统**: 10 级成长体系
- **会员特权**: 基础/高级会员
- **成就系统**: 特殊成就奖励

### 4. 代币资产池
- **多币种支持**: USDT/USDC/ETH/MATIC/BNB
- **实时汇率**: 多币种价值转换
- **资金流向**: 详细的资金流向记录

## 🔧 开发工具与规范

### 代码质量
- **TypeScript**: 严格类型检查
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks

### 测试策略
- **单元测试**: 组件和函数测试
- **集成测试**: API 接口测试
- **E2E 测试**: 用户流程测试

### 版本控制
- **Git Flow**: 功能分支开发
- **Conventional Commits**: 规范化提交信息
- **Semantic Versioning**: 语义化版本管理

## 📚 文档与资源

### 项目文档
- **架构文档**: `.vscode/architecture.md` (本文档)
- **技术架构图**: `.vscode/technical-diagrams.md`
- **开发指南**: `.vscode/development-guide.md`
- **API 参考**: `.vscode/api-reference.md`

### 业务文档
- **产品需求**: `/docs/PRD.md`
- **API 文档**: `/docs/PRD/API-REFERENCE.md`
- **实现总结**: `/docs/PRD/服务报价模块实现总结.md`
- **用户资料同步**: `/docs/USER_PROFILE_SYNC.md`
- **YouTube 集成**: `/docs/YOUTUBE_SIMPLE_IMPLEMENTATION.md`

### 学习资源
- [Next.js 文档](https://nextjs.org/docs)
- [Clerk 认证](https://clerk.com/docs)
- [Neon 数据库](https://neon.tech/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Radix UI](https://www.radix-ui.com/docs)
- [TanStack Query](https://tanstack.com/query/latest)

---

*最后更新: 2025-07-26*
