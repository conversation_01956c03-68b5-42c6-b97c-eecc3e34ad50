{"version": "2.0.0", "tasks": [{"label": "dev", "type": "shell", "command": "yarn", "args": ["dev"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "启动 Next.js 开发服务器"}, {"label": "build", "type": "shell", "command": "yarn", "args": ["build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$tsc"], "detail": "构建生产版本"}, {"label": "start", "type": "shell", "command": "yarn", "args": ["start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "启动生产服务器"}, {"label": "lint", "type": "shell", "command": "yarn", "args": ["lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$eslint-stylish"], "detail": "运行 ESLint 代码检查"}, {"label": "type-check", "type": "shell", "command": "npx", "args": ["tsc", "--noEmit"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$tsc"], "detail": "运行 TypeScript 类型检查"}, {"label": "test-db", "type": "shell", "command": "node", "args": ["scripts/test-db-connection.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "测试数据库连接"}, {"label": "migrate", "type": "shell", "command": "node", "args": ["scripts/run-migrations.js"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "运行数据库迁移"}, {"label": "check-tables", "type": "shell", "command": "node", "args": ["scripts/check-table-structure.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "检查数据库表结构"}, {"label": "clean", "type": "shell", "command": "rm", "args": ["-rf", ".next", "node_modules/.cache"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "清理构建缓存"}, {"label": "install", "type": "shell", "command": "yarn", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "安装项目依赖"}, {"label": "setup", "dependsOrder": "sequence", "dependsOn": ["install", "test-db", "migrate"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "detail": "完整项目设置 (安装依赖 + 数据库设置)"}]}