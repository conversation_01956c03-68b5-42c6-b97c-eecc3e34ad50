# BountyGo MVP 开发指南

## 🎯 项目概述

快速构建一个赏金任务平台 MVP，3-4 小时内完成可用产品。

### ⚠️ 开始前必读
**在开始开发之前，请先完成环境配置：**
👉 **[环境配置指南](./bountygo-setup-guide.md)**

需要配置的服务：
- 🗄️ **Neon Database** (免费 PostgreSQL 数据库)
- 🔐 **Clerk** (免费用户认证服务)
- 🚀 **Vercel** (免费部署平台)
- 📁 **GitHub** (代码托管)

### MVP 功能范围
- ✅ 用户认证登录
- ✅ 发布赏金任务
- ✅ 浏览任务列表
- ✅ 加入/退出任务
- ✅ AI 解析链接内容
- ✅ 个人任务管理

### 开发时间预估
- **数据库设置**: 30 分钟
- **后端 API**: 90 分钟
- **前端测试页**: 60 分钟
- **前端美化**: 60 分钟
- **测试部署**: 30 分钟

## 🗄️ 极简数据库设计

### 只需要 3 张表

#### 1. 用户表 (profiles)
```sql
CREATE TABLE profiles (
  id TEXT PRIMARY KEY,              -- Clerk用户ID
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. 赏金任务表 (bounties)
```sql
CREATE TABLE bounties (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  reward_amount NUMERIC(10,2) NOT NULL,
  reward_currency TEXT DEFAULT 'USDT',
  deadline TIMESTAMP,
  source_url TEXT,                  -- 原始链接 (Twitter/GitHub等)
  sponsor_id TEXT REFERENCES profiles(id),
  status TEXT DEFAULT 'active',     -- active, completed, cancelled
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 3. 用户任务关联表 (user_bounties)
```sql
CREATE TABLE user_bounties (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT REFERENCES profiles(id),
  bounty_id UUID REFERENCES bounties(id),
  status TEXT DEFAULT 'joined',     -- joined, completed, cancelled
  joined_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP,
  UNIQUE(user_id, bounty_id)
);
```

### 数据库初始化脚本
```javascript
// scripts/setup-bountygo-db.js
const postgres = require('postgres');

const sql = postgres(process.env.DATABASE_URL);

async function setupDatabase() {
  try {
    console.log('🚀 开始设置 BountyGo 数据库...');
    
    // 启用 UUID 扩展
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    // 创建用户表
    await sql`
      CREATE TABLE IF NOT EXISTS profiles (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        name TEXT,
        avatar_url TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `;
    
    // 创建赏金任务表
    await sql`
      CREATE TABLE IF NOT EXISTS bounties (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title TEXT NOT NULL,
        description TEXT,
        reward_amount NUMERIC(10,2) NOT NULL,
        reward_currency TEXT DEFAULT 'USDT',
        deadline TIMESTAMP,
        source_url TEXT,
        sponsor_id TEXT REFERENCES profiles(id),
        status TEXT DEFAULT 'active',
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `;
    
    // 创建用户任务关联表
    await sql`
      CREATE TABLE IF NOT EXISTS user_bounties (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id TEXT REFERENCES profiles(id),
        bounty_id UUID REFERENCES bounties(id),
        status TEXT DEFAULT 'joined',
        joined_at TIMESTAMP DEFAULT NOW(),
        completed_at TIMESTAMP,
        UNIQUE(user_id, bounty_id)
      )
    `;
    
    // 创建索引
    await sql`CREATE INDEX IF NOT EXISTS idx_bounties_status ON bounties(status)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_bounties_sponsor ON bounties(sponsor_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_user_bounties_user ON user_bounties(user_id)`;
    
    console.log('✅ BountyGo 数据库设置完成!');
    
    // 插入测试数据
    await insertTestData();
    
  } catch (error) {
    console.error('❌ 数据库设置失败:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
}

async function insertTestData() {
  console.log('📝 插入测试数据...');
  
  // 插入测试用户
  await sql`
    INSERT INTO profiles (id, email, name) 
    VALUES ('test_user_1', '<EMAIL>', 'Test User')
    ON CONFLICT (id) DO NOTHING
  `;
  
  // 插入测试任务
  await sql`
    INSERT INTO bounties (title, description, reward_amount, deadline, sponsor_id)
    VALUES (
      '测试赏金任务',
      '这是一个测试任务，用于验证系统功能',
      100.00,
      NOW() + INTERVAL '7 days',
      'test_user_1'
    )
    ON CONFLICT DO NOTHING
  `;
  
  console.log('✅ 测试数据插入完成');
}

setupDatabase();
```

## 🔧 极简后端 API

### 技术栈选择
- **框架**: Next.js 15 (App Router)
- **认证**: Clerk (稳定可靠)
- **数据库**: Neon PostgreSQL (云原生，免运维)
- **ORM**: 原生 SQL (postgres.js，性能最优)
- **部署**: Vercel (一键部署)

### 1. 任务管理 API (4个核心接口)

#### GET /api/bounties - 获取任务列表
```typescript
// app/api/bounties/route.ts
import { NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'active';
    
    const bounties = await db`
      SELECT 
        b.id, b.title, b.description, b.reward_amount, b.reward_currency,
        b.deadline, b.status, b.source_url, b.created_at,
        p.name as sponsor_name, p.avatar_url as sponsor_avatar
      FROM bounties b
      LEFT JOIN profiles p ON b.sponsor_id = p.id
      WHERE b.status = ${status}
      ORDER BY b.created_at DESC
      LIMIT 50
    `;
    
    return NextResponse.json({
      success: true,
      data: bounties
    });
  } catch (error) {
    console.error('获取任务列表失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch bounties' },
      { status: 500 }
    );
  }
}
```

#### POST /api/bounties - 发布任务
```typescript
// app/api/bounties/route.ts (继续)
import { auth } from '@clerk/nextjs/server';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { title, description, reward_amount, deadline, source_url } = body;

    // 数据验证
    if (!title || !reward_amount) {
      return NextResponse.json(
        { success: false, error: 'Title and reward amount are required' },
        { status: 400 }
      );
    }

    // 确保用户存在
    await db`
      INSERT INTO profiles (id, email, name)
      SELECT ${userId}, '', ''
      WHERE NOT EXISTS (SELECT 1 FROM profiles WHERE id = ${userId})
    `;

    const [bounty] = await db`
      INSERT INTO bounties (
        title, description, reward_amount, deadline, source_url, sponsor_id
      ) VALUES (
        ${title}, ${description}, ${reward_amount}, 
        ${deadline ? new Date(deadline) : null}, ${source_url}, ${userId}
      )
      RETURNING *
    `;

    return NextResponse.json({
      success: true,
      data: bounty
    });
  } catch (error) {
    console.error('创建任务失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create bounty' },
      { status: 500 }
    );
  }
}
```

#### GET /api/bounties/[id] - 任务详情
```typescript
// app/api/bounties/[id]/route.ts
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const [bounty] = await db`
      SELECT 
        b.*, 
        p.name as sponsor_name, 
        p.avatar_url as sponsor_avatar,
        COUNT(ub.id) as participants_count
      FROM bounties b
      LEFT JOIN profiles p ON b.sponsor_id = p.id
      LEFT JOIN user_bounties ub ON b.id = ub.bounty_id AND ub.status = 'joined'
      WHERE b.id = ${params.id}
      GROUP BY b.id, p.name, p.avatar_url
    `;

    if (!bounty) {
      return NextResponse.json(
        { success: false, error: 'Bounty not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: bounty
    });
  } catch (error) {
    console.error('获取任务详情失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch bounty details' },
      { status: 500 }
    );
  }
}
```

#### POST /api/user-bounties - 加入任务
```typescript
// app/api/user-bounties/route.ts
export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { bounty_id } = await request.json();

    // 检查任务是否存在且为活跃状态
    const [bounty] = await db`
      SELECT id, status FROM bounties WHERE id = ${bounty_id}
    `;

    if (!bounty || bounty.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'Bounty not available' },
        { status: 400 }
      );
    }

    // 确保用户存在
    await db`
      INSERT INTO profiles (id, email, name)
      SELECT ${userId}, '', ''
      WHERE NOT EXISTS (SELECT 1 FROM profiles WHERE id = ${userId})
    `;

    const [userBounty] = await db`
      INSERT INTO user_bounties (user_id, bounty_id)
      VALUES (${userId}, ${bounty_id})
      ON CONFLICT (user_id, bounty_id) DO UPDATE SET
        status = 'joined',
        joined_at = NOW()
      RETURNING *
    `;

    return NextResponse.json({
      success: true,
      data: userBounty
    });
  } catch (error) {
    console.error('加入任务失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to join bounty' },
      { status: 500 }
    );
  }
}
```

### 2. 用户管理 API (2个接口)

#### GET /api/profile - 获取用户资料
```typescript
// app/api/profile/route.ts
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const [profile] = await db`
      SELECT * FROM profiles WHERE id = ${userId}
    `;

    if (!profile) {
      // 自动创建用户资料
      const { user } = await currentUser();
      const [newProfile] = await db`
        INSERT INTO profiles (id, email, name, avatar_url)
        VALUES (
          ${userId},
          ${user?.emailAddresses[0]?.emailAddress || ''},
          ${user?.fullName || ''},
          ${user?.imageUrl || ''}
        )
        RETURNING *
      `;
      return NextResponse.json({ success: true, data: newProfile });
    }

    return NextResponse.json({ success: true, data: profile });
  } catch (error) {
    console.error('获取用户资料失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch profile' },
      { status: 500 }
    );
  }
}
```

#### GET /api/user-bounties - 我的任务
```typescript
// app/api/user-bounties/route.ts (继续)
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userBounties = await db`
      SELECT
        ub.status, ub.joined_at, ub.completed_at,
        b.id, b.title, b.description, b.reward_amount,
        b.reward_currency, b.deadline, b.source_url
      FROM user_bounties ub
      JOIN bounties b ON ub.bounty_id = b.id
      WHERE ub.user_id = ${userId}
      ORDER BY ub.joined_at DESC
    `;

    return NextResponse.json({
      success: true,
      data: userBounties
    });
  } catch (error) {
    console.error('获取用户任务失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user bounties' },
      { status: 500 }
    );
  }
}
```

### 3. AI 解析 API (1个接口)

#### POST /api/ai/parse - AI 解析链接
```typescript
// app/api/ai/parse/route.ts
export async function POST(request: Request) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { success: false, error: 'URL is required' },
        { status: 400 }
      );
    }

    // 简单的解析逻辑 (MVP 阶段)
    // 后续可以接入 OpenAI API 或其他 AI 服务
    let parsedData = {
      title: "解析的任务标题",
      description: "从链接内容解析的任务描述",
      reward_amount: 100,
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    };

    // 根据 URL 类型进行简单解析
    if (url.includes('twitter.com') || url.includes('x.com')) {
      parsedData = {
        title: "Twitter 推广任务",
        description: "转发并评论指定推文，增加曝光度",
        reward_amount: 50,
        deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      };
    } else if (url.includes('github.com')) {
      parsedData = {
        title: "GitHub 开源贡献",
        description: "为开源项目贡献代码或文档",
        reward_amount: 200,
        deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
      };
    }

    return NextResponse.json({
      success: true,
      data: parsedData
    });
  } catch (error) {
    console.error('AI 解析失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to parse URL' },
      { status: 500 }
    );
  }
}
```

## 🎯 MVP 核心功能流程

### 用户故事 1：发布任务
1. **输入链接**: 用户在发布页面粘贴 Twitter/GitHub 链接
2. **AI 解析**: 点击"智能解析"调用 `POST /api/ai/parse`
3. **确认信息**: 系统自动填充标题、描述、奖励金额，用户可修改
4. **发布任务**: 确认后调用 `POST /api/bounties`
5. **跳转列表**: 任务发布成功，跳转到任务列表页面

### 用户故事 2：浏览任务
1. **加载列表**: 用户访问首页，调用 `GET /api/bounties`
2. **展示任务**: 显示任务卡片（标题、奖励、截止时间、参与人数）
3. **查看详情**: 点击任务卡片，调用 `GET /api/bounties/[id]`
4. **显示详情**: 展示完整的任务信息和参与状态

### 用户故事 3：加入任务
1. **点击加入**: 在任务详情页点击"加入任务"按钮
2. **发送请求**: 调用 `POST /api/user-bounties`
3. **更新状态**: 成功后按钮变为"已加入"状态
4. **同步列表**: 任务出现在"我的任务"列表中

### 用户故事 4：管理我的任务
1. **访问页面**: 用户访问"我的任务"页面
2. **获取数据**: 调用 `GET /api/user-bounties`
3. **显示状态**: 展示任务状态（进行中、已完成）
4. **操作任务**: 可以查看详情或标记完成

## 📝 开发步骤（极简版）

### Step 1: 环境搭建 (15分钟)
```bash
# 1. 创建 Next.js 项目
npx create-next-app@latest bountygo --typescript --tailwind --app

# 2. 安装必要依赖
cd bountygo
npm install @clerk/nextjs postgres

# 3. 配置环境变量
cp .env.example .env.local
# 填入 DATABASE_URL 和 Clerk 配置
```

### Step 2: 数据库设置 (15分钟)
```bash
# 1. 运行数据库初始化脚本
node scripts/setup-bountygo-db.js

# 2. 验证数据库连接
node scripts/test-db-connection.js
```

### Step 3: 后端 API 开发 (60分钟)
- ✅ 实现 7 个核心 API 接口
- ✅ 集成 Clerk 认证中间件
- ✅ 添加基础错误处理
- ✅ 测试所有接口功能

### Step 4: 前端开发 (120分钟)
**阶段1: API 测试页面 (60分钟)**
- ✅ 创建简单的测试页面
- ✅ 接入所有 7 个 API 接口
- ✅ 验证完整业务流程

**阶段2: 前端美化 (60分钟)**
- ✅ 添加基础样式和布局
- ✅ 优化用户体验
- ✅ 响应式设计

### Step 5: 测试和部署 (30分钟)
- ✅ 功能测试和 Bug 修复
- ✅ 部署到 Vercel
- ✅ 配置生产环境变量

## 📱 前端开发指南

### 阶段1: API 测试页面 (先跑通逻辑)

创建一个简单的测试页面，把所有 API 接口都接上，确保业务逻辑完整可用。

#### 创建测试页面
```typescript
// app/test/page.tsx
'use client';

import { useState } from 'react';

export default function TestPage() {
  const [result, setResult] = useState('');

  // 测试获取任务列表
  const testGetBounties = async () => {
    const res = await fetch('/api/bounties');
    const data = await res.json();
    setResult(JSON.stringify(data, null, 2));
  };

  // 测试创建任务
  const testCreateBounty = async () => {
    const res = await fetch('/api/bounties', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: '测试任务',
        description: '这是一个测试任务',
        reward_amount: 100,
        deadline: '2024-12-31',
      }),
    });
    const data = await res.json();
    setResult(JSON.stringify(data, null, 2));
  };

  // 测试 AI 解析
  const testAIParse = async () => {
    const res = await fetch('/api/ai/parse', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: 'https://twitter.com/example/status/123',
      }),
    });
    const data = await res.json();
    setResult(JSON.stringify(data, null, 2));
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>BountyGo API 测试页面</h1>

      <div style={{ marginBottom: '20px' }}>
        <button onClick={testGetBounties} style={{ margin: '5px', padding: '10px' }}>
          测试获取任务列表
        </button>
        <button onClick={testCreateBounty} style={{ margin: '5px', padding: '10px' }}>
          测试创建任务
        </button>
        <button onClick={testAIParse} style={{ margin: '5px', padding: '10px' }}>
          测试 AI 解析
        </button>
      </div>

      <div style={{
        background: '#f5f5f5',
        padding: '20px',
        borderRadius: '5px',
        whiteSpace: 'pre-wrap',
        minHeight: '300px'
      }}>
        {result || '点击按钮测试 API...'}
      </div>
    </div>
  );
}
```

#### 完整的 API 测试功能
在测试页面中添加所有 7 个接口的测试按钮：
1. GET /api/bounties - 获取任务列表
2. POST /api/bounties - 创建任务
3. GET /api/bounties/[id] - 获取任务详情
4. POST /api/user-bounties - 加入任务
5. GET /api/user-bounties - 我的任务
6. GET /api/profile - 用户资料
7. POST /api/ai/parse - AI 解析

### 阶段2: 前端美化 (基础即可)

API 测试通过后，再创建正式的用户界面。重点是功能可用，样式简洁即可。

---

*BountyGo MVP 开发指南 - 专注实用性*
