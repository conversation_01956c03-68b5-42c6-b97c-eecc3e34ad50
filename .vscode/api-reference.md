# VGee Creator API 参考文档

## 📋 API 概览

VGee Creator 提供完整的 RESTful API，支持用户认证、服务管理、工具管理、用户资料等核心功能。

### 基础信息
- **Base URL**: `https://your-domain.com/api` (生产环境)
- **Base URL**: `http://localhost:3000/api` (开发环境)
- **认证方式**: JW<PERSON> (Clerk)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
  };
}
```

## 🔐 认证 API

### 认证说明
所有需要认证的 API 都通过 Clerk 中间件自动处理，无需手动传递 Token。

### 公开路由 (无需认证)
- `GET /api/public/creators/:id/services` - 获取创作者公开服务
- `GET /api/health` - 健康检查

### 受保护路由 (需要认证)
- 所有 `/api/services/*` 路由
- 所有 `/api/profile/*` 路由
- 所有 `/api/tools/*` 路由

## 👤 用户资料 API

### 获取当前用户资料
```http
GET /api/profile
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "clerk_user_id": "user_xxx",
    "email": "<EMAIL>",
    "full_name": "张三",
    "avatar_url": "https://example.com/avatar.jpg",
    "role": "creator",
    "experience_points": 150,
    "level": 3,
    "is_premium_member": false,
    "youtube_id": "UCxxxxx",
    "subscribers": 1500,
    "views": 50000,
    "profile_completion_percentage": 85
  }
}
```

### 更新用户资料
```http
PUT /api/profile
Content-Type: application/json

{
  "full_name": "李四",
  "motto": "专业的内容创作者",
  "social_links": {
    "twitter": "https://twitter.com/username",
    "instagram": "https://instagram.com/username"
  }
}
```

### 同步 YouTube 数据
```http
POST /api/profile/sync-youtube
```

## 🛠️ 服务管理 API

### 获取我的服务列表
```http
GET /api/services/my-services?type=embedded&status=active
```

**查询参数:**
- `type` (可选): `embedded` | `custom`
- `status` (可选): `active` | `inactive`

**响应示例:**
```json
{
  "success": true,
  "data": {
    "embedded": [
      {
        "id": "uuid",
        "title": "短视频口播推广",
        "description": "专业的短视频内容创作",
        "min_price": 100,
        "max_price": 500,
        "status": "active",
        "created_at": "2025-01-01T00:00:00Z"
      }
    ],
    "custom": []
  }
}
```

### 创建新服务
```http
POST /api/services
Content-Type: application/json

{
  "type": "embedded",
  "title": "短视频口播推广",
  "description": "专业的短视频内容创作服务",
  "min_price": 100,
  "max_price": 500,
  "status": "active"
}
```

### 更新服务
```http
PUT /api/services/:id
Content-Type: application/json

{
  "title": "更新后的服务标题",
  "description": "更新后的服务描述",
  "min_price": 150,
  "max_price": 600
}
```

### 切换服务状态
```http
PATCH /api/services/:id/status
Content-Type: application/json

{
  "status": "inactive"
}
```

### 删除服务
```http
DELETE /api/services/:id
```

## 🔧 工具管理 API

### 获取所有工具
```http
GET /api/tools?category=social&platform=youtube&featured=true
```

**查询参数:**
- `category` (可选): 工具分类
- `platform` (可选): 平台类型
- `featured` (可选): 是否为推荐工具

### 获取用户工具
```http
GET /api/tools/user-tools?status=active
```

### 添加用户工具
```http
POST /api/tools/user-tools
Content-Type: application/json

{
  "tool_id": "uuid",
  "status": "active",
  "configuration": {
    "api_key": "xxx",
    "settings": {}
  }
}
```

### 更新用户工具
```http
PUT /api/tools/user-tools/:id
Content-Type: application/json

{
  "status": "inactive",
  "configuration": {
    "updated_setting": "value"
  }
}
```

## 🌐 公开 API

### 获取创作者公开服务
```http
GET /api/public/creators/:userId/services?type=embedded
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "creator": {
      "full_name": "张三",
      "avatar_url": "https://example.com/avatar.jpg",
      "motto": "专业的内容创作者",
      "youtube_title": "张三的频道",
      "subscribers": 1500
    },
    "embedded": [
      {
        "id": "uuid",
        "title": "短视频口播推广",
        "description": "专业的短视频内容创作",
        "min_price": 100,
        "max_price": 500
      }
    ],
    "custom": []
  }
}
```

## 🎯 排行榜 API

### 获取排行榜数据
```http
GET /api/leaderboard?type=experience&limit=10
```

**查询参数:**
- `type`: `experience` | `subscribers` | `views`
- `limit` (可选): 返回数量，默认 10

## 🔔 Webhook API

### Clerk 用户事件
```http
POST /api/webhooks/clerk
```

处理 Clerk 用户创建、更新、删除事件。

### YouTube 数据同步
```http
POST /api/webhooks/youtube
```

处理 YouTube 数据更新事件。

## 📊 错误代码

### HTTP 状态码
- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `422` - 数据验证失败
- `500` - 服务器内部错误

### 错误响应示例
```json
{
  "success": false,
  "error": {
    "message": "服务标题不能为空",
    "code": "VALIDATION_ERROR"
  }
}
```

### 常见错误代码
- `VALIDATION_ERROR` - 数据验证失败
- `UNAUTHORIZED` - 未认证
- `FORBIDDEN` - 权限不足
- `NOT_FOUND` - 资源不存在
- `DUPLICATE_RESOURCE` - 资源重复
- `DATABASE_ERROR` - 数据库错误
- `EXTERNAL_API_ERROR` - 外部 API 错误

## 🔄 数据验证规则

### 服务数据验证
```typescript
interface ServiceValidation {
  title: string;        // 1-100 字符
  description: string;  // 1-500 字符
  min_price: number;    // >= 0
  max_price: number;    // >= min_price
  type: 'embedded' | 'custom';
  status: 'active' | 'inactive';
}
```

### 用户资料验证
```typescript
interface ProfileValidation {
  full_name?: string;   // 1-50 字符
  motto?: string;       // 1-200 字符
  social_links?: {
    [key: string]: string; // 有效的 URL
  };
}
```

## 📝 使用示例

### JavaScript/TypeScript 客户端
```typescript
// 获取服务列表
async function getMyServices() {
  const response = await fetch('/api/services/my-services', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error?.message || 'API request failed');
  }
  
  return data.data;
}

// 创建新服务
async function createService(serviceData) {
  const response = await fetch('/api/services', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(serviceData),
  });
  
  return response.json();
}
```

### cURL 示例
```bash
# 获取服务列表
curl -X GET "http://localhost:3000/api/services/my-services" \
  -H "Content-Type: application/json"

# 创建新服务
curl -X POST "http://localhost:3000/api/services" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "embedded",
    "title": "短视频推广",
    "description": "专业的短视频内容创作",
    "min_price": 100,
    "max_price": 500,
    "status": "active"
  }'
```

## 🚀 性能优化

### 缓存策略
- **React Query**: 客户端数据缓存
- **数据库连接池**: 优化数据库连接
- **CDN**: 静态资源缓存

### 限流策略
- **API 限流**: 每分钟最多 100 次请求
- **上传限流**: 文件大小限制 10MB
- **数据库查询**: 查询超时 30 秒

---

*最后更新: 2025-07-26*
