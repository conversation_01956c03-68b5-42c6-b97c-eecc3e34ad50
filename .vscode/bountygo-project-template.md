# BountyGo 项目模板

## 📁 完整项目结构

```
bountygo/
├── 📄 配置文件
│   ├── package.json              # 依赖管理
│   ├── next.config.js            # Next.js 配置
│   ├── tailwind.config.ts        # Tailwind 配置
│   ├── tsconfig.json             # TypeScript 配置
│   └── .env.local                # 环境变量
├── 📂 app/                       # Next.js App Router
│   ├── layout.tsx                # 根布局
│   ├── page.tsx                  # 首页
│   ├── dashboard/                # 仪表板页面
│   │   └── page.tsx
│   ├── api/                      # API 路由
│   │   ├── bounties/
│   │   │   ├── route.ts          # GET/POST /api/bounties
│   │   │   └── [id]/route.ts     # GET /api/bounties/[id]
│   │   ├── user-bounties/
│   │   │   └── route.ts          # GET/POST /api/user-bounties
│   │   ├── profile/
│   │   │   └── route.ts          # GET /api/profile
│   │   └── ai/
│   │       └── parse/route.ts    # POST /api/ai/parse
│   └── globals.css               # 全局样式
├── 📂 components/                # React 组件
│   ├── ui/                       # 基础 UI 组件
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── input.tsx
│   │   ├── textarea.tsx
│   │   ├── badge.tsx
│   │   ├── tabs.tsx
│   │   └── label.tsx
│   ├── layout/
│   │   └── MainLayout.tsx        # 主布局组件
│   └── bounty/                   # 任务相关组件
│       ├── BountyList.tsx        # 任务列表
│       ├── CreateBountyForm.tsx  # 创建任务表单
│       └── MyBounties.tsx        # 我的任务
├── 📂 lib/                       # 工具库
│   ├── db.ts                     # 数据库连接
│   ├── utils.ts                  # 工具函数
│   └── validations.ts            # 数据验证
├── 📂 types/                     # TypeScript 类型
│   └── index.ts                  # 类型定义
├── 📂 scripts/                   # 工具脚本
│   ├── setup-bountygo-db.js      # 数据库初始化
│   └── test-db-connection.js     # 数据库连接测试
└── 📂 public/                    # 静态资源
    └── favicon.ico
```

## 📦 package.json 模板

```json
{
  "name": "bountygo",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "setup-db": "node scripts/setup-bountygo-db.js",
    "test-db": "node scripts/test-db-connection.js"
  },
  "dependencies": {
    "@clerk/nextjs": "^6.20.2",
    "@hookform/resolvers": "^3.3.2",
    "@radix-ui/react-badge": "^1.0.4",
    "@radix-ui/react-card": "^1.0.4",
    "@radix-ui/react-label": "^2.0.2",
    "@radix-ui/react-slot": "^1.0.2",
    "@radix-ui/react-tabs": "^1.0.4",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "lucide-react": "^0.323.0",
    "next": "^15.3.3",
    "postgres": "^3.4.7",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "react-hook-form": "^7.48.2",
    "tailwind-merge": "^2.2.1",
    "tailwindcss-animate": "^1.0.7",
    "zod": "^3.22.4"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^19.1.6",
    "@types/react-dom": "^19.1.5",
    "autoprefixer": "^10.0.1",
    "eslint": "^8",
    "eslint-config-next": "^15.3.3",
    "postcss": "^8",
    "tailwindcss": "^3.3.0",
    "typescript": "^5"
  }
}
```

## ⚙️ 核心配置文件

### next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  serverExternalPackages: ["postgres"],
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        net: false,
        tls: false,
        crypto: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
```

### tailwind.config.ts
```typescript
import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};

export default config;
```

### lib/db.ts
```typescript
import postgres from "postgres";

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required");
}

const sql = postgres(process.env.DATABASE_URL, {
  ssl: process.env.NODE_ENV === "production" ? "require" : "prefer",
  max: 10,
  idle_timeout: 20,
  connect_timeout: 30,
  onnotice: () => {},
  debug: process.env.NODE_ENV === "development",
});

export { sql as db };
```

### lib/utils.ts
```typescript
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number, currency: string = 'USDT') {
  return `${amount} ${currency}`;
}

export function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('zh-CN');
}

export function isValidUrl(string: string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}
```

### types/index.ts
```typescript
export interface Bounty {
  id: string;
  title: string;
  description: string;
  reward_amount: number;
  reward_currency: string;
  deadline: string;
  source_url?: string;
  sponsor_id: string;
  status: 'active' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
  sponsor_name?: string;
  sponsor_avatar?: string;
  participants_count?: number;
}

export interface UserBounty {
  id: string;
  user_id: string;
  bounty_id: string;
  status: 'joined' | 'completed' | 'cancelled';
  joined_at: string;
  completed_at?: string;
  bounty?: Bounty;
}

export interface Profile {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  created_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface CreateBountyRequest {
  title: string;
  description: string;
  reward_amount: number;
  deadline: string;
  source_url?: string;
}

export interface JoinBountyRequest {
  bounty_id: string;
}

export interface ParseUrlRequest {
  url: string;
}

export interface ParseUrlResponse {
  title: string;
  description: string;
  reward_amount: number;
  deadline: string;
}
```

## 🚀 一键启动脚本

### scripts/quick-start.sh
```bash
#!/bin/bash

echo "🚀 BountyGo 快速启动脚本"
echo "基于 VGee Creator 最佳实践"

# 1. 检查 Node.js 版本
echo "📋 检查环境..."
node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$node_version" -lt 18 ]; then
  echo "❌ 需要 Node.js 18+ 版本"
  exit 1
fi

# 2. 安装依赖
echo "📦 安装依赖..."
npm install

# 3. 检查环境变量
if [ ! -f ".env.local" ]; then
  echo "⚠️  请创建 .env.local 文件并配置环境变量"
  echo "DATABASE_URL=your_neon_database_url"
  echo "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_key"
  echo "CLERK_SECRET_KEY=your_clerk_secret"
  exit 1
fi

# 4. 设置数据库
echo "🗄️  设置数据库..."
npm run setup-db

# 5. 测试数据库连接
echo "🔍 测试数据库连接..."
npm run test-db

# 6. 启动开发服务器
echo "🎉 启动开发服务器..."
npm run dev
```

## 📋 开发检查清单

### 环境准备 ✅
- [ ] Node.js 18+ 已安装
- [ ] 创建 Neon 数据库账户
- [ ] 创建 Clerk 认证应用
- [ ] 配置 .env.local 文件

### 数据库设置 ✅
- [ ] 运行数据库初始化脚本
- [ ] 验证 3 张表创建成功
- [ ] 插入测试数据
- [ ] 测试数据库连接

### API 开发 ✅
- [ ] 实现 GET /api/bounties
- [ ] 实现 POST /api/bounties
- [ ] 实现 GET /api/bounties/[id]
- [ ] 实现 POST /api/user-bounties
- [ ] 实现 GET /api/user-bounties
- [ ] 实现 GET /api/profile
- [ ] 实现 POST /api/ai/parse

### 前端开发 ✅
- [ ] 创建主布局组件
- [ ] 实现任务列表页面
- [ ] 实现创建任务表单
- [ ] 实现我的任务页面
- [ ] 集成 Clerk 认证
- [ ] 添加响应式设计

### 测试验证 ✅
- [ ] 用户注册登录流程
- [ ] 发布任务功能
- [ ] 浏览任务功能
- [ ] 加入任务功能
- [ ] AI 解析功能
- [ ] 移动端适配

### 部署上线 ✅
- [ ] 推送代码到 GitHub
- [ ] 连接 Vercel 部署
- [ ] 配置生产环境变量
- [ ] 验证生产环境功能
- [ ] 设置自定义域名

## 🎯 MVP 成功标准

### 功能完整性
- ✅ 用户可以注册登录
- ✅ 用户可以发布赏金任务
- ✅ 用户可以浏览和加入任务
- ✅ AI 可以解析链接内容
- ✅ 用户可以管理自己的任务

### 技术指标
- ✅ 页面加载时间 < 3 秒
- ✅ API 响应时间 < 1 秒
- ✅ 移动端完全适配
- ✅ 无明显 Bug 和错误

### 用户体验
- ✅ 界面简洁直观
- ✅ 操作流程顺畅
- ✅ 错误提示友好
- ✅ 响应式设计良好

---

*BountyGo 项目模板 - 快速启动指南*
