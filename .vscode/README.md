# VGee Creator VS Code 配置文档

## 📋 文档概览

本目录包含了 VGee Creator 项目的完整架构文档和 VS Code 配置文件，为开发者提供全面的项目理解和开发环境配置。

## 📚 文档列表

### VGee Creator 核心文档
1. **[项目总览](./project-overview.md)** - 项目的整体介绍和快速开始指南
2. **[架构文档](./architecture.md)** - 详细的技术架构和系统设计
3. **[技术架构图](./technical-diagrams.md)** - 可视化的系统架构图和流程图
4. **[开发指南](./development-guide.md)** - 完整的开发规范和最佳实践
5. **[API 参考](./api-reference.md)** - 详细的 API 接口文档

### BountyGo MVP 开发指南
6. **[BountyGo 快速开始](./bountygo-quickstart.md)** - ⚡ 3-4小时完成 MVP 的步骤清单
7. **[BountyGo 环境配置](./bountygo-setup-guide.md)** - 🔧 第三方服务配置指南 (Clerk、Neon、Vercel)
8. **[BountyGo MVP 指南](./bountygo-mvp-guide.md)** - 📖 详细的开发教程和代码示例
9. **[BountyGo 项目模板](./bountygo-project-template.md)** - 📁 完整的项目结构和配置模板

### VS Code 配置文件
1. **[settings.json](./settings.json)** - VS Code 工作区设置
2. **[tasks.json](./tasks.json)** - 自定义任务配置
3. **[launch.json](./launch.json)** - 调试配置

## 🚀 快速开始

### 1. VGee Creator 开发者
对于 VGee Creator 项目的开发者，建议按以下顺序阅读：

```
1. project-overview.md     # 了解项目整体
2. architecture.md         # 理解技术架构
3. development-guide.md    # 学习开发规范
4. technical-diagrams.md   # 查看架构图
5. api-reference.md        # 参考 API 文档
```

### 2. BountyGo 开发者
对于希望快速开发 BountyGo 的团队，**推荐阅读顺序**：

```
1. bountygo-quickstart.md     # ⚡ 快速开始 (步骤清单)
2. bountygo-setup-guide.md    # 🔧 环境配置 (Clerk、Neon、Vercel)
3. bountygo-mvp-guide.md      # 📖 详细开发教程
4. bountygo-project-template.md # 📁 项目结构参考
```

**可选参考文档**：
```
5. architecture.md            # 技术架构参考
6. development-guide.md       # 开发规范参考
```

### 2. VS Code 配置
打开项目后，VS Code 会自动应用以下配置：
- **代码格式化**: Prettier 自动格式化
- **代码检查**: ESLint 实时检查
- **类型检查**: TypeScript 严格模式
- **Tailwind CSS**: 智能提示和补全
- **文件嵌套**: 相关文件自动分组

### 3. 常用任务
使用 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac) 打开命令面板，然后输入 "Tasks: Run Task" 来执行以下任务：

- **dev** - 启动开发服务器
- **build** - 构建生产版本
- **lint** - 代码检查
- **test-db** - 测试数据库连接
- **migrate** - 运行数据库迁移
- **setup** - 完整项目设置

## 🏗️ 项目架构概览

### VGee Creator (完整平台)
```
VGee Creator (Web3 创作者平台)
├── 前端: Next.js 15 + React 19 + TypeScript
├── 样式: Tailwind CSS + Radix UI + Framer Motion
├── 认证: Clerk Authentication
├── 数据库: Neon PostgreSQL
├── 状态管理: Zustand + TanStack Query
├── 可视化: D3.js + Recharts
└── 部署: AWS Amplify + CloudFront
```

### BountyGo (极简 MVP)
```
BountyGo (赏金任务平台 MVP)
├── 前端: Next.js 15 + React 19 + TypeScript
├── 样式: Tailwind CSS + Radix UI (精简版)
├── 认证: Clerk Authentication
├── 数据库: Neon PostgreSQL (3张表)
├── 表单: React Hook Form + Zod
├── AI解析: 简单逻辑 (可扩展 OpenAI)
└── 部署: Vercel (一键部署)
```

## 🔧 开发环境要求

### 必需软件
- **Node.js**: >= 20.0.0
- **Yarn**: 1.22.22
- **Git**: 最新版本
- **VS Code**: 推荐编辑器

### 推荐扩展
项目已配置推荐扩展列表，VS Code 会自动提示安装：
- Prettier - 代码格式化
- ESLint - 代码检查
- Tailwind CSS IntelliSense - CSS 智能提示
- TypeScript Importer - 自动导入
- Auto Rename Tag - 标签重命名
- Path Intellisense - 路径智能提示

## 📊 核心功能模块

### 1. 用户系统
- Clerk 认证集成
- 用户资料管理
- 角色权限控制
- 经验值和等级系统

### 2. 服务交易
- 服务发布和管理
- 价格设置和展示
- 公开服务页面
- 合作撮合功能

### 3. 工具生态
- 第三方工具集成
- 用户工具配置
- 平台 API 对接
- 工具使用统计

### 4. 任务系统
- 普通任务 (1-3 USDT)
- 悬赏任务 (500-3000 USDT)
- 任务审核流程
- 奖励自动发放

### 5. 代币经济
- USDT 统一结算
- 多链钱包支持
- 智能合约集成
- 资金池管理

## 🎨 设计系统

### 视觉风格
- **主色调**: 渐变紫色系
- **强调色**: 青色系
- **辅助色**: YouTube 红
- **背景**: 毛玻璃效果

### 组件库
- **基础组件**: Radix UI
- **图标**: Lucide React
- **动画**: Framer Motion
- **布局**: 响应式设计

## 🚀 部署流程

### 开发环境
```bash
yarn dev          # 启动开发服务器 (localhost:3000)
yarn build        # 构建检查
yarn lint         # 代码检查
```

### 生产环境
```bash
yarn build        # 构建生产版本
yarn start        # 启动生产服务器
```

### CI/CD
- **版本控制**: GitHub
- **自动化**: GitHub Actions
- **部署**: AWS Amplify
- **监控**: CloudWatch

## 🔍 调试指南

### VS Code 调试配置
项目已配置多种调试模式：
- **服务端调试**: 调试 Next.js API 路由
- **客户端调试**: 调试 React 组件
- **全栈调试**: 同时调试前后端
- **脚本调试**: 调试数据库脚本

### 常见问题排查
1. **数据库连接**: 运行 `test-db` 任务
2. **环境变量**: 检查 `.env.local` 文件
3. **依赖问题**: 运行 `clean` + `install` 任务
4. **类型错误**: 运行 `type-check` 任务

## 📈 性能优化

### 前端优化
- **代码分割**: Next.js 自动分割
- **图片优化**: Next.js Image 组件
- **缓存策略**: React Query 缓存
- **懒加载**: 组件按需加载

### 后端优化
- **数据库连接池**: PostgreSQL 连接优化
- **API 缓存**: 合理的缓存策略
- **查询优化**: 高效的 SQL 查询
- **错误处理**: 完善的错误机制

## 🤝 贡献指南

### 开发流程
1. 阅读相关文档
2. 创建功能分支
3. 遵循代码规范
4. 编写测试用例
5. 提交 Pull Request

### 代码规范
- **TypeScript**: 严格类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 统一代码格式
- **Conventional Commits**: 规范提交信息

## 📞 支持与反馈

### 技术支持
- **文档问题**: 查看相关 `.md` 文件
- **配置问题**: 检查 VS Code 配置文件
- **开发问题**: 参考开发指南

### 联系方式
- **项目仓库**: [GitHub](https://github.com/wjllance/vgee-creator)
- **问题反馈**: GitHub Issues
- **功能建议**: GitHub Discussions

---

*最后更新: 2025-07-26*
*文档版本: v1.0.0*

## 📝 更新日志

### v1.0.0 (2025-07-26)
- ✅ 创建完整的项目架构文档
- ✅ 配置 VS Code 开发环境
- ✅ 添加技术架构图和流程图
- ✅ 编写详细的开发指南
- ✅ 提供完整的 API 参考文档
- ✅ 设置调试和任务配置
