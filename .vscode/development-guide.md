# VGee Creator 开发指南

## 🚀 快速开始

### 环境要求
- **Node.js**: >= 20.0.0
- **Yarn**: 1.22.22
- **PostgreSQL**: 支持 Neon 云数据库
- **Git**: 最新版本

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/wjllance/vgee-creator.git
cd vgee-creator
```

2. **安装依赖**
```bash
npm i -g yarn
yarn install
```

3. **环境配置**
```bash
cp env.example .env.local
# 编辑 .env.local 文件，填入必要的环境变量
```

4. **数据库设置**
```bash
# 测试数据库连接
node scripts/test-db-connection.js

# 运行数据库迁移
node scripts/run-migrations.js
```

5. **启动开发服务器**
```bash
yarn dev
```

访问 http://localhost:3000 查看应用。

## 📝 环境变量配置

### 必需变量
```env
# Neon 数据库
DATABASE_URL=********************************************

# Clerk 认证
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_xxx
CLERK_SECRET_KEY=sk_test_xxx

# YouTube API
YOUTUBE_API_KEY=your_youtube_api_key
NEXT_PUBLIC_YOUTUBE_CLIENT_ID=your_client_id
YOUTUBE_CLIENT_SECRET=your_client_secret
```

### 可选变量
```env
# 开发环境
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 生产环境
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## 🏗️ 项目结构详解

### 核心目录说明

#### `/src/app` - Next.js App Router
```
app/
├── api/                    # API 路由
│   ├── services/          # 服务管理 API
│   ├── profile/           # 用户资料 API
│   ├── public/            # 公开 API
│   └── webhooks/          # Webhook 处理
├── dashboard/             # 仪表板页面
├── creator/               # 创作者页面
├── profile/               # 用户资料页面
└── layout.tsx             # 根布局
```

#### `/src/components` - React 组件
```
components/
├── ui/                    # 基础 UI 组件 (Radix UI)
├── dashboard/             # 仪表板专用组件
├── services/              # 服务管理组件
├── tools/                 # 工具组件
└── providers/             # Context Providers
```

#### `/src/lib` - 工具库
```
lib/
├── database/              # 数据库操作函数
├── db.ts                  # 数据库连接配置
├── schema.ts              # 数据库模式定义
└── utils.ts               # 通用工具函数
```

## 🔧 开发规范

### 代码风格
- **TypeScript**: 严格模式，完整类型定义
- **ESLint**: 遵循 Next.js 推荐配置
- **Prettier**: 自动代码格式化
- **命名规范**: camelCase (变量/函数), PascalCase (组件/类型)

### 组件开发规范

#### 1. 组件文件结构
```typescript
// components/ExampleComponent.tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface ExampleComponentProps {
  title: string;
  description?: string;
  className?: string;
}

export function ExampleComponent({ 
  title, 
  description, 
  className 
}: ExampleComponentProps) {
  return (
    <div className={cn("base-styles", className)}>
      <h2>{title}</h2>
      {description && <p>{description}</p>}
    </div>
  );
}
```

#### 2. API 路由规范
```typescript
// app/api/example/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }

    // 业务逻辑
    const data = await fetchData(userId);
    
    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' }, 
      { status: 500 }
    );
  }
}
```

### 数据库操作规范

#### 1. 数据库查询
```typescript
// lib/database/example.ts
import { db } from '@/lib/db';
import type { Profile } from '@/types';

export async function getUserProfile(userId: string): Promise<Profile | null> {
  try {
    const [profile] = await db`
      SELECT * FROM profiles 
      WHERE clerk_user_id = ${userId}
    `;
    return profile || null;
  } catch (error) {
    console.error('Database error:', error);
    throw new Error('Failed to fetch user profile');
  }
}
```

#### 2. 类型安全
```typescript
// types/example.ts
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
  };
}

export interface Service {
  id: string;
  title: string;
  description: string;
  price_min: number;
  price_max: number;
  status: 'active' | 'inactive';
  created_at: Date;
  updated_at: Date;
}
```

## 🧪 测试策略

### 单元测试
```typescript
// __tests__/components/ExampleComponent.test.tsx
import { render, screen } from '@testing-library/react';
import { ExampleComponent } from '@/components/ExampleComponent';

describe('ExampleComponent', () => {
  it('renders title correctly', () => {
    render(<ExampleComponent title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });
});
```

### API 测试
```typescript
// __tests__/api/example.test.ts
import { GET } from '@/app/api/example/route';
import { NextRequest } from 'next/server';

describe('/api/example', () => {
  it('returns data for authenticated user', async () => {
    const request = new NextRequest('http://localhost:3000/api/example');
    const response = await GET(request);
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
  });
});
```

## 🚀 部署指南

### 开发环境部署
```bash
# 启动开发服务器
yarn dev

# 构建检查
yarn build

# 代码检查
yarn lint
```

### 生产环境部署
```bash
# 构建生产版本
yarn build

# 启动生产服务器
yarn start
```

### 环境变量检查
```bash
# 检查数据库连接
node scripts/test-db-connection.js

# 检查表结构
node scripts/check-table-structure.js
```

## 🔍 调试技巧

### 1. 数据库调试
```typescript
// 开启数据库查询日志
const sql = postgres(process.env.DATABASE_URL, {
  debug: process.env.NODE_ENV === 'development'
});
```

### 2. API 调试
```typescript
// 使用 console.log 进行调试
console.log('API Request:', { userId, params });

// 使用 Next.js 内置的日志
import { NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  console.log('Request headers:', request.headers);
  console.log('Request body:', await request.json());
}
```

### 3. 前端调试
```typescript
// React Query DevTools
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

export function QueryProvider({ children }: { children: React.ReactNode }) {
  return (
    <QueryClient>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClient>
  );
}
```

## 📚 常用命令

### 开发命令
```bash
# 启动开发服务器
yarn dev

# 构建项目
yarn build

# 启动生产服务器
yarn start

# 代码检查
yarn lint

# 类型检查
yarn type-check
```

### 数据库命令
```bash
# 测试数据库连接
node scripts/test-db-connection.js

# 运行迁移
node scripts/run-migrations.js

# 检查表结构
node scripts/check-table-structure.js
```

### Git 命令
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码 (使用 Conventional Commits)
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/new-feature
```

## 🐛 常见问题

### 1. 数据库连接失败
```bash
# 检查环境变量
echo $DATABASE_URL

# 测试连接
node scripts/test-db-connection.js
```

### 2. Clerk 认证问题
```bash
# 检查 Clerk 配置
echo $NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
echo $CLERK_SECRET_KEY
```

### 3. 构建失败
```bash
# 清理缓存
rm -rf .next
yarn build
```

## 📖 学习资源

- [Next.js 官方文档](https://nextjs.org/docs)
- [React 官方文档](https://react.dev)
- [TypeScript 手册](https://www.typescriptlang.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [Clerk 认证文档](https://clerk.com/docs)
- [PostgreSQL 文档](https://www.postgresql.org/docs)

---

*最后更新: 2025-07-26*
