# BountyGo 环境配置指南

## 🔧 需要自行配置的服务

开发 BountyGo 需要配置以下第三方服务，这些都是免费的，但需要注册账户：

### 1. 🗄️ Neon Database (数据库)
### 2. 🔐 Clerk (用户认证)
### 3. 🚀 Vercel (部署平台)
### 4. 📁 GitHub (代码托管)

---

## 1. 🗄️ Neon Database 配置

### 为什么选择 Neon？
- ✅ **免费额度**: 每月 3GB 存储 + 100 小时计算时间
- ✅ **无服务器**: 自动休眠，按需唤醒
- ✅ **PostgreSQL**: 完全兼容，功能强大
- ✅ **零运维**: 无需管理服务器

### 配置步骤

#### Step 1: 注册 Neon 账户
1. 访问 [neon.tech](https://neon.tech)
2. 点击 "Sign up" 注册账户
3. 可以使用 GitHub 账户快速注册

#### Step 2: 创建数据库项目
1. 登录后点击 "Create a project"
2. 填写项目信息：
   - **Project name**: `bountygo-db`
   - **Database name**: `bountygo`
   - **Region**: 选择离你最近的区域 (如 Asia Pacific)
3. 点击 "Create project"

#### Step 3: 获取连接字符串
1. 项目创建后，在 Dashboard 页面找到 "Connection string"
2. 复制 PostgreSQL 连接字符串，格式类似：
```
postgresql://username:<EMAIL>/bountygo?sslmode=require
```

#### Step 4: 配置环境变量
在项目根目录的 `.env.local` 文件中添加：
```env
DATABASE_URL="postgresql://username:<EMAIL>/bountygo?sslmode=require"
```

---

## 2. 🔐 Clerk Authentication 配置

### 为什么选择 Clerk？
- ✅ **免费额度**: 每月 10,000 活跃用户
- ✅ **开箱即用**: 完整的认证 UI 组件
- ✅ **多种登录**: 邮箱、Google、GitHub 等
- ✅ **安全可靠**: 企业级安全标准

### 配置步骤

#### Step 1: 注册 Clerk 账户
1. 访问 [clerk.com](https://clerk.com)
2. 点击 "Start building for free"
3. 使用邮箱或 GitHub 注册

#### Step 2: 创建应用
1. 登录后点击 "Create application"
2. 填写应用信息：
   - **Application name**: `BountyGo`
   - **Sign-in options**: 选择 Email 和 Google (推荐)
3. 点击 "Create application"

#### Step 3: 获取 API 密钥
1. 在应用 Dashboard 中，点击左侧 "API Keys"
2. 复制以下密钥：
   - **Publishable key**: `pk_test_xxx...`
   - **Secret key**: `sk_test_xxx...`

#### Step 4: 配置环境变量
在 `.env.local` 文件中添加：
```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_xxx..."
CLERK_SECRET_KEY="sk_test_xxx..."
```

#### Step 5: 配置重定向 URL (重要!)
1. 在 Clerk Dashboard 中，点击 "Domains"
2. 添加开发环境域名：`http://localhost:3000`
3. 生产环境部署后，再添加生产域名

---

## 3. 🚀 Vercel 部署配置

### 为什么选择 Vercel？
- ✅ **免费部署**: 个人项目完全免费
- ✅ **自动部署**: Git 推送自动触发部署
- ✅ **全球 CDN**: 访问速度快
- ✅ **Next.js 原生**: 完美支持 Next.js

### 配置步骤

#### Step 1: 注册 Vercel 账户
1. 访问 [vercel.com](https://vercel.com)
2. 点击 "Sign up" 
3. **建议使用 GitHub 账户注册** (方便后续部署)

#### Step 2: 连接 GitHub 仓库
1. 在 Vercel Dashboard 点击 "New Project"
2. 选择你的 BountyGo GitHub 仓库
3. 点击 "Import"

#### Step 3: 配置环境变量
1. 在部署配置页面，展开 "Environment Variables"
2. 添加以下变量：
```
DATABASE_URL = your_neon_database_url
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY = your_clerk_publishable_key  
CLERK_SECRET_KEY = your_clerk_secret_key
```

#### Step 4: 部署
1. 点击 "Deploy" 开始部署
2. 等待 2-3 分钟完成部署
3. 获得生产环境 URL，如：`https://bountygo-xxx.vercel.app`

#### Step 5: 更新 Clerk 域名
1. 回到 Clerk Dashboard
2. 在 "Domains" 中添加 Vercel 生产域名
3. 保存配置

---

## 4. 📁 GitHub 代码托管

### 配置步骤

#### Step 1: 创建 GitHub 仓库
1. 访问 [github.com](https://github.com)
2. 点击 "New repository"
3. 填写仓库信息：
   - **Repository name**: `bountygo`
   - **Description**: `BountyGo - 赏金任务平台 MVP`
   - **Visibility**: Public (推荐) 或 Private

#### Step 2: 推送代码
```bash
# 在项目目录中
git init
git add .
git commit -m "Initial BountyGo MVP"
git branch -M main
git remote add origin https://github.com/yourusername/bountygo.git
git push -u origin main
```

---

## 📋 完整的 .env.local 模板

创建项目根目录下的 `.env.local` 文件：

```env
# Neon Database
DATABASE_URL="postgresql://username:<EMAIL>/bountygo?sslmode=require"

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_xxx..."
CLERK_SECRET_KEY="sk_test_xxx..."

# 可选：应用 URL (生产环境)
NEXT_PUBLIC_APP_URL="https://bountygo-xxx.vercel.app"
```

---

## 🔍 配置验证清单

### ✅ Neon Database
- [ ] 注册 Neon 账户
- [ ] 创建数据库项目
- [ ] 获取连接字符串
- [ ] 运行 `npm run test-db` 验证连接
- [ ] 运行 `npm run setup-db` 创建表

### ✅ Clerk Authentication  
- [ ] 注册 Clerk 账户
- [ ] 创建应用
- [ ] 获取 API 密钥
- [ ] 配置重定向域名
- [ ] 测试登录注册功能

### ✅ Vercel 部署
- [ ] 注册 Vercel 账户
- [ ] 连接 GitHub 仓库
- [ ] 配置环境变量
- [ ] 成功部署到生产环境
- [ ] 更新 Clerk 生产域名

### ✅ GitHub 仓库
- [ ] 创建 GitHub 仓库
- [ ] 推送代码到仓库
- [ ] 配置 Vercel 自动部署

---

## 💰 费用说明

### 完全免费的额度
- **Neon**: 3GB 存储 + 100 小时/月计算时间
- **Clerk**: 10,000 活跃用户/月
- **Vercel**: 100GB 带宽/月 + 无限部署
- **GitHub**: 无限公开仓库

### MVP 阶段完全够用
这些免费额度对于 MVP 验证和早期用户完全足够，只有在用户量大幅增长后才需要考虑付费升级。

---

## 🆘 常见问题

### Q: Neon 数据库连接失败？
A: 检查连接字符串是否正确，确保包含 `?sslmode=require` 参数

### Q: Clerk 登录页面显示错误？
A: 检查域名配置，确保开发和生产环境域名都已添加

### Q: Vercel 部署失败？
A: 检查环境变量是否正确配置，特别是数据库连接字符串

### Q: 本地开发无法连接数据库？
A: 确保 `.env.local` 文件在项目根目录，且变量名完全正确

---

*配置完成后，就可以开始 3-4 小时的 MVP 开发之旅了！*
