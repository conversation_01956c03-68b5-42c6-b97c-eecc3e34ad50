#!/usr/bin/env node

const { exec } = require("child_process");
const fs = require("fs");
const path = require("path");

// Try to load .env.local file manually
try {
  const envPath = path.join(__dirname, "..", ".env.local");
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, "utf8");
    envContent.split("\n").forEach(line => {
      if (line.trim() && !line.startsWith("#")) {
        const equalIndex = line.indexOf("=");
        if (equalIndex > 0) {
          const key = line.substring(0, equalIndex).trim();
          const value = line.substring(equalIndex + 1).trim();
          if (key && value) {
            process.env[key] = value;
          }
        }
      }
    });
  }
} catch (error) {
  console.warn("Warning: Could not load .env.local file");
}

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error("❌ DATABASE_URL not found in environment variables");
  console.error("Please set DATABASE_URL in your .env.local file");
  process.exit(1);
}

const migrationsDir = path.join(__dirname, "..", "migrations");

// Migration files in execution order
const migrations = [
  {
    file: "006_create_tools_tables.sql",
    description: "Create tools and user_tools tables for creator tools management",
  },
];

// Utility function to run SQL command
function runSQL(sqlFile, description) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(migrationsDir, sqlFile);

    if (!fs.existsSync(filePath)) {
      reject(new Error(`Migration file not found: ${filePath}`));
      return;
    }

    console.log(`\n🔄 Running: ${description}`);
    console.log(`📁 File: ${sqlFile}`);

    const command = `psql "${DATABASE_URL}" -f "${filePath}"`;

    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error running ${sqlFile}:`);
        console.error(stderr || error.message);
        reject(error);
        return;
      }

      console.log(`✅ Successfully executed ${sqlFile}`);
      if (stdout) console.log(stdout);
      resolve();
    });
  });
}

// Utility function to run validation query
function runValidation(query, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔍 Validating: ${description}`);

    const command = `psql "${DATABASE_URL}" -c "${query}"`;

    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Validation failed: ${description}`);
        console.error(stderr || error.message);
        reject(error);
        return;
      }

      console.log(`✅ ${description}`);
      console.log(stdout);
      resolve();
    });
  });
}

// Main migration function
async function runMigrations() {
  console.log("🚀 Starting database migrations for CreatorCoin Hive");
  console.log(
    "📊 Database:",
    DATABASE_URL.split("@")[1]?.split("/")[0] || "Unknown"
  );

  try {
    // Test database connection first
    console.log("\n🔗 Testing database connection...");
    await runValidation("SELECT NOW();", "Database connection test");

    // Run migrations in order
    for (const migration of migrations) {
      await runSQL(migration.file, migration.description);
    }

    // Run validations
    console.log("\n🔍 Running post-migration validations...");

    await runValidation(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;",
      "Check created tables"
    );

    await runValidation(
      "SELECT COUNT(*) as constraint_count FROM information_schema.table_constraints WHERE table_schema = 'public' AND constraint_type = 'FOREIGN KEY';",
      "Check foreign key constraints"
    );

    await runValidation(
      "SELECT COUNT(*) as index_count FROM pg_indexes WHERE schemaname = 'public';",
      "Check created indexes"
    );

    console.log("\n🎉 All migrations completed successfully!");
    console.log("\n📋 Next steps:");
    console.log("1. Update your .env.local with Google OAuth credentials");
    console.log("2. Test the authentication flow");
    console.log("3. Start your Next.js development server: npm run dev");
  } catch (error) {
    console.error("\n💥 Migration failed:", error.message);
    console.error("\n🔧 Troubleshooting tips:");
    console.error("1. Verify your DATABASE_URL is correct");
    console.error("2. Check that your database user has CREATE privileges");
    console.error("3. Ensure PostgreSQL client (psql) is installed");
    console.error("4. Check the migration files for syntax errors");
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes("--help") || args.includes("-h")) {
  console.log(`
📖 CreatorCoin Hive Migration Runner

Usage: node scripts/run-migrations.js [options]

Options:
  --help, -h     Show this help message
  --validate     Only run validation queries (skip migrations)
  --rollback     Show rollback instructions

Environment Variables Required:
  DATABASE_URL   PostgreSQL connection string for your Neon database

Examples:
  node scripts/run-migrations.js                # Run all migrations
  node scripts/run-migrations.js --validate     # Only validate existing tables
  node scripts/run-migrations.js --rollback     # Show rollback commands
  `);
  process.exit(0);
}

if (args.includes("--rollback")) {
  console.log(`
🔄 Rollback Instructions

To rollback the migrations, run these SQL commands in order:

1. Drop application tables (due to foreign key dependencies):
   DROP TABLE IF EXISTS profiles CASCADE;

2. Drop NextAuth tables:
   DROP TABLE IF EXISTS verification_tokens CASCADE;
   DROP TABLE IF EXISTS sessions CASCADE; 
   DROP TABLE IF EXISTS accounts CASCADE;
   DROP TABLE IF EXISTS users CASCADE;

3. Drop functions and triggers:
   DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

⚠️  WARNING: This will permanently delete all user data!
   Make sure you have backups before proceeding.

Command to execute rollback:
psql "${DATABASE_URL}" -c "DROP TABLE IF EXISTS profiles CASCADE; DROP TABLE IF EXISTS verification_tokens CASCADE; DROP TABLE IF EXISTS sessions CASCADE; DROP TABLE IF EXISTS accounts CASCADE; DROP TABLE IF EXISTS users CASCADE; DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;"
  `);
  process.exit(0);
}

if (args.includes("--validate")) {
  console.log("🔍 Running validation queries only...");

  runValidation(
    "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;",
    "List all tables"
  )
    .then(() => {
      return runValidation(
        "SELECT tc.table_name, kcu.column_name, ccu.table_name AS foreign_table_name FROM information_schema.table_constraints AS tc JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name JOIN information_schema.constraint_column_usage AS ccu ON ccu.constraint_name = tc.constraint_name WHERE tc.constraint_type = 'FOREIGN KEY';",
        "Check foreign key relationships"
      );
    })
    .then(() => {
      console.log("\n✅ Validation completed successfully!");
    })
    .catch((error) => {
      console.error("\n❌ Validation failed:", error.message);
      process.exit(1);
    });
} else {
  // Run full migrations
  runMigrations();
}
