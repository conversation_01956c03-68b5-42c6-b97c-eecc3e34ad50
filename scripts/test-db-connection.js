const postgres = require("postgres");

async function testConnection() {
  // 检查环境变量
  if (!process.env.DATABASE_URL) {
    console.error("❌ DATABASE_URL environment variable is not set");
    console.log("Please check your .env.local file");
    process.exit(1);
  }

  console.log("🔗 Testing database connection...");
  console.log(
    "Database URL:",
    process.env.DATABASE_URL.replace(/:[^:@]*@/, ":***@")
  );

  const sql = postgres(process.env.DATABASE_URL, {
    ssl: process.env.NODE_ENV === "production" ? "require" : "prefer",
    max: 5,
    idle_timeout: 20,
    connect_timeout: 30,
    onnotice: () => {}, // 忽略 notice 消息
    debug: false,
  });

  try {
    // 测试基本连接
    console.log("⏳ Testing basic connection...");
    const result =
      await sql`SELECT NOW() as current_time, version() as version`;
    console.log("✅ Database connection successful!");
    console.log("Current time:", result[0].current_time);
    console.log("Database version:", result[0].version.split(" ")[0]);

    // 测试 profiles 表是否存在
    console.log("\n⏳ Testing profiles table...");
    try {
      const profileCheck = await sql`SELECT COUNT(*) as count FROM profiles`;
      console.log(
        "✅ Profiles table exists with",
        profileCheck[0].count,
        "records"
      );
    } catch (error) {
      console.log("⚠️  Profiles table might not exist or is not accessible");
      console.log("Error:", error.message);
    }

    // 关闭连接
    await sql.end();
    console.log("\n🎉 Database connection test completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Database connection failed!");
    console.error("Error code:", error.code);
    console.error("Error message:", error.message);

    // 提供解决建议
    console.log("\n💡 Troubleshooting suggestions:");

    if (error.code === "ECONNRESET") {
      console.log("- Check if your database server is running");
      console.log("- Verify your DATABASE_URL is correct");
      console.log("- Check if your IP is whitelisted (for cloud databases)");
    } else if (error.code === "ECONNREFUSED") {
      console.log("- Database server is not accepting connections");
      console.log("- Check the host and port in your DATABASE_URL");
    } else if (error.code === "ENOTFOUND") {
      console.log("- Database host not found");
      console.log("- Check the hostname in your DATABASE_URL");
    } else if (error.message.includes("authentication")) {
      console.log("- Check your database username and password");
      console.log("- Verify credentials in your DATABASE_URL");
    }

    await sql.end();
    process.exit(1);
  }
}

testConnection();
