const { Pool } = require("pg");
require("dotenv").config({ path: ".env.local" });

async function checkTableStructure() {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable is required");
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl:
      process.env.NODE_ENV === "production"
        ? { rejectUnauthorized: false }
        : false,
  });

  console.log("🔍 Checking current table structure...");

  try {
    // Check if profiles table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'profiles'
      )
    `);

    console.log("📋 Table exists:", tableExists.rows[0].exists);

    if (tableExists.rows[0].exists) {
      // Get all columns in profiles table
      const columns = await pool.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_schema = 'public' 
        AND table_name = 'profiles'
        ORDER BY ordinal_position
      `);

      console.log("\n📊 Current columns in profiles table:");
      columns.rows.forEach((col) => {
        console.log(
          `  - ${col.column_name}: ${col.data_type} ${
            col.is_nullable === "NO" ? "NOT NULL" : "NULL"
          } ${col.column_default ? `DEFAULT ${col.column_default}` : ""}`
        );
      });

      // Check indexes
      const indexes = await pool.query(`
        SELECT indexname, indexdef
        FROM pg_indexes
        WHERE tablename = 'profiles'
        AND schemaname = 'public'
      `);

      console.log("\n🗂️ Current indexes:");
      indexes.rows.forEach((idx) => {
        console.log(`  - ${idx.indexname}: ${idx.indexdef}`);
      });
    }
  } catch (error) {
    console.error("❌ Error checking table structure:", error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  checkTableStructure()
    .then(() => {
      console.log("\n✅ Table structure check completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Check failed:", error);
      process.exit(1);
    });
}

module.exports = { checkTableStructure };
