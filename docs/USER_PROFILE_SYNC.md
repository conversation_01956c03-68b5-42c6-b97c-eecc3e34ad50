# 用户资料同步功能

## 概述

本系统实现了用户登录后自动将 Clerk 用户信息同步到 Neon 数据库的功能，确保数据一致性和完整性。

## 同步机制

### 1. Webhook 同步（主要机制）

- **路径**: `/api/webhooks/clerk`
- **触发时机**: Clerk 用户事件（创建、更新、删除）
- **处理事件**:
  - `user.created`: 创建新用户资料
  - `user.updated`: 更新现有用户资料
  - `user.deleted`: 删除用户资料

### 2. 客户端同步（补充机制）

- **路径**: `/api/profile/sync`
- **触发时机**: 用户登录成功后
- **作用**: 确保数据同步，处理 webhook 可能错过的情况

## 文件结构

```
src/
├── app/
│   ├── api/
│   │   ├── profile/
│   │   │   ├── [userId]/route.ts     # 用户资料 CRUD
│   │   │   └── sync/route.ts         # 客户端同步 API
│   │   └── webhooks/
│   │       └── clerk/route.ts        # Clerk webhook 处理
│   └── auth/
│       └── AuthForm.tsx              # 登录表单组件
├── hooks/
│   ├── useProfile.ts                 # 用户资料管理 hook
│   └── useProfileSync.ts             # 用户资料同步 hook
└── services/
    └── profileService.ts             # 数据库操作服务
```

## 使用方式

### 在组件中使用

```tsx
import { useProfileSync } from "@/hooks/useProfileSync";

export default function MyComponent() {
  const { isSyncing, syncResult, syncError, syncProfile } = useProfileSync();

  // 手动触发同步
  const handleSync = async () => {
    try {
      await syncProfile();
      console.log("同步成功");
    } catch (error) {
      console.error("同步失败", error);
    }
  };

  return (
    <div>
      {isSyncing && <div>正在同步...</div>}
      {syncError && <div>同步失败: {syncError}</div>}
      {syncResult && <div>同步成功: {syncResult.message}</div>}
      <button onClick={handleSync}>手动同步</button>
    </div>
  );
}
```

### API 调用

```typescript
// 同步当前用户资料
const response = await fetch("/api/profile/sync", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
});

const result = await response.json();
console.log(result); // { success: true, message: "...", profile: {...} }
```

## 数据流程

1. **用户注册/登录** → Clerk
2. **Clerk 触发 webhook** → `/api/webhooks/clerk`
3. **处理用户事件** → 数据库操作
4. **客户端登录成功** → `useProfileSync` 自动触发
5. **调用同步 API** → `/api/profile/sync`
6. **确认数据一致性** → 完成同步

## 错误处理

- **Webhook 失败**: 客户端同步作为备用机制
- **网络错误**: 自动重试机制（计划中）
- **数据库错误**: 记录日志并返回错误信息
- **认证失败**: 返回 401 错误

## 配置要求

### 环境变量

```env
# Clerk 配置
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_...
CLERK_SECRET_KEY=sk_...
CLERK_WEBHOOK_SECRET=whsec_...

# 数据库配置
DATABASE_URL=postgresql://...
```

### Clerk Webhook 设置

1. 登录 Clerk Dashboard
2. 进入 Webhooks 设置
3. 添加新的 endpoint: `https://your-domain.com/api/webhooks/clerk`
4. 选择事件: `user.created`, `user.updated`, `user.deleted`
5. 复制 webhook secret 到环境变量

## 监控和调试

### 日志输出

```bash
# 成功同步
Created profile for user: user_xxx
Updated profile for user: user_xxx

# 错误信息
Error verifying webhook: ...
Profile sync error: ...
```

### 数据库查询

```sql
-- 查看最近创建的用户
SELECT * FROM profiles ORDER BY created_at DESC LIMIT 10;

-- 查看最近登录的用户
SELECT clerk_user_id, full_name, last_login_at
FROM profiles
WHERE last_login_at IS NOT NULL
ORDER BY last_login_at DESC;
```

## 性能优化

- **批量操作**: 处理大量用户时使用批量插入
- **缓存机制**: 减少重复数据库查询
- **异步处理**: Webhook 和客户端同步都是异步的
- **错误恢复**: 失败的同步会在下次登录时重试

## 安全考虑

- **Webhook 验证**: 使用 Clerk 提供的签名验证
- **用户权限**: 只允许用户访问自己的资料
- **数据加密**: 敏感信息在传输和存储时加密
- **审计日志**: 记录所有数据操作用于审计
