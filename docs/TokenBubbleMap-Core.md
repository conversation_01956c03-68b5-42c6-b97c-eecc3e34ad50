# TokenBubbleMap 核心功能与实现方案

## 1. 核心功能

TokenBubbleMap 是一个交互式代币资产可视化组件，主要功能包括：

1. **资产可视化**：通过泡泡大小直观展示不同代币的价值占比
2. **交互操作**：支持拖拽、碰撞和选中等交互方式
3. **物理模拟**：实现基础的物理引擎，使泡泡之间能相互碰撞和反弹
4. **视觉反馈**：提供动画和特效，增强用户体验

## 2. 实现方案概要

### 2.1 组件架构

采用组件化设计，主要包含：

- **主容器组件**：管理状态和渲染
- **泡泡组件**：处理单个泡泡的渲染和交互
- **碰撞管理系统**：处理物理模拟和碰撞逻辑

### 2.2 主要实现思路

1. **物理碰撞系统**

   - 使用基础物理模型计算碰撞和位移
   - 基于欧几里得距离进行碰撞检测
   - 应用动量守恒原理计算碰撞后的运动
   - 使用摩擦力模拟泡泡减速效果

2. **视觉表现**

   - 使用渐变色和透明度创建视觉层次
   - 应用 CSS 变换控制位置和大小
   - 动画效果包括浮动、碰撞反馈和选中状态

3. **交互设计**

   - 使用拖拽 API 实现泡泡拖拽功能
   - 碰撞检测和反馈
   - 泡泡选择和信息展示

### 2.3 布局算法

泡泡初始布局采用黄金螺旋分布，确保均匀分布并避免初始重叠：

- 使用黄金比例计算角度
- 基于容器尺寸调整分布半径
- 根据代币价值计算泡泡大小

## 3. 可能的实现方案

### 3.1 主要技术选择

1. **React 实现**

   - 使用函数组件和 hooks
   - 利用 Framer Motion 处理动画

2. **Canvas 实现**

   - 使用 HTML5 Canvas 或 WebGL 渲染
   - 可能提供更好的性能，尤其是大量泡泡时

3. **SVG 实现**
   - 使用 SVG 元素和动画
   - 适合需要精确控制的场景

### 3.2 物理引擎选择

1. **自定义简易物理系统**

   - 实现基础的碰撞检测和响应
   - 适合泡泡数量较少的情况

2. **专业物理引擎集成**

   - 可考虑使用 Matter.js 或 Box2D 等
   - 提供更真实、复杂的物理效果
   - 适合需要高级物理效果的场景

3. **WebWorker 物理计算**
   - 将物理计算放在单独线程
   - 避免阻塞 UI 渲染，提升性能

### 3.3 性能优化方向

1. **空间分区优化**

   - 使用网格或四叉树空间分区
   - 减少碰撞检测次数

2. **按需渲染**

   - 只渲染视口内的泡泡
   - 适合大量泡泡的情况

3. **计算优化**
   - 使用近似计算替代精确计算
   - 减少不必要的状态更新

## 4. 数据接口

核心数据接口应包含：

```typescript
// 组件属性接口
interface TokenBubbleMapProps {
  data: TokenData[]; // 代币数据
  width?: number; // 可选的容器宽度
  height?: number; // 可选的容器高度
  onSelect?: (token: TokenData) => void; // 选择回调
}

// 代币数据接口
interface TokenData {
  symbol: string; // 代币符号
  value: number; // 代币价值
  color?: string; // 可选的自定义颜色
}
```

## 5. 扩展思路

可考虑的功能扩展：

1. **聚类效果**

   - 相似代币自动聚集
   - 可基于类别或其他属性

2. **时间序列动画**

   - 展示代币价值随时间变化
   - 支持回放和预测

3. **多维数据展示**

   - 使用颜色、透明度等属性展示更多维度的数据
   - 例如代币波动率、交易量等

4. **交互增强**
   - 泡泡合并/分裂效果
   - 多选和比较功能
   - 缩放和平移操作
