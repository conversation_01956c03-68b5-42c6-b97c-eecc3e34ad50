## 1. 产品概述

### 1.1 产品愿景

构建一个去中心化的 Web3 创作者平台，通过任务驱动和代币激励机制，连接项目方与创作者，打造高效的内容创作与项目推广生态。

### 1.2 核心价值主张

- 为项目方提供高效的内容营销和社区建设解决方案
- 为创作者提供稳定的收入来源和成长路径
- 通过代币经济模型实现多方共赢

> 说明：平台统一使用 USDT 作为所有奖励与交易结算通用代币。

## 2. 用户角色与权限

### 2.1 创作者

### 2.1.1 普通创作者

- **准入条件**：完成基础注册和社交账号绑定
- **核心权益**：
  - 参与普通任务
  - 积累经验值和 USDT 奖励

### 2.1.2 超级创作者

- **准入条件**：
  - YouTube 粉丝数 ≥ 1000，视频数量 10-20
  - 每 60 天可申请一次
  - 平台审核通过
- **专属权益**：
  - 参与悬赏任务
  - 专属客服支持
  - 查看详细数据分析
  - 平台推荐曝光

### 2.2 项目方

- **准入条件**：完成项目认证
- **核心功能**：
  - 发布和管理任务
  - 设置任务奖励预算（USDT）
  - 审核任务完成情况
  - 查看任务效果数据

### 2.3 业务流程与用户旅程

以下流程以普通创作者和项目方两个主角色为主线，描述从注册到奖励发放的关键步骤：

### 2.3.1 普通创作者旅程

1. **账号注册与绑定**
   - 用户填写邮箱/手机号，设置密码
   - **可选任务：平台与工具熟悉**（注册后推荐完成）：
   - **美国 Apple ID**：免费注册美区 Apple ID，无需信用卡，支持台湾手机号。用于下载美区限定应用/游戏或礼品卡充值。
   - **Google 帐号**：建议使用全新 Gmail，便于管理与其他服务绑定。
   - **Twitter（推特）**：注册并绑定，用于社交分享。
   - **Discord**：注册并加入官方服务器，参与社区互动。
   - **加密货币钱包（MetaMask）**：安装并创建钱包，用于接收 USDT 奖励。
   - **YouTube 帐号**：确认登录并绑定，便于视频任务自动关联。
   - **Telegram**：注册并加入公告频道，获取最新通知。
2. **任务浏览与领取**
   - 在"任务市场"查看可参与的普通任务列表
   - 根据过滤条件（类型、报酬、截止时间）筛选 → 点击"领取"
3. **内容创作与提交通知**
   - 根据任务要求完成短视频、文案或社区互动
   - 上传作品素材，并填写提交说明
4. **审核与反馈**
   - 平台或项目方在 24–48 小时内完成初审
   - 如需修改，创作者收到反馈并按要求补充/修改后重新提交
5. **奖励发放**
   - 审核通*过后，系统自动将 USDT 发放至创作者链上钱包*
   - _创作者可在"资产管理"中查看余额和交易记录_
6. **经验值与成长**
   - 每完成一次任务，获得固定经验值；内容质量优秀可获得额外经验
   - 经验值累积至一定等级后，可解锁会员特权或申请升级为超级创作者

### 2.3.2 项目方旅程

1. **项目认证**
   - 填写项目基本信息、社交链接及认证材料 → 平台审核
2. **发布任务**
   - 进入"发布中心"设定任务类型（普通/悬赏）、详情、预算（USDT）和时间
3. **任务管理**
   - 实时监控任务领取与提交情况
   - 对提交内容进行审核（通过/退回）并给出反馈
4. **效果评估**
   - 在"效果数据"模块查看任务完成量、创作者分布、互动数据等
5. **费用结算**
   - 审核通过的任务费用（USDT）由智能合约自动扣款并支付给创作者

> 流程图建议：
>
> - 使用 Swimlane 或时序图展示两条旅程中各角色的交互和时序。
> - 标注各环节期望时长及异常处理机制（如 3 次审核超时自动通过）。

## 3. 核心功能模块

### 3.1 任务系统

### 3.1.1 普通任务

- **任务类型**：短视频创作 / 项目推广 / 社区互动
- **奖励机制**：
  - 经验值：10 点/任务
  - USDT 奖励：1–3 USDT/任务
  - 完成次数限制：100–10,000 次

### 3.1.2 悬赏任务

- **任务类型**：深度内容创作 / 项目分析报告 / 社区运营方案
- **奖励机制**：
  - 基础奖励：500 USDT / 1,000 USDT / 3,000 USDT
  - 平台抽成：30%
  - 未入选补偿：10–30 USDT

### 3.2 创作者成长体系

#### 3.2.1 经验值(XP)系统

- **基础经验值获取**：

  - 普通任务完成：10 XP/任务
  - 悬赏任务完成：50 XP/任务
  - 每日签到：5 XP/天（连续签到奖励递增）
  - 社区互动：1-5 XP/次（评论、点赞、分享）

- **质量加成系数**：

  - 内容质量优秀：1.5x 基础经验值
  - 内容质量良好：1.2x 基础经验值
  - 内容质量一般：1.0x 基础经验值
  - 内容质量较差：0.8x 基础经验值

- **等级系统**：
  | 等级 | 所需经验值 | 解锁特权 |
  |------|------------|----------|
  | 1 | 0-100 | 基础任务参与资格 |
  | 2 | 101-300 | 每日任务上限+5 |
  | 3 | 301-600 | 优先任务推荐 |
  | 4 | 601-1000 | 任务奖励加成 5% |
  | 5 | 1001-1500 | 超级创作者申请资格 |
  | 6 | 1501-2100 | 专属客服支持 |
  | 7 | 2101-2800 | 任务奖励加成 10% |
  | 8 | 2801-3600 | 高级数据分析工具 |
  | 9 | 3601-4500 | 平台推荐曝光 |
  | 10 | 4501+ | 平台合伙人资格 |

- **经验值衰减机制**：

  - 连续 30 天未活跃：每日衰减 1%经验值
  - 连续 60 天未活跃：每日衰减 2%经验值
  - 连续 90 天未活跃：每日衰减 3%经验值
  - 重新活跃后停止衰减

- **特殊成就系统**：
  - 连续完成 10 个任务：额外奖励 50 XP
  - 月度最佳创作者：额外奖励 100 XP
  - 季度最佳创作者：额外奖励 300 XP
  - 年度最佳创作者：额外奖励 1000 XP

#### 3.2.2 会员特权

- **基础会员（免费）**
- **高级会员（20 USDT/季度）**：金色名字、优先展示、高级工具、专属数据分析

### 3.3 平台经济模型

- **普通任务奖励**：1–3 USDT/次，平台不抽成，Gas 费由项目方承担
- **悬赏任务奖励**：500–3,000 USDT，平台抽成 30%，未入选补偿 10–30 USDT
- **会员付费**：高级会员 20 USDT/季度

### 3.4 代币资产池

代币池是项目方提供的激励池子，用于平台任务的奖励发放，用户可以直观看到平台当前所有任务的奖励剩余额度。

#### 3.4.1 首页展示

- **资产总览卡片**

  - 显示当前总资产（USD 折合）
  - 24 小时变化百分比
  - 支持的主要币种图标（USDT/USDC/ETH/MATIC/BNB）
  - 简洁的饼图显示各币种占比

- **资金流向卡片**

  - 24 小时流入/流出金额
  - 最近 5 笔交易记录
  - 交易状态标识（成功/处理中/失败）

- **任务资金池卡片**
  - 当前可用任务奖励总额
  - 各类型任务资金占比
  - 资金使用进度条

#### 3.4.2 资产池详情

- **总代币资产**

  - 显示所有代币的 USD 折合价值
  - 支持多币种实时汇率转换
  - 24 小时资产变化趋势

- **币种分类显示**

  - 支持主流代币：
    - USDT (ERC20/BEP20/Polygon)
    - USDC (ERC20/BEP20/Polygon)
    - ETH
    - MATIC
    - BNB
  - 每个币种显示：
    - 当前余额
    - 24 小时流入/流出量

- **链上地址管理**
  - 多链钱包地址展示
  - 区块链浏览器链接
  - 交易历史查询

#### 3.4.3 资金流向

- **奖励发放记录**

  - 任务奖励发放明细
  - 发放时间
  - 接收方地址
  - 交易状态

- **资金流入记录**
  - 项目方充值记录
  - 充值时间
  - 充值金额
  - 交易状态

### 3.5 创作者公开主页

- 每位创作者拥有独立的公开主页，便于合作方了解其内容与合作意向。
- 主页内容包括：
  - 头像、昵称、简介/签名
  - 合作方式与收费标准（如按次、按月等）
  - 最近视频/作品列表（缩略图、标题、播放量、发布时间等）
  - 主要成就/荣誉
  - 账号认证标识（如已认证 YouTube、Twitter 等）
- 主页不展示敏感信息（如钱包地址、联系方式等），仅本人可见编辑入口。
- 合作方可通过主页快速了解创作者实力与合作价格，提升撮合效率。

## 4. 技术实现要点

- **智能合约**：USDT 支付集成、自动奖励发放、审核与发放逻辑、治理投票
- **数据安全**：用户数据加密、交易上链、合约审计、权限管理
- **基础设施**：AWS Amplify + CloudFront + IPFS 存储，CI/CD、监控日志、异常预案

## 5. UI/UX 设计规范

### 5.1 视觉风格

- **色彩系统**：

  - 主色调：渐变紫色 (#4A148C 到 #311B92)
  - 强调色：青色系 (#00CEC9)
  - 辅助色：YouTube 红 (#ff0000)
  - 背景：半透明效果 (backdrop-blur)

- **游戏化元素**：
  - 像素风格装饰
  - 动态背景效果
  - 流动进度条
  - 悬浮和点击动画

### 5.2 交互设计

- **动画效果**：

  - 页面加载动画
  - 按钮悬浮缩放
  - 进度条流动效果
  - 状态切换过渡

- **布局规范**：
  - 卡片式设计
  - 清晰的视觉层级
  - 响应式网格布局
  - 固定位置导航

### 5.3 组件规范

- **卡片组件**：

  - 圆角设计
  - 半透明背景
  - 悬浮效果
  - 动态装饰元素

- **按钮组件**：
  - 主按钮：青色渐变
  - 次要按钮：半透明
  - 图标按钮：圆形设计
  - 悬浮动画效果

## 6. 运营策略

- **用户获取**：社交媒体、社区合作、项目资源对接、USDT 空投
- **用户留存**：定期任务更新、培训计划、社区活动、USDT 价值维护

## 7. 发展路线图

| 阶段            | 时间     | 关键里程碑                                        |
| --------------- | -------- | ------------------------------------------------- |
| 第一阶段（MVP） | 2025 Q2  | 普通任务系统上线、创作者入驻、基础数据分析        |
| 第二阶段        | 2025 Q3  | 超级创作者计划内测、高级工具、社区治理、跨链支持  |
| 第三阶段        | 2025 Q4+ | AI 创作助手、元宇宙展示、去中心化存储、跨平台整合 |
