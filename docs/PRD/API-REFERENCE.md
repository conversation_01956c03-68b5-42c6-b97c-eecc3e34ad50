# 服务与报价模块 API 参考文档

## 📋 目录

1. [概述](#概述)
2. [后端API接口](#后端api接口)
3. [前端API服务](#前端api服务)
4. [TypeScript类型定义](#typescript类型定义)
5. [使用示例](#使用示例)

---

## 概述

服务与报价模块提供6个RESTful接口和TypeScript客户端，支持服务的完整CRUD操作。

### 核心特性
- 🔐 **Clerk认证** - 创作者接口需要认证，公开接口无需认证
- 🛡️ **类型安全** - 完整的TypeScript类型定义  
- 📝 **数据验证** - 严格的输入验证 (title: 1-100字符, description: 1-500字符)
- 🎯 **服务模板** - 4个预定义模板快速创建服务



## 后端API接口

| 方法 | 路径 | 功能 | 认证 |
|------|------|------|------|
| GET | `/api/services/my-services` | 获取我的服务 | ✅ |
| POST | `/api/services` | 创建服务 | ✅ |
| PUT | `/api/services/:id` | 更新服务 | ✅ |
| PATCH | `/api/services/:id/status` | 切换状态 | ✅ |
| DELETE | `/api/services/:id` | 删除服务 | ✅ |
| GET | `/api/public/creators/:id/services` | 获取公开服务 | ❌ |

### 统一响应格式
```json
{
  "success": true,
  "data": { /* 具体数据 */ },
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述"
  }
}
```

### 常见错误码
| 状态码 | 错误码 | 描述 |
|--------|--------|------|
| 401 | UNAUTHORIZED | 未认证 |
| 404 | NOT_FOUND | 资源不存在 |
| 400 | VALIDATION_ERROR | 参数验证失败 |
| 409 | DUPLICATE_SERVICE | 服务标题重复 |

---

## 前端API服务

### ServicesApiService 使用

```typescript
import { servicesApi } from '@/services/servicesApi';

// 基础操作
await servicesApi.createService(data)
await servicesApi.getMyServices()
await servicesApi.updateService(id, data)
await servicesApi.deleteService(id)

// 便捷方法
await servicesApi.toggleServiceStatus(id, currentStatus)
await servicesApi.getMyServicesByType('embedded')
await servicesApi.deleteMultipleServices(['id1', 'id2'])
```

### 服务模板

系统提供4个预定义模板供快速创建：

```typescript
import { getTemplatesByType, findTemplateByTitle } from '@/constants/service-templates';

// 获取模板
const templates = getTemplatesByType('embedded');
const template = findTemplateByTitle('短视频口播');

// 基于模板创建
const service = await servicesApi.createService({
  type: 'embedded',
  ...template
});
```

**可用模板**:
- **短视频口播** (¥300-600) - 植入推广
- **社交图文** (¥200-400) - 植入推广  
- **定制视频** (¥1000-2500) - 定制推广
- **品牌合作** (¥1500-3000) - 定制推广

---

## TypeScript类型定义

```typescript
// 基础类型
export type ServiceType = 'embedded' | 'custom';
export type ServiceStatus = 'active' | 'inactive';

// 核心数据模型
export interface Service {
  id: string;
  type: ServiceType;
  title: string;
  description: string;
  priceMin: number;
  priceMax: number;
  status: ServiceStatus;
  createdAt: string;
  updatedAt: string;
}

// API请求/响应类型
export interface CreateServiceRequest {
  type: ServiceType;
  title: string;      // 1-100字符
  description: string; // 1-500字符  
  priceMin: number;    // >= 0
  priceMax: number;    // >= priceMin
}

export interface MyServicesResponse {
  embedded: Service[];
  custom: Service[];
}

// 服务模板
export interface ServiceTemplate {
  title: string;
  description: string;
  priceMin: number;
  priceMax: number;
}
```

## 使用示例

### 完整流程
```typescript
import { servicesApi } from '@/services/servicesApi';
import { findTemplateByTitle } from '@/constants/service-templates';

// 1. 基于模板创建服务
const template = findTemplateByTitle('短视频口播');
const service = await servicesApi.createService({
  type: 'embedded',
  ...template
});

// 2. 管理服务
const services = await servicesApi.getMyServices();
await servicesApi.updateService(service.id, { priceMax: 800 });
await servicesApi.toggleServiceStatus(service.id, service.status);
await servicesApi.deleteService(service.id);
```

### 错误处理
```typescript
try {
  const service = await servicesApi.createService(data);
} catch (error) {
  console.error('创建失败:', error.message);
  // 常见错误: 认证失败、参数验证失败、标题重复
}
```

---

**测试页面**: 访问 `/test-api` 进行手动测试和调试 