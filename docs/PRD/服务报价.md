# 服务与报价编辑模块 PRD

## 一、背景与目标

* **背景**：平台聚合多领域创作者，需为品牌方、项目方提供标准化、可比的服务与报价展示。现有板块过于简陋，限制了创作者个性化配置与报价发布能力。
* **目标**：在 MVP 阶段快速上线「服务与报价编辑模块」，实现创作者自由配置官方标准化服务项并在个人主页展示，提升撮合效率。

---

## 二、MVP 范围

### 1. 功能模块

#### 1.1 服务列表管理

* 创作者可查看当前已配置的服务列表，初始为空。
* 列表按「植入推广」「定制推广」两类切换标签。
* 每行显示：服务名称、价格区间、状态（上架/下架）、操作按钮【编辑】【删除】。

#### 1.2 新增 / 编辑服务

* **表单字段**（简化为5个核心字段）：

  * 服务类型：植入推广 / 定制推广（单选）
  * 服务名称（必填）
  * 简短描述（必填）
  * 价格区间：最小价格 - 最大价格（必填，统一USDT）
  * 状态：上架/下架（默认上架）
* **操作**：保存后默认为「上架」状态；支持状态切换。

#### 1.3 前台展示

* 在创作者主页「服务与报价」区块展示，分两列卡片式布局：

  * 植入推广
  * 定制推广
* 卡片内容：服务名称、描述、价格区间、【立即咨询】按钮。

#### 1.4 默认服务模板（MVP 提供）
在平台上线初期，为帮助创作者快速配置，系统内置以下常见服务模板，创作者可一键添加并修改：

| 模板名称     | 类型   | 描述                    | 参考价格 (USDT) |
| -------- | ---- | --------------------- | ----------- |
| 短视频口播    | 植入推广 | 60-90秒品牌宣传视频          | 300 - 600   |
| 社交图文     | 植入推广 | Twitter/小红书图文推广      | 200 - 400   |
| 定制视频     | 定制推广 | 脚本+拍摄+剪辑完整制作         | 1000 - 2500 |
| 品牌合作     | 定制推广 | 深度品牌内容合作            | 1500 - 3000 |

以上模板均支持编辑后保存为私有配置。

### 2. 数据模型

#### PostgreSQL Schema (简化版)

```sql
CREATE TABLE services (
  id UUID PRIMARY KEY,
  creator_id UUID NOT NULL REFERENCES creators(id),
  type VARCHAR(10) NOT NULL CHECK (type IN ('embedded','custom')),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  price_min NUMERIC(10,2) NOT NULL,
  price_max NUMERIC(10,2) NOT NULL,
  status VARCHAR(10) NOT NULL DEFAULT 'active' CHECK (status IN ('active','inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### API 数据结构

```json
{
    "creatorId": "string",
    "services": [
        {
            "id": "string",
            "type": "embedded | custom",
            "title": "string",
            "description": "string",
            "priceMin": number,
            "priceMax": number,
            "status": "active | inactive"
        }
    ]
}

```

### 3. 用户流程 (MVP)
1. 创作者进入「我的服务」→ 列表页  
2. 点击【新增服务】→ 填写表单 → 点击【保存】  
3. 列表中呈现新增项；可【编辑】或【删除】  
4. 前台个人主页「服务与报价」同步更新  
5. 访客点击【立即咨询】触发品牌方私信或简易咨询表单（MVP 可跳转到统一联系方式页）

---

## 三、UI 原型要点

- 列表页：两标签页、表格+操作按钮  
- 表单页：分步或单页显示核心字段，右侧静态示例卡片  
- 主页区块：卡片化组件、简洁明了

---

## 四、成功指标

- **配置率**：80% 新入驻创作者在 7 天内完成至少 1 个服务配置  
- **咨询触达率**：创作者主页访问用户点击「立即咨询」转化率 ≥ 5%  
- **使用满意度**：内测期创作者满意度 ≥ 4.0（5 分制）

---

## 五、风险与里程碑

| 阶段        | 时间         | 输出                     |
|-------------|-------------|--------------------------|
| 需求评审    | Week 1      | PRD 完成、UI Mock       |
| 开发迭代    | Week 2–3    | API + 后台页面 + 前台卡片|
| 测试 & 内测  | Week 4      | 修复 & 内部反馈           |
| 上线 MVP    | Week 5      | 全量发布                 |

---

*End of MVP PRD*

```
