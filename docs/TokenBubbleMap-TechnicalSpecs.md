# TokenBubbleMap 技术规范文档

## 1. 概述

TokenBubbleMap 是一个交互式代币资产可视化组件，通过动态泡泡图展示代币池的分布情况。它不仅提供直观的数据展示，还实现了物理碰撞系统，使泡泡之间能够相互作用，提供沉浸式的用户体验。

### 1.1 设计原则

- **直观性**：通过泡泡大小直观反映代币价值占比
- **交互性**：支持拖拽、碰撞和选中等多种交互方式
- **物理真实感**：实现基于物理原理的碰撞和运动系统
- **视觉吸引力**：提供渐变色彩、动画效果和反馈机制
- **响应式设计**：自适应不同屏幕尺寸和设备

## 2. 架构设计

### 2.1 组件结构

TokenBubbleMap 采用了模块化设计，主要包含以下组件：

1. **TokenBubbleMap**：主容器组件，管理整体状态和渲染
2. **TokenBubble**：单个泡泡组件，处理泡泡的渲染和交互
3. **CollisionEffect**：碰撞特效组件，展示碰撞视觉反馈
4. **CollisionSystem**：物理碰撞管理系统，处理所有泡泡的位置和碰撞逻辑

### 2.2 数据流

```
数据输入 → TokenBubbleMap → CollisionSystem ↔ TokenBubble → 视觉渲染
                                 ↓
                          CollisionEffect
```

### 2.3 技术栈

- React (Hooks, Functional Components)
- TypeScript
- Framer Motion (动画库)
- CSS-in-JS (Tailwind CSS)

## 3. 数据结构

### 3.1 主要接口定义

```typescript
// 组件属性接口
interface TokenBubbleMapProps {
  data: TokenPoolData;
  maxBubbles?: number;
  minBubbleSize?: number;
  maxBubbleSize?: number;
  containerHeight?: number;
  onBubbleClick?: (token: TokenData) => void;
}

// 代币池数据结构
interface TokenPoolData {
  totalAssets: number;
  change24h: number;
  tokens: TokenData[];
  recentTransactions: Transaction[];
  taskPool: TaskPool;
}

// 代币数据
interface TokenData {
  symbol: string;
  amount: number;
  usdValue: number;
  percentage: number;
  change24h?: number;
}

// 泡泡位置数据
interface BubblePosition {
  symbol: string;
  x: number;
  y: number;
  size: number;
  velocity: { x: number; y: number };
  lastUpdate: number;
}

// 碰撞事件数据
interface CollisionEvent {
  symbol1: string;
  symbol2: string;
  timestamp: number;
  position: { x: number; y: number };
}
```

## 4. 物理碰撞系统

CollisionSystem 是整个组件的核心，实现了物理碰撞模拟和运动逻辑。

### 4.1 碰撞检测算法

```typescript
// 碰撞检测函数
const detectCollision = (
  bubble1: BubblePosition,
  bubble2: BubblePosition
): boolean => {
  const dx = bubble1.x - bubble2.x;
  const dy = bubble1.y - bubble2.y;
  const distance = Math.sqrt(dx * dx + dy * dy);
  const minDistance = (bubble1.size / 2 + bubble2.size / 2) * 0.8;
  return distance < minDistance;
};
```

### 4.2 碰撞响应计算

```typescript
// 计算碰撞后的位移
const calculateCollisionDisplacement = (
  bubble1: BubblePosition,
  bubble2: BubblePosition
): { bubble1: { x: number; y: number }; bubble2: { x: number; y: number } } => {
  // 计算距离
  const dx = bubble1.x - bubble2.x;
  const dy = bubble1.y - bubble2.y;
  const distance = Math.sqrt(dx * dx + dy * dy);
  const minDistance = (bubble1.size / 2 + bubble2.size / 2) * 0.8;

  if (distance >= minDistance)
    return { bubble1: { x: 0, y: 0 }, bubble2: { x: 0, y: 0 } };

  // 防止除零错误
  if (distance === 0) {
    return {
      bubble1: { x: Math.random() * 2 - 1, y: Math.random() * 2 - 1 },
      bubble2: { x: Math.random() * -2 + 1, y: Math.random() * -2 + 1 },
    };
  }

  // 计算重叠程度
  const overlap = minDistance - distance;

  // 计算方向向量
  const nx = dx / distance;
  const ny = dy / distance;

  // 根据泡泡大小比例分配位移
  const totalSize = bubble1.size + bubble2.size;
  const ratio1 = bubble2.size / totalSize;
  const ratio2 = bubble1.size / totalSize;

  return {
    bubble1: {
      x: nx * overlap * ratio1,
      y: ny * overlap * ratio1,
    },
    bubble2: {
      x: -nx * overlap * ratio2,
      y: -ny * overlap * ratio2,
    },
  };
};
```

### 4.3 物理模拟系统

CollisionSystem 类处理以下关键功能：

1. **位置管理**：追踪和更新所有泡泡的位置
2. **碰撞检测**：检测泡泡之间的碰撞
3. **物理更新**：应用物理规则（速度、摩擦力、边界约束）
4. **事件派发**：触发碰撞事件并通知系统

关键物理参数：

- 摩擦系数：0.95（每帧速度衰减 5%）
- 反弹系数：0.8（撞击边界后速度保留 80%）
- 碰撞冷却：500ms（防止短时间内重复碰撞）

### 4.4 物理更新循环

使用 requestAnimationFrame 实现物理更新循环：

```typescript
// 启动物理模拟循环
useEffect(() => {
  // 每帧更新物理模拟
  const updatePhysics = () => {
    if (collisionSystemRef.current) {
      collisionSystemRef.current.applyPhysics(containerSize);
    }

    // 继续下一帧
    animationFrameRef.current = requestAnimationFrame(updatePhysics);
  };

  // 启动动画循环
  animationFrameRef.current = requestAnimationFrame(updatePhysics);

  return () => {
    // 清理动画帧
    if (animationFrameRef.current !== null) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  };
}, [containerSize]);
```

## 5. 视觉效果与动画

### 5.1 泡泡外观

每个泡泡包含多层视觉效果：

1. **背景层**：渐变背景色
2. **内容层**：代币符号和价值
3. **特效层**：碰撞波纹、选中状态高亮
4. **边框层**：细微边框增强深度感

### 5.2 动画效果

主要动画效果包括：

1. **浮动动画**：随机幅度和时间的垂直浮动

   ```typescript
   const generateFloatAnimation = () => {
     const amplitude = Math.random() * 3 + 2;
     return {
       y: [0, amplitude, -amplitude, 0],
       transition: {
         y: {
           repeat: Infinity,
           duration: 4 + Math.random() * 2,
           ease: "easeInOut",
           repeatType: "mirror",
         },
       },
     };
   };
   ```

2. **碰撞特效**：泡泡碰撞时的波纹扩散效果

   ```typescript
   <motion.div
     initial={{ scale: 0, opacity: 0.8 }}
     animate={{
       scale: [0, 3, 5],
       opacity: [0.8, 0.4, 0],
     }}
     transition={{ duration: 0.6 }}
   />
   ```

3. **拖拽反馈**：拖拽时的视觉指示器
4. **选中效果**：选中状态的高亮动画
5. **弹性边界**：拖拽到边界的弹性效果

### 5.3 布局算法

泡泡的初始布局使用黄金螺旋比例：

```typescript
// 计算初始位置
const position = useMemo(() => {
  const phi = (1 + Math.sqrt(5)) / 2; // 黄金比例
  const angle = index * phi * Math.PI * 2;
  const radiusFactor = 0.35;
  const maxRadius =
    Math.min(containerSize.width, containerSize.height) * radiusFactor;
  const radius = Math.min(maxRadius, 60 + (index % 8) * 25);

  return {
    offsetX: Math.cos(angle) * radius,
    offsetY: Math.sin(angle) * radius,
  };
}, [index, containerSize]);
```

## 6. 性能优化

### 6.1 渲染优化

1. **组件记忆化**：使用 React.memo 减少不必要的重渲染
2. **计算缓存**：使用 useMemo 和 useCallback 缓存计算结果
3. **变换优化**：使用 CSS transform 而非位置属性

### 6.2 计算优化

1. **碰撞对缓存**：记录最近的碰撞对，避免重复检测
2. **增量更新**：只在数值变化明显时更新状态
3. **空间划分**：对大量泡泡时可实现空间分区算法

### 6.3 动画性能

1. **硬件加速**：使用 `will-change` 提示浏览器使用硬件加速
2. **节流更新**：限制容器尺寸更新频率
3. **按需动画**：只在必要时启动动画效果

## 7. 自定义配置

TokenBubbleMap 支持以下配置选项：

| 属性            | 类型          | 默认值                 | 说明             |
| --------------- | ------------- | ---------------------- | ---------------- |
| data            | TokenPoolData | 必须                   | 代币池数据       |
| maxBubbles      | number        | 10                     | 最大显示泡泡数量 |
| minBubbleSize   | number        | 60                     | 最小泡泡尺寸(px) |
| maxBubbleSize   | number        | containerWidth \* 0.25 | 最大泡泡尺寸(px) |
| containerHeight | number        | 400                    | 容器高度(px)     |
| onBubbleClick   | function      | undefined              | 泡泡点击回调     |

## 8. 实现注意事项

### 8.1 边界处理

边界碰撞需要考虑泡泡半径：

```typescript
// 边界处理
const maxX = containerSize.width / 2 - bubble.size / 2;
const maxY = containerSize.height / 2 - bubble.size / 2;

if (Math.abs(newX) > maxX) {
  newX = Math.sign(newX) * maxX;
  bubble.velocity.x = -bubble.velocity.x * bounceFactor;
}
```

### 8.2 事件处理

拖拽结束后需要计算最终速度并应用到物理系统：

```typescript
onDragEnd={() => {
  onSelect("");

  const now = Date.now();
  const deltaTime = now - lastUpdateRef.current;

  if (deltaTime > 0) {
    // 计算最终速度
    const velocityX = ((dragPosition.x - lastPositionRef.current.x) / deltaTime) * 20;
    const velocityY = ((dragPosition.y - lastPositionRef.current.y) / deltaTime) * 20;

    // 更新位置到碰撞系统
    collisionSystem.updateBubblePosition({
      symbol: token.symbol,
      x: position.offsetX + dragPosition.x,
      y: position.offsetY + dragPosition.y,
      size,
      velocity: { x: velocityX, y: velocityY },
      lastUpdate: now,
    });
  }
}}
```

### 8.3 布局适配

初始布局需要考虑容器尺寸变化：

```typescript
// 监听容器尺寸变化
useEffect(() => {
  const updateContainerSize = () => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      if (
        Math.abs(rect.width - containerSize.width) > 5 ||
        Math.abs(rect.height - containerSize.height) > 5
      ) {
        setContainerSize({
          width: Math.floor(rect.width),
          height: Math.floor(rect.height),
        });
      }
    }
  };

  updateContainerSize();
  window.addEventListener("resize", updateContainerSize);

  return () => {
    window.removeEventListener("resize", updateContainerSize);
  };
}, [containerSize.width, containerSize.height]);
```

## 9. 扩展可能性

### 9.1 未来可能的增强

1. **高级物理效果**

   - 增加重力场效果
   - 实现聚类行为
   - 添加非线性阻尼

2. **更多互动方式**

   - 双指缩放
   - 多泡泡同时选择
   - 泡泡合并/分裂效果

3. **性能增强**

   - WebGL 渲染
   - WebWorker 物理计算
   - 四叉树空间划分优化碰撞检测

4. **数据扩展**
   - 时间序列动画
   - 实时数据更新
   - 数据流可视化

## 10. 参考实现

### 10.1 关键类

CollisionSystem 类的核心实现：

```typescript
class CollisionSystem {
  private bubblePositions: Map<string, BubblePosition> = new Map();
  private collisionPairs: Set<string> = new Set();
  private updateCallback: (positions: BubblePosition[]) => void;
  private collisionCallback: (
    symbol1: string,
    symbol2: string,
    position: { x: number; y: number }
  ) => void;

  constructor(
    updateCallback: (positions: BubblePosition[]) => void,
    collisionCallback: (
      symbol1: string,
      symbol2: string,
      position: { x: number; y: number }
    ) => void
  ) {
    this.updateCallback = updateCallback;
    this.collisionCallback = collisionCallback;
  }

  // 位置更新
  updateBubblePosition(position: BubblePosition): void {
    this.bubblePositions.set(position.symbol, position);
    this.checkCollisions(position.symbol);
  }

  // 碰撞检测
  private checkCollisions(symbol: string): void {
    const bubble = this.bubblePositions.get(symbol);
    if (!bubble) return;

    const updatedPositions: BubblePosition[] = [];
    let hasCollision = false;

    this.bubblePositions.forEach((otherBubble, otherSymbol) => {
      if (symbol === otherSymbol) return;

      const pairKey = [symbol, otherSymbol].sort().join("-");
      if (this.collisionPairs.has(pairKey)) return;

      if (detectCollision(bubble, otherBubble)) {
        // 处理碰撞...
      }
    });

    if (hasCollision && updatedPositions.length > 0) {
      this.updateCallback(updatedPositions);
    }
  }

  // 物理模拟
  applyPhysics(containerSize: { width: number; height: number }): void {
    const now = Date.now();
    const updatedPositions: BubblePosition[] = [];
    let hasUpdates = false;

    this.bubblePositions.forEach((bubble, symbol) => {
      // 应用物理规则...
    });

    if (hasUpdates) {
      this.updateCallback(updatedPositions);
    }
  }
}
```

### 10.2 渲染实现

泡泡渲染的关键部分：

```tsx
<motion.div
  ref={bubbleRef}
  initial={{ opacity: 1 }}
  animate={controls}
  style={{
    width: size,
    height: size,
    position: "absolute",
    left: `calc(50% + ${position.offsetX}px)`,
    top: `calc(50% + ${position.offsetY}px)`,
    transform: `translate(-50%, -50%) translate(${dragPosition.x}px, ${dragPosition.y}px)`,
    zIndex: isSelected ? 50 : 0,
    willChange: "transform",
    touchAction: "none",
  }}
  drag
  dragMomentum={true}
  dragConstraints={getSafeBoundary()}
  dragElastic={0.5}
  onDrag={handleDrag}
  whileDrag={{ scale: 1.05 }}
  whileHover={{ scale: 1.1, transition: { duration: 0.3 } }}
  whileTap={{ scale: 0.95 }}
  onDragStart={() => onSelect(token.symbol)}
  onDragEnd={handleDragEnd}
  onClick={() => onSelect(isSelected ? "" : token.symbol)}
  className="group cursor-pointer"
>
  {/* 泡泡内容 */}
</motion.div>
```

## 11. 总结

TokenBubbleMap 组件将数据可视化与交互物理模拟相结合，创造了一个既实用又引人入胜的代币资产展示方式。通过精心设计的物理系统、视觉效果和交互模式，实现了一个流畅、直观且富有吸引力的用户体验。

这个技术规范可以作为在其他平台或使用其他技术栈重新实现该组件的参考指南，确保核心功能和用户体验的一致性。
