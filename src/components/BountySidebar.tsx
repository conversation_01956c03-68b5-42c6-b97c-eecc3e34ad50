"use client";

import React, { useState } from "react";
import { Trophy, Clock, Star, Target, ChevronLeft, ChevronRight, ChevronDown, ChevronUp } from "lucide-react";
import { useLayout } from "@/contexts/LayoutContext";
import { longTermTasks, careerBountyTasks } from "@/data/dashboard";

interface BountySidebarProps {
  className?: string;
  onTaskClick?: (taskId: string) => void;
}

const BountySidebar: React.FC<BountySidebarProps> = ({
  className = "",
  onTaskClick
}) => {
  const { layoutState, toggleSidebarCollapsed } = useLayout();
  const isCollapsed = layoutState.sidebarCollapsed;

  // State for collapsing different task types
  const [showBountyTasks, setShowBountyTasks] = useState(true);
  const [showCareerTasks, setShowCareerTasks] = useState(false);

  // Separate bounty tasks by type
  const bountyTasks = longTermTasks.map(task => ({ ...task, type: 'bounty' as const }));
  const careerTasks = careerBountyTasks.map(task => ({ ...task, type: 'career' as const }));

  const handleTaskClick = (taskId: string) => {
    if (onTaskClick) {
      onTaskClick(taskId);
    }
  };

  if (isCollapsed) {
    return (
      <aside className={`w-12 flex-shrink-0 ${className}`}>
        <div className="bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl p-3 shadow-lg mt-4">
          <div className="flex flex-col items-center gap-2">
            <Trophy className="w-5 h-5 text-[#ff2e63]" />
            <div className="text-xs text-[#94a3b8] text-center transform rotate-90 whitespace-nowrap">
              懸賞任務
            </div>
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside className={`w-full md:w-[340px] flex-shrink-0 ${className}`}>
      <div className="space-y-4 py-4">
        {/* Header */}
        <div className="bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl px-4 py-3 shadow-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-[#ff2e63] to-[#ff6b6b] flex items-center justify-center">
              <Trophy className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-[#ff2e63]">懸賞任務</h2>
              <div className="text-xs text-[#94a3b8]">
                {bountyTasks.length + careerTasks.length} 個可用任務
              </div>
            </div>
          </div>
        </div>

        {/* Tasks List */}
        <div className="bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl shadow-lg overflow-hidden">
          <div className="max-h-[calc(100vh-200px)] overflow-y-auto custom-scrollbar">

            {/* Bounty Tasks Section (Red) */}
            {bountyTasks.length > 0 && (
              <div className="border-b border-[#2d3748]/50">
                {/* Bounty Tasks Header */}
                <div
                  className="px-4 py-3 bg-[#ff2e63]/10 border-b border-[#ff2e63]/20 cursor-pointer hover:bg-[#ff2e63]/15 transition-colors duration-200"
                  onClick={() => setShowBountyTasks(!showBountyTasks)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Trophy className="w-4 h-4 text-[#ff2e63]" />
                      <span className="text-sm font-medium text-[#ff2e63]">懸賞任務</span>
                      <span className="text-xs text-[#ff2e63]/70 bg-[#ff2e63]/10 px-2 py-0.5 rounded-full">
                        {bountyTasks.length}
                      </span>
                    </div>
                    {showBountyTasks ? (
                      <ChevronUp className="w-4 h-4 text-[#ff2e63]" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-[#ff2e63]" />
                    )}
                  </div>
                </div>

                {/* Bounty Tasks Content */}
                {showBountyTasks && bountyTasks.map((task, index) => (
                  <div
                    key={task.id}
                    onClick={() => handleTaskClick(task.id)}
                    className={`
                      px-4 py-3 hover:bg-[#ff2e63]/5
                      transition-all duration-200 cursor-pointer group
                      ${index === bountyTasks.length - 1 ? '' : 'border-b border-[#ff2e63]/10'}
                    `}
                  >
                    {/* Task Row */}
                    <div className="flex items-center gap-3">
                      {/* Task Icon */}
                      <div className="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 bg-[#ff2e63]/10 group-hover:bg-[#ff2e63]/20">
                        <Trophy className="w-4 h-4 text-[#ff2e63]" />
                      </div>

                      {/* Task Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="text-sm font-medium text-white truncate group-hover:text-[#ff2e63] transition-colors">
                            {task.title}
                          </h3>
                          <div className="text-xs font-bold px-2 py-0.5 rounded-full flex-shrink-0 ml-2 text-[#ff2e63] bg-[#ff2e63]/10">
                            {task.reward}
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-xs px-2 py-0.5 rounded-full text-[#ff2e63] bg-[#ff2e63]/10">
                              {task.tag}
                            </span>
                          </div>

                          <div className="flex items-center gap-1 text-xs text-[#94a3b8]">
                            <Clock className="w-3 h-3" />
                            <span>{task.timeLeft}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Career Tasks Section (Green) */}
            {careerTasks.length > 0 && (
              <div>
                {/* Career Tasks Header */}
                <div
                  className="px-4 py-3 bg-[#00ff87]/10 border-b border-[#00ff87]/20 cursor-pointer hover:bg-[#00ff87]/15 transition-colors duration-200"
                  onClick={() => setShowCareerTasks(!showCareerTasks)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Target className="w-4 h-4 text-[#00ff87]" />
                      <span className="text-sm font-medium text-[#00ff87]">職業賞金任務</span>
                      <span className="text-xs text-[#00ff87]/70 bg-[#00ff87]/10 px-2 py-0.5 rounded-full">
                        {careerTasks.length}
                      </span>
                    </div>
                    {showCareerTasks ? (
                      <ChevronUp className="w-4 h-4 text-[#00ff87]" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-[#00ff87]" />
                    )}
                  </div>
                </div>

                {/* Career Tasks Content */}
                {showCareerTasks && careerTasks.map((task, index) => (
                  <div
                    key={task.id}
                    onClick={() => handleTaskClick(task.id)}
                    className={`
                      px-4 py-3 hover:bg-[#00ff87]/5
                      transition-all duration-200 cursor-pointer group
                      ${index === careerTasks.length - 1 ? '' : 'border-b border-[#00ff87]/10'}
                    `}
                  >
                    {/* Task Row */}
                    <div className="flex items-center gap-3">
                      {/* Task Icon */}
                      <div className="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 bg-[#00ff87]/10 group-hover:bg-[#00ff87]/20">
                        <Target className="w-4 h-4 text-[#00ff87]" />
                      </div>

                      {/* Task Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="text-sm font-medium text-white truncate group-hover:text-[#00ff87] transition-colors">
                            {task.title}
                          </h3>
                          <div className="text-xs font-bold px-2 py-0.5 rounded-full flex-shrink-0 ml-2 text-[#00ff87] bg-[#00ff87]/10">
                            {task.reward}
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-xs px-2 py-0.5 rounded-full text-[#00ff87] bg-[#00ff87]/10">
                              {task.tag}
                            </span>
                          </div>

                          <div className="flex items-center gap-1 text-xs text-[#94a3b8]">
                            <Clock className="w-3 h-3" />
                            <span>{task.timeLeft}</span>
                          </div>
                        </div>

                        {/* Progress bar for career tasks */}
                        {'progress' in task && 'progressTotal' in task && (
                          <div className="mt-2">
                            <div className="w-full bg-[#1a2332]/80 rounded-full h-1.5">
                              <div
                                className="bg-gradient-to-r from-[#00ff87] to-[#60efff] h-1.5 rounded-full transition-all duration-300"
                                style={{ width: `${(task.progress / task.progressTotal) * 100}%` }}
                              />
                            </div>
                            <div className="text-xs text-[#94a3b8] mt-1">
                              {task.progress}/{task.progressTotal} 完成
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-4 py-3 bg-[#2d3748]/30 border-t border-[#2d3748]/50">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2 text-[#94a3b8]">
                <Star className="w-3 h-3" />
                <span>更多任務即將推出</span>
              </div>
              <div className="text-[#ff2e63] font-medium">
                查看全部
              </div>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default BountySidebar;