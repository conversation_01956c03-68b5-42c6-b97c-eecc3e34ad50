import React from "react";
import { motion } from "framer-motion";

interface GameCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

const GameCard: React.FC<GameCardProps> = ({
  children,
  className = "",
  onClick,
}) => (
  <motion.div
    whileHover={{ scale: 1.02, y: -2 }}
    whileTap={{ scale: 0.98 }}
    onClick={onClick}
    className={`
      relative bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl shadow-lg overflow-hidden group hover:bg-[#1a1a2e]/90 transition-all duration-300
      ${className}
    `}
  >
    {/* Card corners */}
    <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#2a2a3e] rounded-tl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
    <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#2a2a3e] rounded-tr-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
    <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#2a2a3e] rounded-bl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
    <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#2a2a3e] rounded-br-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />

    {/* Content */}
    <div className="relative p-6">{children}</div>
  </motion.div>
);

export default GameCard;
