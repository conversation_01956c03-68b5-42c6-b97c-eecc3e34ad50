import React from "react";
import { cn } from "@/lib/utils";

interface PixelIconProps {
  children: React.ReactNode;
  size?: "sm" | "md" | "lg";
  color?: string;
  className?: string;
}

export const PixelIcon: React.FC<PixelIconProps> = ({
  children,
  size = "md",
  color = "#00ff87",
  className,
}) => {
  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-8 h-8",
    lg: "w-12 h-12",
  };

  return (
    <div
      className={cn(
        "flex items-center justify-center rounded-lg border-2 border-current",
        sizeClasses[size],
        className
      )}
      style={{ color }}
    >
      {children}
    </div>
  );
};
