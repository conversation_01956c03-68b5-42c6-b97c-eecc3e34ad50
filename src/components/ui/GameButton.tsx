import React from "react";
import { motion } from "framer-motion";

interface GameButtonProps {
  children: React.ReactNode;
  onClick: (e?: React.MouseEvent) => void;
  variant?: "primary" | "secondary";
  className?: string;
}

const GameButton: React.FC<GameButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  className = "",
}) => (
  <motion.button
    whileHover={{ scale: 1.05, y: -2 }}
    whileTap={{ scale: 0.95 }}
    onClick={onClick}
    className={`
      relative px-6 py-3 rounded-lg font-medium text-white
      ${
        variant === "primary"
          ? "bg-gradient-to-r from-[#00CEC9] to-[#00B894] shadow-lg shadow-[#00CEC9]/30"
          : "bg-white/10 border border-white/20 backdrop-blur-sm"
      }
      overflow-hidden group
      ${className}
    `}
  >
    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
    {children}
  </motion.button>
);

export default GameButton;
