import * as React from "react";
import { cn } from "@/lib/utils";

interface PixelProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  max?: number;
  color?: string;
  showValue?: boolean;
  size?: "sm" | "md" | "lg";
}

const PixelProgress = React.forwardRef<HTMLDivElement, PixelProgressProps>(
  (
    {
      className,
      value,
      max = 100,
      color = "#00ff87",
      showValue = true,
      size = "md",
      ...props
    },
    ref
  ) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

    return (
      <div
        ref={ref}
        className={cn(
          "relative w-full overflow-hidden",
          "bg-white/5 backdrop-blur-sm",
          "border border-white/10",
          "shadow-[0_0_0_1px_rgba(255,255,255,0.1)]",
          {
            "h-1.5": size === "sm",
            "h-2": size === "md",
            "h-3": size === "lg",
          },
          className
        )}
        {...props}
      >
        {/* Pixel effect background */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNMjAgMjBoMTB2MTBIMjB6TTQwIDQwaDEwdjEwSDQwek02MCA2MGgxMHYxMEg2MHoiIGZpbGw9InJnYmEoMjU1LDI1NSwyNTUsMC4xKSIvPjwvc3ZnPg==')] opacity-20" />

        {/* Progress bar */}
        <div
          className={cn(
            "h-full transition-all duration-300 ease-out",
            "bg-gradient-to-r from-[var(--progress-color)] to-[var(--progress-color-light)]",
            "relative overflow-hidden"
          )}
          style={
            {
              width: `${percentage}%`,
              "--progress-color": color,
              "--progress-color-light": `${color}80`,
            } as React.CSSProperties
          }
        >
          {/* Pixel effect overlay */}
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNMjAgMjBoMTB2MTBIMjB6TTQwIDQwaDEwdjEwSDQwek02MCA2MGgxMHYxMEg2MHoiIGZpbGw9InJnYmEoMjU1LDI1NSwyNTUsMC4yKSIvPjwvc3ZnPg==')] opacity-30" />

          {/* Animated particles */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute inset-0 animate-progress-particles">
              {Array.from({ length: 3 }).map((_, i) => (
                <div
                  key={i}
                  className="absolute h-full w-1 bg-white/20 animate-progress-particle"
                  style={{
                    left: `${Math.random() * 100}%`,
                    animationDelay: `${i * 0.5}s`,
                  }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Value display */}
        {showValue && (
          <div
            className={cn(
              "absolute right-0 top-1/2 -translate-y-1/2",
              "px-2 py-0.5 text-xs font-medium",
              "bg-black/50 backdrop-blur-sm",
              "border border-white/10",
              "rounded-sm"
            )}
            style={{ color }}
          >
            {Math.round(percentage)}%
          </div>
        )}
      </div>
    );
  }
);
PixelProgress.displayName = "PixelProgress";

export { PixelProgress };
