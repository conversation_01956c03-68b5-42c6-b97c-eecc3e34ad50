"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface EnhancedProgressBarProps {
  current: number;
  total: number;
  percentage: number;
  title?: string;
  subtitle?: string;
  label?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
  showStatus?: boolean;
  status?: "completed" | "in_progress" | "not_started";
  variant?: "level" | "goal";
  animated?: boolean;
}

const EnhancedProgressBar: React.FC<EnhancedProgressBarProps> = ({
  current,
  total,
  percentage,
  title,
  subtitle,
  label,
  className = "",
  size = "md",
  showStatus = true,
  status = "in_progress",
  variant = "goal",
  animated = true,
}) => {
  // Status icons with enhanced styling using new color scheme
  const statusConfig = {
    completed: {
      icon: "✅",
      color: "#4ecdc4",
      bgColor: "bg-[#4ecdc4]/15",
      borderColor: "border-[#4ecdc4]/30",
      progressColor: "from-[#4ecdc4]/25 to-[#4ecdc4]/15"
    },
    in_progress: {
      icon: "⏳",
      color: "#f39c12",
      bgColor: "bg-[#f39c12]/15",
      borderColor: "border-[#f39c12]/30",
      progressColor: "from-[#f39c12]/25 to-[#f39c12]/15"
    },
    not_started: {
      icon: "❌",
      color: "#e74c3c",
      bgColor: "bg-[#e74c3c]/15",
      borderColor: "border-[#e74c3c]/30",
      progressColor: "from-[#e74c3c]/25 to-[#e74c3c]/15"
    },
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      height: "h-10",
      textSize: "text-sm",
      padding: "px-3 py-2",
    },
    md: {
      height: "h-12",
      textSize: "text-base",
      padding: "px-4 py-3",
    },
    lg: {
      height: "h-14",
      textSize: "text-lg",
      padding: "px-5 py-4",
    },
  };

  const config = sizeConfig[size];
  const statusInfo = statusConfig[status];

  if (variant === "level") {
    // Enhanced level variant with expandable hover state
    return (
      <motion.div
        initial={animated ? { opacity: 0, y: 10 } : {}}
        animate={animated ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.3 }}
        className={cn(
          "relative bg-gradient-to-r from-[#1a202c]/90 to-[#0f1419]/80 rounded-xl border border-[#2d3748] overflow-hidden transition-all duration-300 hover:border-[#4ecdc4]/40 group cursor-pointer",
          "hover:h-auto", // Allow height expansion on hover
          config.height,
          className
        )}
      >
        {/* Animated background gradient */}
        <motion.div
          initial={animated ? { width: 0 } : { width: `${percentage}%` }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: animated ? 1.2 : 0, ease: "easeOut" }}
          className="absolute inset-0 bg-gradient-to-r from-[#4ecdc4]/20 via-[#f39c12]/15 to-[#4ecdc4]/20"
        />

        {/* Shimmer effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />

        {/* Content - Single line by default, multi-line on hover */}
        <div className={cn("relative transition-all duration-300", config.padding)}>
          {/* Compact view (default) */}
          <div className="flex items-center h-full group-hover:hidden">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <motion.span
                initial={animated ? { opacity: 0, x: -10 } : {}}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
                className={cn("text-white/90 font-medium", config.textSize)}
              >
                ({percentage.toFixed(0)}%)
              </motion.span>
              <motion.span
                initial={animated ? { opacity: 0, x: -10 } : {}}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                className={cn("text-[#4ecdc4] font-bold truncate", config.textSize)}
              >
                {title}
              </motion.span>
              {subtitle && (
                <>
                  <span className="text-white/40">-</span>
                  <motion.span
                    initial={animated ? { opacity: 0, x: -10 } : {}}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: 0.4 }}
                    className={cn("text-white/70 truncate", config.textSize)}
                  >
                    {subtitle}
                  </motion.span>
                </>
              )}
            </div>

            <motion.div
              initial={animated ? { opacity: 0, x: 10 } : {}}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.5 }}
              className="flex items-center gap-2 flex-shrink-0"
            >
              <span className={cn("text-white/80 font-mono", config.textSize)}>
                {current.toLocaleString()}/{total.toLocaleString()}
              </span>
              <span className={cn("text-[#f39c12] font-medium", config.textSize)}>
                XP
              </span>
            </motion.div>
          </div>

          {/* Expanded view (on hover) */}
          <div className="hidden group-hover:block space-y-2 py-1">
            {/* Level and Progress */}
            <div className="flex items-center justify-between">
              <motion.span
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className={cn("text-[#4ecdc4] font-bold", config.textSize)}
              >
                {title}
              </motion.span>
              <motion.span
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className={cn("text-white/90 font-medium", config.textSize)}
              >
                {percentage.toFixed(0)}%
              </motion.span>
            </div>

            {/* Title/Subtitle */}
            {subtitle && (
              <motion.div
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className={cn("text-white/70", config.textSize)}
              >
                {subtitle}
              </motion.div>
            )}

            {/* XP Progress */}
            <div className="flex items-center justify-between">
              <motion.span
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
                className={cn("text-white/60 text-sm")}
              >
                經驗值進度
              </motion.span>
              <motion.span
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.4 }}
                className={cn("text-[#f39c12] font-mono", config.textSize)}
              >
                {current.toLocaleString()}/{total.toLocaleString()} XP
              </motion.span>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Enhanced goal variant
  return (
    <motion.div
      initial={animated ? { opacity: 0, y: 5 } : {}}
      animate={animated ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.3 }}
      className={cn(
        "relative bg-gradient-to-r from-[#1a202c]/90 to-[#0f1419]/80 rounded-lg border overflow-hidden transition-all duration-300 group",
        config.height,
        statusInfo.bgColor,
        statusInfo.borderColor,
        `hover:${statusInfo.borderColor}`,
        className
      )}
    >
      {/* Progress background */}
      <motion.div
        initial={animated ? { width: 0 } : { width: `${percentage}%` }}
        animate={{ width: `${percentage}%` }}
        transition={{ duration: animated ? 0.8 : 0, ease: "easeOut" }}
        className={cn("absolute inset-0 bg-gradient-to-r", statusInfo.progressColor)}
      />
      
      {/* Content */}
      <div className={cn("relative flex items-center justify-between h-full", config.padding)}>
        {/* Status and label */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {showStatus && (
            <motion.span
              initial={animated ? { scale: 0 } : {}}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="text-lg"
            >
              {statusInfo.icon}
            </motion.span>
          )}
          <motion.span
            initial={animated ? { opacity: 0, x: -10 } : {}}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
            className={cn("text-white/90 font-medium", config.textSize)}
          >
            {label}
          </motion.span>
        </div>
        
        {/* Values only */}
        <div className="flex items-center justify-center flex-1 min-w-0">
          <motion.span
            initial={animated ? { opacity: 0, scale: 0.8 } : {}}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.3 }}
            className={cn("text-white/80 font-mono whitespace-nowrap", config.textSize)}
          >
            {current.toLocaleString()}/{total.toLocaleString()}
          </motion.span>
        </div>
        
        {/* Percentage */}
        <motion.div
          initial={animated ? { opacity: 0, x: 10 } : {}}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4, delay: 0.5 }}
          className="flex-shrink-0"
        >
          <span
            className={cn("font-bold", config.textSize)}
            style={{ color: statusInfo.color }}
          >
            {percentage.toFixed(0)}%
          </span>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default EnhancedProgressBar;
