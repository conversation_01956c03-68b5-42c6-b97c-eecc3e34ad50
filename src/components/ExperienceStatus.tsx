import React from "react";
import { useProfile } from "@/hooks/useProfile";

interface ExperienceStatusProps {
  className?: string;
  mini?: boolean;
}

export const ExperienceStatus: React.FC<ExperienceStatusProps> = ({
  className = "",
  mini = false,
}) => {
  const { profile, canGainExperience, nextExperienceIn } = useProfile();

  if (!profile) return null;

  const currentLevelExp = (profile.level - 1) * 100;
  const nextLevelExp = profile.level * 100;
  const progressPercent = ((profile.experience_points - currentLevelExp) / 100) * 100;

  const formatTimeUntilNext = (hours: number): string => {
    if (hours <= 0) return "现在可以获得经验！";
    
    const wholeHours = Math.floor(hours);
    const minutes = Math.floor((hours - wholeHours) * 60);
    
    if (wholeHours > 0) {
      return `${wholeHours}小时${minutes}分钟后可获得经验`;
    } else {
      return `${minutes}分钟后可获得经验`;
    }
  };

  // Mini版本 - 紧凑的一行显示
  if (mini) {
    return (
      <div className={`flex items-center gap-3 px-4 py-2 rounded-lg ${className || 'bg-white border shadow-sm'}`}>
        {/* 等级标签 */}
        <div className={`text-xs px-2 py-1 rounded-full font-medium ${
          className 
            ? 'bg-gradient-to-r from-[#00ff87] to-[#60efff] text-black' 
            : 'bg-blue-100 text-blue-800'
        }`}>
          Lv.{profile.level}
        </div>

        {/* 经验进度 */}
        <div className="flex items-center gap-2">
          <div className={`w-16 rounded-full h-1.5 ${
            className ? 'bg-[#2a2a3e]' : 'bg-gray-200'
          }`}>
            <div
              className="bg-gradient-to-r from-[#00ff87] to-[#60efff] h-1.5 rounded-full transition-all duration-500"
              style={{ width: `${Math.min(progressPercent, 100)}%` }}
            />
          </div>
          <span className={`text-xs ${
            className ? 'text-gray-300' : 'text-gray-600'
          }`}>
            {profile.experience_points}/{nextLevelExp}
          </span>
        </div>

        {/* 状态指示 */}
        <div className="text-xs">
          {canGainExperience ? (
            <span className={`${
              className ? 'text-[#00ff87]' : 'text-green-600'
            }`}>
              🎉
            </span>
          ) : (
            <span className={`${
              className ? 'text-gray-400' : 'text-gray-500'
            }`}>
              ⏰
            </span>
          )}
        </div>
      </div>
    );
  }

  // 完整版本
  return (
    <div className={`rounded-xl p-6 ${className || 'bg-white border shadow-sm'}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className={`text-lg font-semibold ${className ? 'text-white' : 'text-gray-800'}`}>
          ⚡ 经验状态
        </h3>
        <div className={`text-sm px-3 py-1 rounded-full ${
          className 
            ? 'bg-gradient-to-r from-[#00ff87] to-[#60efff] text-black font-medium' 
            : 'bg-blue-100 text-blue-800'
        }`}>
          等级 {profile.level}
        </div>
      </div>
      
      {/* 经验进度条 */}
      <div className="mb-4">
        <div className={`flex justify-between text-sm mb-2 ${
          className ? 'text-gray-300' : 'text-gray-600'
        }`}>
          <span>{profile.experience_points} 经验</span>
          <span>{nextLevelExp} 经验升级</span>
        </div>
        <div className={`w-full rounded-full h-3 ${
          className ? 'bg-[#2a2a3e]' : 'bg-gray-200'
        }`}>
          <div
            className="bg-gradient-to-r from-[#00ff87] to-[#60efff] h-3 rounded-full transition-all duration-500 shadow-sm"
            style={{ width: `${Math.min(progressPercent, 100)}%` }}
          />
        </div>
      </div>

      {/* 下次获得经验时间 */}
      <div className="text-sm mb-3">
        {canGainExperience ? (
          <div className={`font-medium flex items-center gap-2 ${
            className ? 'text-[#00ff87]' : 'text-green-600'
          }`}>
            🎉 <span>登录即可获得 10 经验！</span>
          </div>
        ) : (
          <div className={`flex items-center gap-2 ${
            className ? 'text-gray-400' : 'text-gray-500'
          }`}>
            ⏰ <span>{formatTimeUntilNext(nextExperienceIn)}</span>
          </div>
        )}
      </div>

      {/* 每日经验提示 */}
      <div className={`text-xs border-t pt-3 ${
        className 
          ? 'text-gray-500 border-[#2a2a3e]' 
          : 'text-gray-400 border-gray-200'
      }`}>
        💡 每12小时登录可获得10经验值
      </div>
    </div>
  );
}; 