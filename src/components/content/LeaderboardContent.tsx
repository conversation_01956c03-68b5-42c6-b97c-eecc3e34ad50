"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

interface Profile {
  clerk_user_id: string;
  full_name: string | null;
  avatar_url?: string;
  level: number;
  experience_points: number;
  subscribers: number;
  views: number;
  created_at: string;
}

interface LeaderboardEntry extends Profile {
  rank: number;
}

const LeaderboardContent: React.FC = () => {
  const router = useRouter();
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const fetchLeaderboard = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/leaderboard?sortBy=experience_points&limit=50`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch leaderboard");
      }

      const data: Profile[] = await response.json();

      // 添加排名
      const rankedData: LeaderboardEntry[] = data.map((profile, index) => ({
        ...profile,
        rank: index + 1,
      }));

      setLeaderboard(rankedData);
      setError("");
    } catch (err) {
      setError("加載榜單失敗，請稍後重試");
      console.error("Error fetching leaderboard:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaderboard();
  }, []);

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "from-[#ffd700] to-[#ffb347]";
      case 2:
        return "from-[#c0c0c0] to-[#808080]";
      case 3:
        return "from-[#cd7f32] to-[#8b4513]";
      default:
        return "from-[#2a2a3e] to-[#1a1a2e]";
    }
  };

  const getRankIcon = (rank: number) => {
    if (rank === 1) return "🥇";
    if (rank === 2) return "🥈";
    if (rank === 3) return "🥉";
    return `#${rank}`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getSortValue = (profile: Profile) => {
    return formatNumber(profile.experience_points);
  };

  return (
    <div className="flex flex-col gap-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            創作者榜單
          </h1>
        </div>
        <Button
          onClick={() => fetchLeaderboard()}
          variant="outline"
          size="sm"
          className="bg-[#00ff87]/20 text-[#00ff87] border-[#00ff87]/40 hover:bg-[#00ff87]/30"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl px-8 py-6 shadow-lg">
            <div className="flex items-center gap-4 text-[#00ff87]">
              <div className="w-3 h-10 rounded-full bg-gradient-to-b from-[#00ff87] to-[#60efff] animate-pulse" />
              <span className="text-xl font-bold bg-gradient-to-r from-[#00ff87] to-[#60efff] bg-clip-text text-transparent">
                🚀 載入榜單中...
              </span>
            </div>
          </div>
        </div>
      ) : error ? (
        <div className="flex justify-center items-center py-20">
          <div className="bg-[#1a1a2e]/95 backdrop-blur-sm rounded-xl p-8 border border-[#ff2e63]/50 text-center shadow-lg">
            <div className="text-[#ff2e63] text-xl font-bold mb-4">
              ⚠️ {error}
            </div>
            <Button
              onClick={() => fetchLeaderboard()}
              className="bg-gradient-to-r from-[#ff2e63] to-[#ff6b9d] hover:from-[#e91e54] hover:to-[#ff5a8a] text-white font-bold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
            >
              🔄 重新載入
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* 前三名特殊展示 - 颁奖台样式 */}
          {leaderboard.length >= 3 && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-white text-center mb-6 font-heading">
                🏆 榜單前三甲
              </h2>
              {/* 頒獎台布局 - 底部對齊 */}
              <div className="flex items-end justify-center gap-4 md:gap-8 mb-8 px-2 max-w-4xl mx-auto">
                {/* 第二名 - 左側，較低 */}
                <div className="flex flex-col items-center flex-1 max-w-[220px]">
                  {leaderboard[1] && (
                    <div className="bg-gradient-to-br from-[#c0c0c0]/20 to-[#808080]/20 backdrop-blur-sm rounded-xl p-4 md:p-6 border-2 border-[#c0c0c0]/30 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer group w-full"
                         onClick={() => router.push(`/creator/${leaderboard[1].clerk_user_id}`)}>
                      <div className="text-center">
                        <div className="relative mb-2 md:mb-3">
                          <div className="w-12 h-12 md:w-16 md:h-16 mx-auto rounded-full overflow-hidden border-3 border-[#c0c0c0] shadow-lg">
                            {leaderboard[1].avatar_url ? (
                              <img src={leaderboard[1].avatar_url} alt={leaderboard[1].full_name || "用戶"} className="w-full h-full object-cover" />
                            ) : (
                              <div className="w-full h-full bg-gradient-to-br from-[#c0c0c0] to-[#808080] flex items-center justify-center text-white text-lg md:text-xl font-bold">
                                {leaderboard[1].full_name?.[0] || "?"}
                              </div>
                            )}
                          </div>
                          <div className="absolute -top-1 -right-1 w-5 h-5 md:w-6 md:h-6 bg-gradient-to-r from-[#c0c0c0] to-[#808080] rounded-full flex items-center justify-center text-white font-black text-xs shadow-lg">
                            🥈
                          </div>
                        </div>
                        <h3 className="text-white font-bold text-xs md:text-sm mb-1 group-hover:text-[#c0c0c0] transition-colors line-clamp-1">
                          {leaderboard[1].full_name || "匿名用戶"}
                        </h3>
                        <div className="text-[#c0c0c0] font-bold text-sm md:text-lg mb-1">
                          {getSortValue(leaderboard[1])}
                        </div>
                      </div>
                    </div>
                  )}
                  {/* 第二名台階 */}
                  <div className="w-full h-8 md:h-12 bg-gradient-to-t from-[#c0c0c0]/30 to-[#c0c0c0]/10 rounded-t-lg border-t-2 border-[#c0c0c0]/50 flex items-center justify-center">
                    <span className="text-[#c0c0c0] font-bold text-lg md:text-xl">2</span>
                  </div>
                </div>

                {/* 第一名 - 中間，最高 */}
                <div className="flex flex-col items-center flex-1 max-w-[260px]">
                  {leaderboard[0] && (
                    <div className="bg-gradient-to-br from-[#ffd700]/20 to-[#ffb347]/20 backdrop-blur-sm rounded-xl p-6 md:p-8 border-2 border-[#ffd700]/50 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110 cursor-pointer group relative overflow-hidden w-full"
                         onClick={() => router.push(`/creator/${leaderboard[0].clerk_user_id}`)}>
                      <div className="absolute inset-0 bg-gradient-to-r from-[#ffd700]/10 to-[#ffb347]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <div className="text-center relative z-10">
                        <div className="relative mb-3 md:mb-4">
                          <div className="w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full overflow-hidden border-3 border-[#ffd700] shadow-xl">
                            {leaderboard[0].avatar_url ? (
                              <img src={leaderboard[0].avatar_url} alt={leaderboard[0].full_name || "用戶"} className="w-full h-full object-cover" />
                            ) : (
                              <div className="w-full h-full bg-gradient-to-br from-[#ffd700] to-[#ffb347] flex items-center justify-center text-white text-xl md:text-2xl font-bold">
                                {leaderboard[0].full_name?.[0] || "?"}
                              </div>
                            )}
                          </div>
                          <div className="absolute -top-1 md:-top-2 -right-1 md:-right-2 w-6 h-6 md:w-8 md:h-8 bg-gradient-to-r from-[#ffd700] to-[#ffb347] rounded-full flex items-center justify-center text-white font-black text-xs md:text-sm shadow-xl animate-pulse">
                            🥇
                          </div>
                        </div>
                        <h3 className="text-white font-bold text-sm md:text-lg mb-1 md:mb-2 group-hover:text-[#ffd700] transition-colors line-clamp-1">
                          {leaderboard[0].full_name || "匿名用戶"}
                        </h3>
                        <div className="text-[#ffd700] font-black text-lg md:text-xl mb-1">
                          {getSortValue(leaderboard[0])}
                        </div>
                        <div className="text-gray-300 text-xs font-semibold">
                          👑 冠軍
                        </div>
                      </div>
                    </div>
                  )}
                  {/* 第一名台階 - 最高 */}
                  <div className="w-full h-10 md:h-16 bg-gradient-to-t from-[#ffd700]/30 to-[#ffd700]/10 rounded-t-lg border-t-2 border-[#ffd700]/50 flex items-center justify-center">
                    <span className="text-[#ffd700] font-bold text-xl md:text-2xl">1</span>
                  </div>
                </div>

                {/* 第三名 - 右側，較低 */}
                <div className="flex flex-col items-center flex-1 max-w-[220px]">
                  {leaderboard[2] && (
                    <div className="bg-gradient-to-br from-[#cd7f32]/20 to-[#8b4513]/20 backdrop-blur-sm rounded-xl p-4 md:p-6 border-2 border-[#cd7f32]/30 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer group w-full"
                         onClick={() => router.push(`/creator/${leaderboard[2].clerk_user_id}`)}>
                      <div className="text-center">
                        <div className="relative mb-2 md:mb-3">
                          <div className="w-12 h-12 md:w-16 md:h-16 mx-auto rounded-full overflow-hidden border-3 border-[#cd7f32] shadow-lg">
                            {leaderboard[2].avatar_url ? (
                              <img src={leaderboard[2].avatar_url} alt={leaderboard[2].full_name || "用戶"} className="w-full h-full object-cover" />
                            ) : (
                              <div className="w-full h-full bg-gradient-to-br from-[#cd7f32] to-[#8b4513] flex items-center justify-center text-white text-lg md:text-xl font-bold">
                                {leaderboard[2].full_name?.[0] || "?"}
                              </div>
                            )}
                          </div>
                          <div className="absolute -top-1 -right-1 w-5 h-5 md:w-6 md:h-6 bg-gradient-to-r from-[#cd7f32] to-[#8b4513] rounded-full flex items-center justify-center text-white font-black text-xs shadow-lg">
                            🥉
                          </div>
                        </div>
                        <h3 className="text-white font-bold text-xs md:text-sm mb-1 group-hover:text-[#cd7f32] transition-colors line-clamp-1">
                          {leaderboard[2].full_name || "匿名用戶"}
                        </h3>
                        <div className="text-[#cd7f32] font-bold text-sm md:text-lg mb-1">
                          {getSortValue(leaderboard[2])}
                        </div>
                      </div>
                    </div>
                  )}
                  {/* 第三名台階 */}
                  <div className="w-full h-8 md:h-12 bg-gradient-to-t from-[#cd7f32]/30 to-[#cd7f32]/10 rounded-t-lg border-t-2 border-[#cd7f32]/50 flex items-center justify-center">
                    <span className="text-[#cd7f32] font-bold text-lg md:text-xl">3</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 完整榜單 */}
          <div className="space-y-3">
            <h2 className="text-lg font-bold text-white mb-4 font-heading flex items-center gap-2">
              <div className="w-1 h-5 bg-gradient-to-b from-[#00ff87] to-[#60efff] rounded-full" />
              📊 完整榜單
            </h2>

            {leaderboard.map((entry, index) => (
              <div
                key={entry.clerk_user_id}
                className={`
                  relative overflow-hidden rounded-xl transition-all duration-300 cursor-pointer group
                  ${entry.rank <= 3
                    ? 'bg-gradient-to-r from-[#1a1a2e]/95 to-[#2a2a3e]/95 border-2 border-[#00ff87]/30 shadow-lg hover:shadow-xl hover:border-[#00ff87]/50'
                    : 'bg-[#1a1a2e]/90 border border-[#2a2a3e] shadow-md hover:shadow-lg hover:border-[#00ff87]/20'
                  }
                  backdrop-blur-sm transform hover:scale-[1.01]
                `}
                onClick={() => router.push(`/creator/${entry.clerk_user_id}`)}
              >
                {/* 背景裝飾 */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00ff87]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                <div className="relative z-10 p-4">
                  <div className="flex items-center gap-4">
                    {/* 排名 */}
                    <div className={`
                      flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center font-black text-sm shadow-lg
                      ${entry.rank <= 3
                        ? `bg-gradient-to-r ${getRankColor(entry.rank)} border-2 border-white/20`
                        : 'bg-gradient-to-r from-[#2a2a3e] to-[#1a1a2e] border border-[#3a3a4e]'
                      }
                    `}>
                      <span className="text-white drop-shadow-lg">
                        {getRankIcon(entry.rank)}
                      </span>
                    </div>

                    {/* 頭像 */}
                    <div className="flex-shrink-0 w-12 h-12 rounded-xl overflow-hidden border-2 border-[#2a2a3e] shadow-lg group-hover:border-[#00ff87]/30 transition-colors">
                      {entry.avatar_url ? (
                        <img
                          src={entry.avatar_url}
                          alt={entry.full_name || "用戶"}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-[#2a2a3e] to-[#1a1a2e] flex items-center justify-center text-white text-lg font-bold">
                          {entry.full_name?.[0] || "?"}
                        </div>
                      )}
                    </div>

                    {/* 用戶信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-white font-bold text-lg font-heading truncate group-hover:text-[#00ff87] transition-colors">
                          {entry.full_name || "匿名用戶"}
                        </h3>
                        <span className="px-2 py-1 bg-[#00ff87]/20 text-[#00ff87] rounded-full text-xs font-semibold border border-[#00ff87]/30 flex-shrink-0">
                          Lv.{entry.level}
                        </span>
                      </div>

                      <div className="flex items-center gap-3 text-xs text-gray-500">
                        <span>經驗: {entry.experience_points?.toLocaleString() || 0}</span>
                        {entry.subscribers > 0 && (
                          <span>訂閱: {entry.subscribers.toLocaleString()}</span>
                        )}
                        {entry.views > 0 && (
                          <span>觀看: {entry.views.toLocaleString()}</span>
                        )}
                      </div>
                    </div>

                    {/* 統計數據 */}
                    <div className="flex-shrink-0 text-right">
                      <div className="text-white font-black text-lg group-hover:text-[#00ff87] transition-colors">
                        {entry.experience_points?.toLocaleString() || 0}
                      </div>
                      <div className="text-gray-400 text-xs font-semibold flex items-center justify-end gap-1">
                        <span>💎</span>
                        <span>經驗值</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {leaderboard.length === 0 && (
            <div className="text-center py-16">
              <div className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-8 shadow-lg max-w-sm mx-auto">
                <div className="text-4xl mb-3">🎯</div>
                <div className="text-gray-400 text-lg font-semibold mb-2">
                  暫無榜單數據
                </div>
                <div className="text-gray-500 text-sm">
                  等待更多創作者加入！
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LeaderboardContent;
