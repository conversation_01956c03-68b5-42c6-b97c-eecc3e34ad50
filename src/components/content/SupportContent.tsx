"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import { HelpCircle, MessageCircle, Book, Mail, Phone, ExternalLink } from "lucide-react";

const SupportContent: React.FC = () => {
  const faqItems = [
    {
      question: "如何開始使用VG蜂巢？",
      answer: "首先完成個人資料設定，然後連接您的YouTube頻道和錢包，即可開始創作和賺取獎勵。"
    },
    {
      question: "如何獲得更多RPx獎勵？",
      answer: "完成任務、發布優質內容、參與社群互動都可以獲得RPx獎勵。"
    },
    {
      question: "錢包連接有問題怎麼辦？",
      answer: "請確保您的MetaMask錢包已正確安裝並解鎖，然後重新嘗試連接。"
    }
  ];

  return (
    <div className="flex flex-col gap-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            幫助支援
          </h1>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contact Options */}
        <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-6">
          <div className="flex items-center gap-4 mb-6">
            <MessageCircle className="w-6 h-6 text-[#00ff87]" />
            <h2 className="text-xl font-bold text-white">聯繫我們</h2>
          </div>
          
          <div className="space-y-4">
            <button className="w-full text-left p-4 bg-[#2a2a3e]/50 rounded-lg hover:bg-[#2a2a3e]/70 transition-colors flex items-center gap-3">
              <Mail className="w-5 h-5 text-[#00ff87]" />
              <div>
                <div className="text-white font-medium">電子郵件支援</div>
                <div className="text-gray-400 text-sm"><EMAIL></div>
              </div>
              <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
            </button>
            
            <button className="w-full text-left p-4 bg-[#2a2a3e]/50 rounded-lg hover:bg-[#2a2a3e]/70 transition-colors flex items-center gap-3">
              <MessageCircle className="w-5 h-5 text-[#00ff87]" />
              <div>
                <div className="text-white font-medium">即時聊天</div>
                <div className="text-gray-400 text-sm">24/7 線上支援</div>
              </div>
              <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
            </button>
            
            <button className="w-full text-left p-4 bg-[#2a2a3e]/50 rounded-lg hover:bg-[#2a2a3e]/70 transition-colors flex items-center gap-3">
              <Book className="w-5 h-5 text-[#00ff87]" />
              <div>
                <div className="text-white font-medium">使用手冊</div>
                <div className="text-gray-400 text-sm">詳細操作指南</div>
              </div>
              <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
            </button>
          </div>
        </Card>

        {/* FAQ */}
        <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-6">
          <div className="flex items-center gap-4 mb-6">
            <HelpCircle className="w-6 h-6 text-[#00ff87]" />
            <h2 className="text-xl font-bold text-white">常見問題</h2>
          </div>
          
          <div className="space-y-4">
            {faqItems.map((item, index) => (
              <div key={index} className="border-b border-[#2a2a3e] pb-4 last:border-b-0">
                <div className="text-white font-medium mb-2">{item.question}</div>
                <div className="text-gray-400 text-sm">{item.answer}</div>
              </div>
            ))}
          </div>
          
          <button className="w-full mt-6 p-3 bg-[#00ff87]/20 text-[#00ff87] rounded-lg border border-[#00ff87]/40 hover:bg-[#00ff87]/30 transition-colors">
            查看更多FAQ
          </button>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-6">
        <h3 className="text-lg font-bold text-white mb-4">快速操作</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 bg-[#2a2a3e]/50 rounded-lg hover:bg-[#2a2a3e]/70 transition-colors text-center">
            <Book className="w-8 h-8 text-[#00ff87] mx-auto mb-2" />
            <div className="text-white font-medium">新手指南</div>
          </button>
          
          <button className="p-4 bg-[#2a2a3e]/50 rounded-lg hover:bg-[#2a2a3e]/70 transition-colors text-center">
            <MessageCircle className="w-8 h-8 text-[#00ff87] mx-auto mb-2" />
            <div className="text-white font-medium">提交問題</div>
          </button>
          
          <button className="p-4 bg-[#2a2a3e]/50 rounded-lg hover:bg-[#2a2a3e]/70 transition-colors text-center">
            <ExternalLink className="w-8 h-8 text-[#00ff87] mx-auto mb-2" />
            <div className="text-white font-medium">社群論壇</div>
          </button>
        </div>
      </Card>
    </div>
  );
};

export default SupportContent;
