"use client";

import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import TaskDetailModal from "@/components/TaskDetailModal";
import {
  Zap,
  CheckCircle,
  Clock,
  AlertCircle,
  Trophy,
  Star,
  Target,
  Gift,
  Calendar,
  TrendingUp
} from "lucide-react";
import {
  mockTokenPoolData,
  longTermTasks,
  careerBountyTasks,
} from "@/data/dashboard";
import {
  TokenPoolOverview,
  BountyTasksSection,
  CareerTasksSection,
} from "@/components/dashboard";
import { creatorKitTasks } from "@/data/creator-kit-tasks";

const TasksContent: React.FC = () => {
  const [activeTaskId, setActiveTaskId] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const handleTaskClick = (taskId: string) => {
    setActiveTaskId(taskId);
  };

  const activeTask = activeTaskId
    ? creatorKitTasks.find((task) => task.id === activeTaskId)
    : null;

  // Mock task statistics
  const taskStats = {
    total: creatorKitTasks.length,
    completed: creatorKitTasks.filter(task => task.isCompleted).length,
    inProgress: creatorKitTasks.filter(task => task.progress > 0 && !task.isCompleted).length,
    pending: creatorKitTasks.filter(task => task.progress === 0).length,
  };

  const categories = [
    { id: "all", label: "全部任務", icon: <Target className="w-4 h-4" /> },
    { id: "daily", label: "每日任務", icon: <Calendar className="w-4 h-4" /> },
    { id: "weekly", label: "週任務", icon: <TrendingUp className="w-4 h-4" /> },
    { id: "achievement", label: "成就任務", icon: <Trophy className="w-4 h-4" /> },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "in_progress":
        return <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "已完成";
      case "in_progress":
        return "進行中";
      default:
        return "待開始";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-500";
      case "in_progress":
        return "text-yellow-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <div className="flex flex-col gap-10 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            任務中心
          </h1>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-3">
        {categories.map((category) => (
          <Button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            variant={selectedCategory === category.id ? "default" : "outline"}
            size="sm"
            className={
              selectedCategory === category.id
                ? "bg-[#00ff87] text-black hover:bg-[#00ff87]/80"
                : "bg-[#1a1a2e]/50 text-white border-[#2a2a3e] hover:bg-[#2a2a3e]/50"
            }
          >
            {category.icon}
            <span className="ml-2">{category.label}</span>
          </Button>
        ))}
      </div>

      {/* Task Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-4">
          <div className="flex items-center gap-3">
            <Target className="w-8 h-8 text-[#00ff87]" />
            <div>
              <div className="text-2xl font-bold text-white">{taskStats.total}</div>
              <div className="text-gray-400 text-sm">總任務</div>
            </div>
          </div>
        </Card>

        <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-4">
          <div className="flex items-center gap-3">
            <CheckCircle className="w-8 h-8 text-green-500" />
            <div>
              <div className="text-2xl font-bold text-white">{taskStats.completed}</div>
              <div className="text-gray-400 text-sm">已完成</div>
            </div>
          </div>
        </Card>

        <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-4">
          <div className="flex items-center gap-3">
            <Clock className="w-8 h-8 text-yellow-500" />
            <div>
              <div className="text-2xl font-bold text-white">{taskStats.inProgress}</div>
              <div className="text-gray-400 text-sm">進行中</div>
            </div>
          </div>
        </Card>

        <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-4">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-8 h-8 text-gray-500" />
            <div>
              <div className="text-2xl font-bold text-white">{taskStats.pending}</div>
              <div className="text-gray-400 text-sm">待開始</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Token Pool Overview */}
      <TokenPoolOverview data={mockTokenPoolData} />

      <div className="flex flex-col gap-8">
        {/* Bounty Tasks */}
        <BountyTasksSection
          tasks={longTermTasks}
          onTaskClick={handleTaskClick}
        />

        {/* Career Bounty Tasks */}
        <CareerTasksSection
          tasks={careerBountyTasks}
          onTaskClick={handleTaskClick}
        />
      </div>

      {/* Task Detail Modal */}
      {activeTask && (
        <TaskDetailModal
          task={{
            id: activeTask.id,
            title: activeTask.title,
            description: activeTask.description,
            reward: activeTask.reward,
            tag: activeTask.category,
            timeLeft: `${
              activeTask.totalSteps - activeTask.progress
            } steps left`,
            cta: activeTask.isCompleted ? "Completed" : "Start Task",
          }}
          onClose={() => setActiveTaskId(null)}
        />
      )}
    </div>
  );
};

export default TasksContent;
