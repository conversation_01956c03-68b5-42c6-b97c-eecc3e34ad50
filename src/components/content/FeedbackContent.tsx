"use client";

import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { MessageSquare, Star, Send, ThumbsUp, ThumbsDown } from "lucide-react";

const FeedbackContent: React.FC = () => {
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState("");

  return (
    <div className="flex flex-col gap-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            意見回饋
          </h1>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Feedback Form */}
        <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-6">
          <div className="flex items-center gap-4 mb-6">
            <MessageSquare className="w-6 h-6 text-[#00ff87]" />
            <h2 className="text-xl font-bold text-white">提供意見</h2>
          </div>
          
          <div className="space-y-6">
            {/* Rating */}
            <div>
              <label className="text-white font-medium mb-3 block">整體評分</label>
              <div className="flex gap-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => setRating(star)}
                    className={`p-1 transition-colors ${
                      star <= rating ? 'text-yellow-500' : 'text-gray-500 hover:text-yellow-400'
                    }`}
                  >
                    <Star className="w-8 h-8 fill-current" />
                  </button>
                ))}
              </div>
            </div>

            {/* Feedback Type */}
            <div>
              <label className="text-white font-medium mb-3 block">意見類型</label>
              <div className="grid grid-cols-2 gap-3">
                <button className="p-3 bg-[#2a2a3e]/50 rounded-lg hover:bg-[#00ff87]/20 hover:border-[#00ff87]/40 border border-transparent transition-all flex items-center gap-2">
                  <ThumbsUp className="w-4 h-4 text-green-500" />
                  <span className="text-white">建議</span>
                </button>
                <button className="p-3 bg-[#2a2a3e]/50 rounded-lg hover:bg-[#00ff87]/20 hover:border-[#00ff87]/40 border border-transparent transition-all flex items-center gap-2">
                  <ThumbsDown className="w-4 h-4 text-red-500" />
                  <span className="text-white">問題回報</span>
                </button>
              </div>
            </div>

            {/* Feedback Text */}
            <div>
              <label className="text-white font-medium mb-3 block">詳細意見</label>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                placeholder="請詳細描述您的意見或建議..."
                className="w-full h-32 p-4 bg-[#2a2a3e]/50 border border-[#2a2a3e] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#00ff87]/40 resize-none"
              />
            </div>

            {/* Submit Button */}
            <button className="w-full p-4 bg-[#00ff87]/20 text-[#00ff87] rounded-lg border border-[#00ff87]/40 hover:bg-[#00ff87]/30 transition-colors flex items-center justify-center gap-2">
              <Send className="w-4 h-4" />
              提交意見
            </button>
          </div>
        </Card>

        {/* Recent Feedback */}
        <Card className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-6">
          <div className="flex items-center gap-4 mb-6">
            <Star className="w-6 h-6 text-[#00ff87]" />
            <h2 className="text-xl font-bold text-white">最近回饋</h2>
          </div>
          
          <div className="space-y-4">
            <div className="p-4 bg-[#2a2a3e]/50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex gap-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="w-4 h-4 text-yellow-500 fill-current" />
                  ))}
                </div>
                <span className="text-gray-400 text-sm">2天前</span>
              </div>
              <p className="text-gray-300 text-sm">
                "平台功能很完整，使用體驗很好！希望能增加更多創作工具。"
              </p>
              <div className="mt-2 text-xs text-[#00ff87]">已處理</div>
            </div>
            
            <div className="p-4 bg-[#2a2a3e]/50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex gap-1">
                  {[1, 2, 3, 4].map((star) => (
                    <Star key={star} className="w-4 h-4 text-yellow-500 fill-current" />
                  ))}
                  <Star className="w-4 h-4 text-gray-500" />
                </div>
                <span className="text-gray-400 text-sm">5天前</span>
              </div>
              <p className="text-gray-300 text-sm">
                "載入速度有時候比較慢，希望能優化一下性能。"
              </p>
              <div className="mt-2 text-xs text-yellow-500">處理中</div>
            </div>
            
            <div className="p-4 bg-[#2a2a3e]/50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex gap-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="w-4 h-4 text-yellow-500 fill-current" />
                  ))}
                </div>
                <span className="text-gray-400 text-sm">1週前</span>
              </div>
              <p className="text-gray-300 text-sm">
                "很棒的平台！任務系統設計得很有趣，獎勵機制也很公平。"
              </p>
              <div className="mt-2 text-xs text-[#00ff87]">已處理</div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default FeedbackContent;
