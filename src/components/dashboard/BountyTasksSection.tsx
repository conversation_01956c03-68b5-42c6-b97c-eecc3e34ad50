import React, { useState } from "react";
import { ArrowRight } from "lucide-react";
import GameBountyCard from "./GameBountyCard";

interface BountyTask {
  id: string;
  title: string;
  description: string;
  reward: number;
  tag: string;
  timeLeft: string;
}

interface BountyTasksSectionProps {
  tasks: BountyTask[];
  onTaskClick: (taskId: string) => void;
}

const BountyTasksSection: React.FC<BountyTasksSectionProps> = ({
  tasks,
  onTaskClick,
}) => {
  const [showAllTasks, setShowAllTasks] = useState(false);

  return (
    <div className="bg-gradient-to-br from-[#ff2e63]/10 to-[#252a34]/80 backdrop-blur-sm border border-[#ff2e63]/20 rounded-3xl shadow-xl overflow-visible relative">
      {/* Game-style corner decorations */}
      <div className="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-[#ff2e63]/30 rounded-tl-3xl" />
      <div className="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-[#ff2e63]/30 rounded-tr-3xl" />
      <div className="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-[#ff2e63]/30 rounded-bl-3xl" />
      <div className="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-[#ff2e63]/30 rounded-br-3xl" />

      {/* Premium Badge */}
      <div className="absolute -top-3 -right-3 bg-gradient-to-r from-[#ff2e63] to-[#ff6b6b] text-white px-4 py-1 rounded-full text-sm font-bold shadow-lg z-10">
        PREMIUM
      </div>

      <div className="px-8 py-4 border-b border-[#ff2e63]/20">
        <div className="flex items-center gap-3">
          <h2 className="text-2xl font-bold text-[#ff2e63]">悬赏任务</h2>
          <div className="flex gap-2">
            <span className="text-sm text-[#ff2e63] bg-[#ff2e63]/10 px-2 py-0.5 rounded-full">
              高额奖励
            </span>
            <span className="text-sm text-[#ff2e63] bg-[#ff2e63]/10 px-2 py-0.5 rounded-full">
              限时挑战
            </span>
          </div>
        </div>
        <p className="mt-2 text-gray-400 text-sm">
          品牌合作推广 · 热门话题创作挑战 · 专属资源配额
        </p>
      </div>

      <div className="p-8">
        <div className="grid grid-cols-2 gap-6">
          {(showAllTasks ? tasks : tasks.slice(0, 2)).map((task) => (
            <GameBountyCard
              key={task.id}
              task={task}
              onClick={() => onTaskClick(task.id)}
              isPremium={task.tag === "品牌合作"}
            />
          ))}
        </div>
        {tasks.length > 2 && (
          <div className="mt-4 flex justify-end">
            <button
              onClick={() => setShowAllTasks(!showAllTasks)}
              className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-[#ff2e63]/10 text-[#ff2e63] rounded-lg hover:bg-[#ff2e63]/20 transition-colors hover:scale-105 text-sm"
            >
              {showAllTasks ? "收起" : "查看更多"}
              <ArrowRight
                className={`w-3.5 h-3.5 transition-transform duration-300 ${
                  showAllTasks ? "rotate-180" : ""
                }`}
              />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default BountyTasksSection;
