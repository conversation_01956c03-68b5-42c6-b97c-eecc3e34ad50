import React from "react";
import { Trophy } from "lucide-react";
import { PixelIcon } from "@/components/ui/pixel-icon";

interface AchievementToastProps {
  show: boolean;
  title?: string;
  description?: string;
  onClose?: () => void;
}

const AchievementToast: React.FC<AchievementToastProps> = ({
  show,
  title = "成就解锁！",
  description = "完成所有新手任务",
  onClose,
}) => {
  if (!show) return null;

  return (
    <div className="fixed top-16 right-4 bg-[#00ff87]/10 backdrop-blur-sm border border-[#00ff87]/20 rounded-xl p-4 shadow-lg animate-fade-in z-50">
      <div className="flex items-center gap-3">
        <PixelIcon size="sm" color="#00ff87">
          <Trophy className="w-4 h-4" />
        </PixelIcon>
        <div>
          <h4 className="text-[#00ff87] font-bold">{title}</h4>
          <p className="text-gray-300 text-sm">{description}</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-auto text-gray-400 hover:text-white transition-colors"
          >
            ×
          </button>
        )}
      </div>
    </div>
  );
};

export default AchievementToast;
