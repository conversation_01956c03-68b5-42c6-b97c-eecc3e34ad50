import React, { useState } from "react";
import { motion } from "framer-motion";
import { Trophy } from "lucide-react";

interface BountyTask {
  id: string;
  title: string;
  description: string;
  reward: number;
  tag: string;
  timeLeft: string;
}

interface GameBountyCardProps {
  task: BountyTask;
  onClick: () => void;
  isPremium?: boolean;
}

const GameBountyCard: React.FC<GameBountyCardProps> = ({
  task,
  onClick,
  isPremium = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02, y: -5 }}
      whileTap={{ scale: 0.98 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={onClick}
      className="group relative bg-[#1a1a2e]/80 backdrop-blur-sm rounded-xl border border-[#ff2e63]/20 hover:border-[#ff2e63]/40 transition-all duration-300 hover:shadow-lg hover:shadow-[#ff2e63]/10 cursor-pointer overflow-hidden"
    >
      {/* Corner decorations */}
      <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#ff2e63]/20 rounded-tl-xl group-hover:border-[#ff2e63]/40 transition-colors" />
      <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#ff2e63]/20 rounded-tr-xl group-hover:border-[#ff2e63]/40 transition-colors" />
      <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#ff2e63]/20 rounded-bl-xl group-hover:border-[#ff2e63]/40 transition-colors" />
      <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#ff2e63]/20 rounded-br-xl group-hover:border-[#ff2e63]/40 transition-colors" />

      {/* Hover effect overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-[#ff2e63]/5 to-transparent"
        animate={{
          opacity: isHovered ? 1 : 0,
        }}
        transition={{ duration: 0.3 }}
      />

      <div className="p-4">
        {/* Task Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <motion.div
              className="relative"
              animate={{
                y: isHovered ? [-2, 2, -2] : 0,
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              <div className="w-8 h-8 rounded-full bg-[#ff2e63]/10 flex items-center justify-center group-hover:bg-[#ff2e63]/20 transition-colors">
                <Trophy className="w-4 h-4 text-[#ff2e63]" />
              </div>
            </motion.div>
            <div className="flex flex-col">
              <h3 className="text-base font-bold text-white group-hover:text-[#ff2e63] transition-colors">
                {task.title}
              </h3>
              <div className="flex items-center gap-2">
                <span className="text-[#ff2e63] text-xs font-medium bg-[#ff2e63]/10 px-2 py-0.5 rounded-full">
                  {task.tag}
                </span>
                <span className="text-xs text-[#ff2e63]">
                  {task.reward} RPX
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Task Description */}
        <p className="text-gray-400 text-sm mb-4 group-hover:text-gray-300 transition-colors line-clamp-2">
          {task.description}
        </p>

        {/* Task Footer */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <motion.div
              className="w-1.5 h-1.5 bg-[#ff2e63] rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <span className="text-xs text-gray-400">
              {Math.floor(Math.random() * 1000)} 人参与
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-xs text-gray-400">剩余时间</div>
            <motion.div
              className="relative"
              animate={{
                scale: isHovered ? [1, 1.1, 1] : 1,
              }}
              transition={{
                duration: 0.5,
              }}
            >
              <div className="text-[#ff2e63] font-bold text-xs">
                {task.timeLeft}
              </div>
              <motion.div
                className="absolute -right-1 -top-1 w-1.5 h-1.5"
                animate={{
                  scale: [1, 1.2, 1],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                <div className="absolute inset-0 bg-[#ff2e63] rounded-full opacity-30" />
                <div className="absolute inset-0 bg-[#ff2e63] rounded-full animate-ping" />
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default GameBountyCard;
