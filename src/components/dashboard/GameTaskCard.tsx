import React from "react";
import { CheckCircle2, Clock, AlertCircle, Coins } from "lucide-react";
import GameCard from "@/components/ui/GameCard";
import GameButton from "@/components/ui/GameButton";
import type { CreatorKitTask } from "@/types/creator-kit";

interface Task extends CreatorKitTask {
  status: "pending" | "completed" | "in_progress";
  reward: number;
  deadline?: string;
}

interface GameTaskCardProps {
  task: Task;
  onClick: (task: Task) => void;
}

const GameTaskCard: React.FC<GameTaskCardProps> = ({ task, onClick }) => {
  const getStatusColor = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return "text-green-400";
      case "in_progress":
        return "text-yellow-400";
      default:
        return "text-gray-400";
    }
  };

  const getStatusIcon = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="w-4 h-4" />;
      case "in_progress":
        return <Clock className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  return (
    <GameCard onClick={() => onClick(task)} className="hover:cursor-pointer">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <div
              className={`w-6 h-6 rounded-lg bg-[#2a2a3e] flex items-center justify-center ${getStatusColor(
                task.status
              )}`}
            >
              {getStatusIcon(task.status)}
            </div>
            <h3 className="text-lg font-medium text-white">{task.title}</h3>
          </div>
          <p className="text-gray-400 text-sm mb-4">{task.description}</p>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1 text-[#00cec9]">
              <Coins className="w-4 h-4" />
              <span className="text-sm">{task.reward}</span>
            </div>
            {task.deadline && (
              <div className="flex items-center gap-1 text-gray-400">
                <Clock className="w-4 h-4" />
                <span className="text-sm">{task.deadline}</span>
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <GameButton
            variant="secondary"
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              onClick(task);
            }}
          >
            查看详情
          </GameButton>
        </div>
      </div>
    </GameCard>
  );
};

export default GameTaskCard;
