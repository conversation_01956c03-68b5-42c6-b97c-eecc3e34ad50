import React from "react";
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";
import type { Engine } from "tsparticles-engine";
import type { ISourceOptions } from "tsparticles-engine";

interface DashboardBackgroundProps {
  currentPattern?: "grid" | "hexagon" | "cyberpunk";
  children?: React.ReactNode;
}

const DashboardBackground: React.FC<DashboardBackgroundProps> = ({
  currentPattern = "hexagon",
  children,
}) => {
  const particlesInit = async (engine: Engine) => {
    await loadSlim(engine);
  };

  const particlesOptions: ISourceOptions = {
    particles: {
      number: {
        value: 50,
        density: {
          enable: true,
          value_area: 800,
        },
      },
      color: {
        value: "#4ecdc4",
      },
      shape: {
        type: "circle",
      },
      opacity: {
        value: 0.6,
        random: false,
      },
      size: {
        value: 2,
        random: true,
      },
      line_linked: {
        enable: true,
        distance: 150,
        color: "#4ecdc4",
        opacity: 0.3,
        width: 1,
      },
      move: {
        enable: true,
        speed: 2,
        direction: "none",
        random: false,
        straight: false,
        out_mode: "out",
        bounce: false,
      },
    },
    interactivity: {
      detect_on: "canvas",
      events: {
        onhover: {
          enable: true,
          mode: "grab",
        },
        onclick: {
          enable: true,
          mode: "push",
        },
        resize: true,
      },
      modes: {
        grab: {
          distance: 140,
          line_linked: {
            opacity: 1,
          },
        },
        push: {
          particles_nb: 4,
        },
      },
    },
    retina_detect: true,
  };



  return (
    <div className="min-h-screen w-full relative bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748]">
      {/* Base Background Layer - Ensure it's always visible */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748] z-0" />



      {/* Conditional Background Patterns */}
      {currentPattern === "hexagon" && (
        <div className="absolute inset-0 overflow-hidden">
          {/* 简化的六角蜂巢背景 */}
          <div
            className="absolute inset-0 opacity-20 hex-float"
            style={{
              backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'52\' viewBox=\'0 0 60 52\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpolygon points=\'30,2 52,15 52,37 30,50 8,37 8,15\' fill=\'none\' stroke=\'%2300ff87\' stroke-width=\'0.5\' opacity=\'0.3\'/%3E%3C/svg%3E")',
              backgroundSize: '60px 52px',
            }}
          />
          <div
            className="absolute inset-0 opacity-10 hex-glow"
            style={{
              backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'120\' height=\'104\' viewBox=\'0 0 120 104\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpolygon points=\'60,4 104,30 104,74 60,100 16,74 16,30\' fill=\'none\' stroke=\'%2360efff\' stroke-width=\'1\' opacity=\'0.2\'/%3E%3C/svg%3E")',
              backgroundSize: '120px 104px',
            }}
          />
          <div
            className="absolute inset-0 opacity-5 hex-pulse"
            style={{
              backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'180\' height=\'156\' viewBox=\'0 0 180 156\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpolygon points=\'90,6 156,45 156,111 90,150 24,111 24,45\' fill=\'%2300ff87\' opacity=\'0.1\'/%3E%3Cpolygon points=\'90,6 156,45 156,111 90,150 24,111 24,45\' fill=\'none\' stroke=\'%2300ff87\' stroke-width=\'1.5\' opacity=\'0.3\'/%3E%3C/svg%3E")',
              backgroundSize: '180px 156px',
            }}
          />
        </div>
      )}

      {/* Particle Background - Only show for grid pattern */}
      {currentPattern === "grid" && (
        <Particles
          id="tsparticles"
          init={particlesInit}
          options={particlesOptions}
          className="absolute inset-0 z-0"
        />
      )}

      {/* Enhanced Animated Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,_rgba(0,255,135,0.15)_0%,_transparent_50%)] opacity-50 animate-pulse" />

      {/* Hexagonal pattern background */}
      {currentPattern === "grid" && (
        <div
          className="absolute inset-0 opacity-15"
          style={{
            backgroundImage: `
              radial-gradient(circle at 25% 25%, rgba(0,255,135,0.08) 1.5px, transparent 2px),
              radial-gradient(circle at 75% 25%, rgba(96,239,255,0.06) 1.5px, transparent 2px),
              radial-gradient(circle at 50% 75%, rgba(0,255,135,0.04) 1.5px, transparent 2px)
            `,
            backgroundSize: '40px 35px',
            backgroundPosition: '0 0, 20px 0, 10px 17.5px'
          }}
        />
      )}

      {/* Cyberpunk pattern background */}
      <div
        className={`absolute inset-0 bg-repeat transition-opacity duration-500 ${
          currentPattern === "cyberpunk" ? "opacity-60" : "opacity-0"
        }`}
        style={{
          backgroundImage: currentPattern === "cyberpunk"
            ? 'url("data:image/svg+xml,%3Csvg width=\'40\' height=\'40\' viewBox=\'0 0 40 40\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' stroke=\'%2360efff\' stroke-width=\'1\' opacity=\'0.3\'%3E%3Cpath d=\'M0 20h40M20 0v40\'/%3E%3Ccircle cx=\'20\' cy=\'20\' r=\'3\' fill=\'%2300ff87\' opacity=\'0.5\'/%3E%3C/g%3E%3C/svg%3E")'
            : 'none'
        }}
      />

      {/* Additional cyberpunk effects */}
      {currentPattern === "cyberpunk" && (
        <>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,_rgba(96,239,255,0.1)_0%,_transparent_50%)] opacity-40" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,_rgba(0,255,135,0.1)_0%,_transparent_50%)] opacity-40" />
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `
                radial-gradient(circle at 30% 30%, rgba(96,239,255,0.06) 2px, transparent 3px),
                radial-gradient(circle at 70% 70%, rgba(0,255,135,0.06) 2px, transparent 3px),
                radial-gradient(circle at 50% 10%, rgba(96,239,255,0.04) 1px, transparent 2px),
                radial-gradient(circle at 10% 50%, rgba(0,255,135,0.04) 1px, transparent 2px),
                radial-gradient(circle at 90% 50%, rgba(96,239,255,0.04) 1px, transparent 2px)
              `,
              backgroundSize: '60px 52px',
              backgroundPosition: '0 0, 30px 26px, 15px -13px, -15px 13px, 45px 13px'
            }}
          />
        </>
      )}

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default DashboardBackground;
