import React from "react";
import { Target, Crown } from "lucide-react";
import GameCareerCard from "./GameCareerCard";

interface CareerTask {
  id: string;
  title: string;
  description: string;
  reward: number;
  progress: number;
  progressTotal: number;
  tag: string;
  cta: string;
  timeLeft: string;
  icon: "youtube";
}

interface CareerTasksSectionProps {
  tasks: CareerTask[];
  onTaskClick: (taskId: string) => void;
}

const CareerTasksSection: React.FC<CareerTasksSectionProps> = ({
  tasks,
  onTaskClick,
}) => {
  return (
    <div className="relative bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl shadow-lg overflow-hidden group hover:bg-[#1a1a2e]/90 transition-all duration-300">
      {/* Corner decorations */}
      <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#2a2a3e] rounded-tl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
      <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#2a2a3e] rounded-tr-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
      <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#2a2a3e] rounded-bl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
      <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#2a2a3e] rounded-br-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />

      <div className="px-8 py-4 border-b border-[#2a2a3e] flex justify-between items-center">
        <div className="flex items-center gap-3">
          <h2 className="text-xl font-bold text-[#00ff87] group-hover:text-[#60efff] transition-colors duration-300">
            職業賞金任務
          </h2>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 bg-[#2a2a3e]/50 px-2 py-1 rounded-lg group-hover:bg-[#2a2a3e]/70 transition-colors duration-300">
            <Target className="w-4 h-4 text-[#00ff87]" />
            <span className="text-[#00ff87] text-sm">創作者成長</span>
          </div>
          <div className="flex items-center gap-1 bg-[#2a2a3e]/50 px-2 py-1 rounded-lg group-hover:bg-[#2a2a3e]/70 transition-colors duration-300">
            <Crown className="w-4 h-4 text-[#00ff87]" />
            <span className="text-[#00ff87] text-sm">KPI 挑戰</span>
          </div>
        </div>
      </div>

      <div className="p-8">
        <div className="grid grid-cols-2 gap-6">
          {tasks.map((task) => (
            <GameCareerCard
              key={task.id}
              task={task}
              onClick={() => onTaskClick(task.id)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default CareerTasksSection;
