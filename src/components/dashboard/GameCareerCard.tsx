import React, { useState } from "react";
import { motion } from "framer-motion";
import { Crown } from "lucide-react";

interface CareerTask {
  id: string;
  title: string;
  description: string;
  reward: number;
  progress: number;
  progressTotal: number;
  tag: string;
  cta: string;
  timeLeft: string;
  icon: "youtube";
}

interface GameCareerCardProps {
  task: CareerTask;
  onClick: () => void;
}

const GameCareerCard: React.FC<GameCareerCardProps> = ({ task, onClick }) => {
  const [isHovered, setIsHovered] = useState(false);
  const progress = (task.progress / task.progressTotal) * 100;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02, y: -5 }}
      whileTap={{ scale: 0.98 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={onClick}
      className="group relative bg-[#1a1a2e]/80 backdrop-blur-sm rounded-xl border border-[#00ff87]/20 hover:border-[#00ff87]/40 transition-all duration-300 hover:shadow-lg hover:shadow-[#00ff87]/10 cursor-pointer overflow-hidden"
    >
      {/* Corner decorations */}
      <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#00ff87]/20 rounded-tl-xl group-hover:border-[#00ff87]/40 transition-colors" />
      <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#00ff87]/20 rounded-tr-xl group-hover:border-[#00ff87]/40 transition-colors" />
      <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#00ff87]/20 rounded-bl-xl group-hover:border-[#00ff87]/40 transition-colors" />
      <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#00ff87]/20 rounded-br-xl group-hover:border-[#00ff87]/40 transition-colors" />

      {/* Hover effect overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-[#00ff87]/5 to-transparent"
        animate={{
          opacity: isHovered ? 1 : 0,
        }}
        transition={{ duration: 0.3 }}
      />

      <div className="p-4">
        {/* Task Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <motion.div
              className="relative"
              animate={{
                y: isHovered ? [-2, 2, -2] : 0,
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              <div className="w-8 h-8 rounded-full bg-[#00ff87]/10 flex items-center justify-center group-hover:bg-[#00ff87]/20 transition-colors">
                <Crown className="w-4 h-4 text-[#00ff87]" />
              </div>
            </motion.div>
            <div className="flex flex-col">
              <h3 className="text-base font-bold text-white group-hover:text-[#00ff87] transition-colors">
                {task.title}
              </h3>
              <div className="flex items-center gap-2">
                <span className="text-[#00ff87] text-xs font-medium bg-[#00ff87]/10 px-2 py-0.5 rounded-full">
                  {task.tag}
                </span>
                <span className="text-xs text-[#00ff87]">
                  {task.reward} RPX
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Task Description */}
        <p className="text-gray-400 text-sm mb-4 group-hover:text-gray-300 transition-colors">
          {task.description}
        </p>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-gray-400">进度</span>
            <span className="text-xs text-[#00ff87]">
              {progress.toFixed(0)}%
            </span>
          </div>
          <div className="h-2 bg-[#00ff87]/10 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-[#00ff87] rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 1, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Task Footer */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <motion.div
              className="w-1.5 h-1.5 bg-[#00ff87] rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <span className="text-xs text-gray-400">
              {task.progress}/{task.progressTotal} 完成
            </span>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="text-[#00ff87] text-xs font-medium bg-[#00ff87]/10 px-3 py-1 rounded-full hover:bg-[#00ff87]/20 transition-colors"
          >
            {task.cta}
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default GameCareerCard;
