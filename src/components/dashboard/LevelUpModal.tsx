import React from "react";
import { Sparkles } from "lucide-react";
import { PixelIcon } from "@/components/ui/pixel-icon";

interface LevelUpModalProps {
  show: boolean;
  currentLevel: number;
  onClose: () => void;
}

const LevelUpModal: React.FC<LevelUpModalProps> = ({
  show,
  currentLevel,
  onClose,
}) => {
  if (!show) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50 animate-fade-in">
      <div className="relative bg-[#1a1a2e] border border-[#00ff87]/20 rounded-2xl p-10 text-center overflow-hidden max-w-md w-full mx-4">
        {/* 背景闪光效果 */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#00ff87]/10 via-[#00ff87]/5 to-[#00ff87]/10 animate-pulse"></div>

        {/* 粒子效果 */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-[#00ff87] rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `float ${Math.random() * 2 + 1}s infinite`,
              }}
            />
          ))}
        </div>

        {/* 内容 */}
        <div className="relative z-10">
          <div className="animate-bounce mb-6">
            <PixelIcon size="lg" color="#00ff87" className="mx-auto scale-125">
              <Sparkles className="w-10 h-10 animate-spin" />
            </PixelIcon>
          </div>

          <h2 className="text-5xl font-bold text-[#00ff87] mb-4 animate-pulse">
            等级提升！
          </h2>

          <div className="relative mb-8">
            <p className="text-gray-300 text-xl mb-2">恭喜你达到</p>
            <div className="text-7xl font-bold text-[#00ff87] animate-scale">
              {currentLevel}
              <span className="text-3xl ml-2">级</span>
            </div>
          </div>

          <button
            onClick={onClose}
            className="relative bg-[#00ff87]/10 text-[#00ff87] px-8 py-3 rounded-lg hover:bg-[#00ff87]/20 transition-all duration-300 transform hover:scale-105 hover:shadow-[0_0_15px_rgba(0,255,135,0.3)] text-lg font-medium"
          >
            继续
          </button>
        </div>
      </div>
    </div>
  );
};

export default LevelUpModal;
