import React from "react";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import TokenBubbleMapD3 from "@/components/TokenBubbleMap/D3ForceImplementation";
import { useRouter } from "next/navigation";

interface TokenPoolData {
  totalAssets: number;
  change24h: number;
  tokens: {
    symbol: string;
    amount: number;
    usdValue: number;
    percentage: number;
    change24h?: number;
  }[];
  recentTransactions: {
    type: "in" | "out";
    amount: number;
    symbol: string;
    timestamp: string;
    status: "success" | "pending" | "failed";
  }[];
  taskPool: {
    total: number;
    used: number;
    byType: {
      type: string;
      amount: number;
      percentage: number;
    }[];
  };
}

interface TokenPoolOverviewProps {
  data: TokenPoolData;
}

const TokenPoolOverview: React.FC<TokenPoolOverviewProps> = ({ data }) => {
  const router = useRouter();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="relative bg-gradient-to-br from-[#1a1a2e]/80 to-[#16213e]/80 backdrop-blur-sm border border-[#00ff87]/20 rounded-xl p-6 overflow-hidden"
    >
      {/* 装饰元素 */}
      <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#00ff87]/30 rounded-tl-xl" />
      <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#00ff87]/30 rounded-tr-xl" />
      <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#00ff87]/30 rounded-bl-xl" />
      <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#00ff87]/30 rounded-br-xl" />

      {/* 背景效果 */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,_rgba(0,255,135,0.1)_0%,_transparent_50%)] animate-pulse" />

      {/* 头部信息 */}
      <div className="flex items-center justify-between mb-6 relative z-10">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-bold text-[#00ff87] tracking-wider">
            代币池概览
          </h3>
          <div className="flex items-center gap-2">
            <span className="text-sm text-[#00ff87] bg-[#00ff87]/10 px-2 py-0.5 rounded-full border border-[#00ff87]/20">
              总资产 ${(data.totalAssets / 1000).toFixed(1)}K
            </span>
          </div>
        </div>
        <button
          onClick={() => router.push("/token-pool")}
          className="flex items-center gap-2 text-[#00ff87] hover:text-white transition-colors group"
        >
          <span className="group-hover:translate-x-1 transition-transform">
            查看详情
          </span>
          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
        </button>
      </div>

      {/* 泡泡图 */}
      <TokenBubbleMapD3 data={data} />
    </motion.div>
  );
};

export default TokenPoolOverview;
