"use client";

import React from "react";
import { motion } from "framer-motion";
import { Trophy } from "lucide-react";

interface Achievement {
  id: string;
  name: string;
  icon: string;
  rarity: "common" | "rare" | "epic" | "legendary";
  obtainedAt: string;
}

interface AchievementsSectionProps {
  achievements: Achievement[];
  isVisible?: boolean;
}

const getRarityColor = (rarity: string) => {
  switch (rarity) {
    case "common":
      return "from-gray-400 to-gray-600";
    case "rare":
      return "from-blue-400 to-blue-600";
    case "epic":
      return "from-purple-400 to-purple-600";
    case "legendary":
      return "from-yellow-400 to-orange-500";
    default:
      return "from-gray-400 to-gray-600";
  }
};

const AchievementsSection: React.FC<AchievementsSectionProps> = ({
  achievements,
  isVisible = false,
}) => {
  if (!isVisible) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1.2 }}
      className="max-w-6xl mx-auto mt-4 bg-white/5 backdrop-blur-sm rounded-lg p-3 border border-white/10"
    >
      <div className="flex items-center gap-2 mb-3">
        <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#FFD700] to-[#FFA500] flex items-center justify-center">
          <Trophy className="w-4 h-4 text-white" />
        </div>
        <h3 className="text-base font-bold text-white">
          已获成就 · {achievements.length}
        </h3>
      </div>

      <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 gap-2">
        {achievements.map((achievement, index) => (
          <motion.div
            key={achievement.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            className="group relative"
          >
            <div
              className={`w-full aspect-square rounded-md bg-gradient-to-br ${getRarityColor(
                achievement.rarity
              )}/20 hover:${getRarityColor(
                achievement.rarity
              )}/30 transition-colors flex flex-col items-center justify-center p-1.5`}
            >
              <div className="text-xl mb-0.5">{achievement.icon}</div>
              <div className="text-[10px] text-white/80 font-medium text-center leading-tight truncate w-full">
                {achievement.name}
              </div>
            </div>

            <div className="absolute -top-1 -right-1">
              <div
                className={`w-1.5 h-1.5 rounded-full bg-gradient-to-br ${getRarityColor(
                  achievement.rarity
                )}`}
              />
            </div>

            <div className="opacity-0 group-hover:opacity-100 transition-opacity absolute -top-6 left-1/2 -translate-x-1/2 bg-black/90 text-white text-[10px] px-1.5 py-0.5 rounded whitespace-nowrap z-10">
              {achievement.obtainedAt}
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default AchievementsSection;
