import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Task, TaskCategory, TaskStatus, TaskType } from "@/types/task";

interface TaskCardProps {
  task: Task;
  onAccept?: () => void;
  onView?: () => void;
}

const TaskCard: React.FC<TaskCardProps> = ({ task, onAccept, onView }) => {
  const getTaskTypeLabel = (type: TaskType): string => {
    switch (type) {
      case TaskType.SHORT_VIDEO:
        return "Short Video";
      case TaskType.PROJECT_PROMOTION:
        return "Project Promotion";
      case TaskType.COMMUNITY_INTERACTION:
        return "Community Interaction";
      case TaskType.DEEP_CONTENT:
        return "Deep Content";
      case TaskType.PROJECT_ANALYSIS:
        return "Project Analysis";
      case TaskType.COMMUNITY_OPERATION:
        return "Community Operation";
      default:
        return "Unknown";
    }
  };

  const getTaskStatusColor = (status: TaskStatus): string => {
    switch (status) {
      case TaskStatus.OPEN:
        return "bg-green-100 text-green-800";
      case TaskStatus.IN_PROGRESS:
        return "bg-blue-100 text-blue-800";
      case TaskStatus.SUBMITTED:
        return "bg-yellow-100 text-yellow-800";
      case TaskStatus.UNDER_REVIEW:
        return "bg-orange-100 text-orange-800";
      case TaskStatus.COMPLETED:
        return "bg-purple-100 text-purple-800";
      case TaskStatus.REJECTED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  return (
    <Card className="border shadow-sm hover:border-primary/50 transition-colors">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={task.projectLogo} alt={task.projectName} />
              <AvatarFallback>{task.projectName.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="font-medium text-sm text-muted-foreground">
              {task.projectName}
            </span>
          </div>
          <Badge
            variant="secondary"
            className={getTaskStatusColor(task.status)}
          >
            {task.status}
          </Badge>
        </div>
        <h3 className="font-semibold text-lg mt-2">{task.title}</h3>
        <div className="flex flex-wrap gap-2 mt-1">
          <Badge variant="outline" className="bg-blue-50">
            {getTaskTypeLabel(task.type)}
          </Badge>
          {task.category === TaskCategory.BOUNTY && (
            <Badge className="bg-primary text-primary-foreground">Bounty</Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground line-clamp-3">
          {task.description}
        </p>

        <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
          <div>
            <p className="text-muted-foreground">Reward</p>
            <p className="font-semibold text-green-600">{task.reward} USD</p>
          </div>
          <div>
            <p className="text-muted-foreground">Experience</p>
            <p className="font-semibold text-primary">
              +{task.experiencePoints} XP
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">Deadline</p>
            <p className="font-medium">{formatDate(task.deadline)}</p>
          </div>
          <div>
            {task.remainingSlots !== undefined && (
              <>
                <p className="text-muted-foreground">Slots</p>
                <p className="font-medium">
                  {task.remainingSlots}/{task.maxParticipants}
                </p>
              </>
            )}
          </div>
        </div>

        {task.requirements && task.requirements.length > 0 && (
          <div className="mt-4">
            <p className="text-xs font-semibold text-muted-foreground mb-1">
              Requirements
            </p>
            <ul className="text-xs list-disc list-inside space-y-1">
              {task.requirements.slice(0, 2).map((req, index) => (
                <li key={index} className="truncate">
                  {req}
                </li>
              ))}
              {task.requirements.length > 2 && (
                <li className="text-muted-foreground">
                  +{task.requirements.length - 2} more
                </li>
              )}
            </ul>
          </div>
        )}
      </CardContent>
      <CardFooter className="border-t pt-4">
        {task.status === TaskStatus.OPEN ? (
          <Button className="w-full" onClick={onAccept}>
            Accept Task
          </Button>
        ) : (
          <Button className="w-full" variant="outline" onClick={onView}>
            View Details
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default TaskCard;
