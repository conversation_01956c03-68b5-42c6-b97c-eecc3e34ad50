"use client";

import React, { useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useLayout } from "@/contexts/LayoutContext";
import ProfileSyncWrapper from "@/components/ProfileSyncWrapper";
import ThreePanelLayout from "@/components/ThreePanelLayout";
import {
  DashboardBackground,
  AchievementToast,
  LevelUpModal,
} from "@/components/dashboard";

interface DashboardLayoutProps {
  children?: React.ReactNode;
  showBountySidebar?: boolean;
  enableDynamicContent?: boolean;
  forceContent?: string;
  backgroundPattern?: "grid" | "hexagon" | "cyberpunk";
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  showBountySidebar,
  enableDynamicContent = true,
  forceContent,
  backgroundPattern,
}) => {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const { layoutState, setBackgroundPattern } = useLayout();

  // Update background pattern if provided
  useEffect(() => {
    if (backgroundPattern && backgroundPattern !== layoutState.backgroundPattern) {
      setBackgroundPattern(backgroundPattern);
    }
  }, [backgroundPattern, layoutState.backgroundPattern, setBackgroundPattern]);

  useEffect(() => {
    if (!isLoading && !user) {
      router.push("/auth");
    }
  }, [user, isLoading, router]);

  if (!user) {
    return null;
  }

  return (
    <ProfileSyncWrapper>
      <DashboardBackground currentPattern={layoutState.backgroundPattern}>
        {/* Achievement Toast */}
        <AchievementToast
          show={false}
          onClose={() => {}}
        />

        {/* Level Up Animation */}
        <LevelUpModal
          show={false}
          currentLevel={1}
          onClose={() => {}}
        />

        <ThreePanelLayout
          showBountySidebar={showBountySidebar !== undefined ? showBountySidebar && !isLoading : layoutState.showHomeSidebar && !isLoading}
          enableDynamicContent={enableDynamicContent}
          forceContent={forceContent}
        >
          {children}
        </ThreePanelLayout>
      </DashboardBackground>
    </ProfileSyncWrapper>
  );
};

export default DashboardLayout;
