import React from "react";
import { <PERSON>, <PERSON>, ArrowRight } from "lucide-react";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface SimpleTask {
  id: string;
  title: string;
  description: string;
  reward: number;
  tag: string;
  timeLeft: string;
  cta: string;
}

interface TaskDetailModalProps {
  task: SimpleTask;
  onClose: () => void;
  onAccept?: () => void;
}

const TaskDetailModal: React.FC<TaskDetailModalProps> = ({
  task,
  onClose,
  onAccept,
}) => {
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trophy className="w-5 h-5 text-primary" />
              <span>Task Details</span>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold">{task.title}</h2>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="secondary">{task.tag}</Badge>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Description</h3>
            <p className="text-muted-foreground">{task.description}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Reward</h3>
              <p className="text-2xl font-bold text-green-600">
                {task.reward} RPX
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Time Left</h3>
              <p className="text-muted-foreground">{task.timeLeft}</p>
            </div>
          </div>

          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button onClick={onAccept}>
              {task.cta}
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TaskDetailModal;
