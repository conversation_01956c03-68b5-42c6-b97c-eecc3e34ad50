"use client";

import React, { useEffect, useState, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Link,
  Unlink,
  Plus,
  Youtube,
  Twitter,
  MessageSquare,
  Wallet,
  Mail,
  Send,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import {
  usePlatformConnections,
  useConnections,
  useCompletionPercentage,
  useConnectionActions,
  type PlatformConnection,
  type ConnectionsData,
} from "@/hooks/usePlatformConnections";

// Constants
const ANIMATION_DELAY = 0.1;
const ANIMATION_DURATION = 0.8;

// Types
type IconType = "Wallet" | "Youtube" | "Twitter" | "MessageSquare" | "Mail" | "Send";
type PlatformCategory = "wallet" | "social" | "contact";

interface PlatformConfig {
  name: string;
  iconType: IconType;
  required: boolean;
  description: string;
  category: PlatformCategory;
}

interface ModalContent {
  type: "connect" | "disconnect" | "error";
  platform: string;
  message?: string;
}

interface AccountConnectionsProps {
  onConnectionUpdate?: (connections: ConnectionsData) => void;
  className?: string;
}

// Platform configurations - cleaned up commented code
const PLATFORM_CONFIGS: Record<string, PlatformConfig> = {
  MetaMask: {
    name: "MetaMask",
    iconType: "Wallet",
    required: true,
    description: "连接你的Web3钱包",
    category: "wallet",
  },
  YouTube: {
    name: "YouTube",
    iconType: "Youtube",
    required: true,
    description: "连接你的YouTube频道",
    category: "social",
  },
  Twitter: {
    name: "Twitter",
    iconType: "Twitter",
    required: true,
    description: "连接你的Twitter账号",
    category: "social",
  },
} as const;

// Icon mapping for better type safety
const ICON_COMPONENTS: Record<IconType, React.ComponentType<any>> = {
  Wallet,
  Youtube,
  Twitter,
  MessageSquare,
  Mail,
  Send,
};

// Status color mapping
const STATUS_COLORS: Record<PlatformConnection["status"], string> = {
  "已绑定": "text-[#00CEC9]",
  "已认证": "text-[#00CEC9]",
  "未绑定": "text-[#00CEC9]",
  "连接中": "text-blue-400",
  "错误": "text-[#ff2e63]",
};

// UI Helper Components
const StatusIcon: React.FC<{ status: PlatformConnection["status"] }> = ({ status }) => {
  const iconProps = { className: "w-3 h-3" };
  
  switch (status) {
    case "已绑定":
    case "已认证":
      return <CheckCircle {...iconProps} className="w-3 h-3 text-[#00CEC9]" />;
    case "连接中":
      return <Loader2 {...iconProps} className="w-3 h-3 text-[#00CEC9] animate-spin" />;
    case "错误":
      return <AlertCircle {...iconProps} className="w-3 h-3 text-[#ff2e63]" />;
    default:
      return null;
  }
};

const PlatformIcon: React.FC<{ iconType: string }> = ({ iconType }) => {
  const IconComponent = ICON_COMPONENTS[iconType as IconType] || Link;
  return <IconComponent className="w-4 h-4 text-[#00CEC9]" />;
};

// Connection Modal Component
const ConnectionModal: React.FC<{
  show: boolean;
  content: ModalContent | null;
  onClose: () => void;
  onConfirmDisconnect: () => Promise<void>;
}> = ({ show, content, onClose, onConfirmDisconnect }) => {
  if (!show || !content) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-[#1a1a2e] rounded-xl p-6 w-full max-w-md border border-[#00CEC9]/30 relative"
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 w-6 h-6 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors"
        >
          <X className="w-4 h-4 text-white" />
        </button>

        {content.type === "disconnect" && (
          <>
            <h3 className="text-lg font-bold text-white mb-4">
              断开 {content.platform} 连接
            </h3>
            <p className="text-white/70 mb-6">
              确定要断开与 {content.platform} 的连接吗？这将移除相关的账号信息。
            </p>
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors"
              >
                取消
              </button>
              <button
                onClick={onConfirmDisconnect}
                className="flex-1 px-4 py-2 bg-[#ff2e63] text-white rounded-lg hover:bg-[#ff2e63]/80 transition-colors"
              >
                断开连接
              </button>
            </div>
          </>
        )}

        {content.type === "error" && (
          <>
            <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-[#ff2e63]" />
              连接失败
            </h3>
            <p className="text-white/70 mb-6">
              无法连接到 {content.platform}：{content.message}
            </p>
            <button
              onClick={onClose}
              className="w-full px-4 py-2 bg-[#00CEC9] text-white rounded-lg hover:bg-[#00CEC9]/80 transition-colors"
            >
              确定
            </button>
          </>
        )}
      </motion.div>
    </div>
  );
};

// Main Component
const AccountConnections: React.FC<AccountConnectionsProps> = ({
  onConnectionUpdate,
  className = "",
}) => {
  const { user } = useAuth();
  const connections = useConnections();
  const completionPercentage = useCompletionPercentage();
  const { connectPlatform, disconnectPlatform, initializeConnections } = useConnectionActions();

  // Zustand store state
  const { isInitialized, globalLoading } = usePlatformConnections();

  // Local component state
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [modalContent, setModalContent] = useState<ModalContent | null>(null);

  // Helper functions
  const getStatusText = useCallback((connection: PlatformConnection): string => {
    switch (connection.status) {
      case "未绑定":
        return "点击绑定";
      case "连接中":
        return "连接中...";
      case "错误":
        return connection.error || "连接失败";
      default:
        return connection.handle || connection.address || "已连接";
    }
  }, []);

  const isConnected = useCallback((status: PlatformConnection["status"]): boolean => {
    return status === "已绑定" || status === "已认证";
  }, []);

  // Event handlers
  const handleConnectPlatform = useCallback(async (platformId: string) => {
    if (!user?.id) {
      console.error("User not authenticated");
      return;
    }

    const platform = connections[platformId];
    if (!platform || platform.status === "连接中") return;

    setConnectingPlatform(platformId);

    try {
      await connectPlatform(platformId, user.id);
    } catch (error) {
      console.error(`Error connecting to ${platformId}:`, error);
      setModalContent({
        type: "error",
        platform: platform.platform,
        message: error instanceof Error ? error.message : "连接失败，请重试",
      });
      setShowModal(true);
    } finally {
      setConnectingPlatform(null);
    }
  }, [user?.id, connections, connectPlatform]);

  const handleDisconnectPlatform = useCallback((platformId: string) => {
    const platform = connections[platformId];
    if (!platform) return;

    setModalContent({
      type: "disconnect",
      platform: platform.platform,
    });
    setShowModal(true);
  }, [connections]);

  const confirmDisconnect = useCallback(async () => {
    if (!modalContent || modalContent.type !== "disconnect" || !user?.id) return;

    const platformId = Object.keys(connections).find(
      (key) => connections[key].platform === modalContent.platform
    );

    if (!platformId) return;

    try {
      await disconnectPlatform(platformId, user.id);
    } catch (error) {
      console.error("Error disconnecting platform:", error);
    } finally {
      setShowModal(false);
      setModalContent(null);
    }
  }, [modalContent, user?.id, connections, disconnectPlatform]);

  const handleQuickConnect = useCallback(() => {
    const firstUnconnected = Object.values(connections).find(
      (c) => c.status === "未绑定"
    );
    if (firstUnconnected) {
      handleConnectPlatform(firstUnconnected.id);
    }
  }, [connections, handleConnectPlatform]);

  const closeModal = useCallback(() => {
    setShowModal(false);
    setModalContent(null);
  }, []);

  // Effects
  useEffect(() => {
    if (!isInitialized && user?.id) {
      initializeConnections(user.id);
    }
  }, [isInitialized, user?.id, initializeConnections]);

  useEffect(() => {
    if (Object.keys(connections).length > 0) {
      onConnectionUpdate?.(connections);
    }
  }, [connections, onConnectionUpdate]);

  // Loading state
  if (!isInitialized || globalLoading) {
    return (
      <div className={`relative ${className}`}>
        <div className="flex items-center justify-center p-8">
          <Loader2 className="w-8 h-8 text-[#00CEC9] animate-spin" />
          <span className="ml-3 text-white/60">正在加载连接状态...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-[#00CEC9]/20 flex items-center justify-center">
            <Link className="w-4 h-4 text-[#00CEC9]" />
          </div>
          <div>
            <h3 className="text-base font-bold text-white">账号绑定</h3>
            <div className="text-xs text-white/60">完成度 {completionPercentage}%</div>
          </div>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleQuickConnect}
          className="px-3 py-1.5 bg-[#00CEC9]/10 text-[#00CEC9] rounded-lg text-sm font-medium hover:bg-[#00CEC9]/20 transition-all flex items-center gap-1 border border-[#00CEC9]/20"
        >
          <Plus className="w-4 h-4" />
          绑定账号
        </motion.button>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-white/5 rounded-full h-1.5 mb-4">
        <motion.div
          className="bg-gradient-to-r from-[#00CEC9] to-[#60efff] h-1.5 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${completionPercentage}%` }}
          transition={{ duration: ANIMATION_DURATION, ease: "easeOut" }}
        />
      </div>

      {/* Connections Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
        {Object.values(connections).map((connection, index) => (
          <motion.div
            key={connection.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * ANIMATION_DELAY }}
            className={`relative bg-white/5 rounded-lg p-3 group transition-all hover:bg-white/10 cursor-pointer ${
              connection.status === "未绑定"
                ? "hover:border-[#00CEC9]/20 border border-transparent"
                : "border border-white/10"
            }`}
            onClick={() => {
              if (connection.status === "未绑定") {
                handleConnectPlatform(connection.id);
              }
            }}
          >
            {/* Platform Icon */}
            <div
              className={`w-8 h-8 rounded-lg ${
                connection.status === "未绑定" ? "bg-white/5" : "bg-[#00CEC9]/10"
              } flex items-center justify-center mb-2`}
            >
              <PlatformIcon iconType={connection.iconType} />
            </div>

            {/* Platform Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <div className="text-sm text-white font-medium truncate">
                  {connection.platform}
                </div>
                {connection.required && (
                  <div className="w-1.5 h-1.5 rounded-full bg-[#ff2e63]" />
                )}
                <StatusIcon status={connection.status} />
              </div>
              <div className={`text-xs ${STATUS_COLORS[connection.status] || "text-white/60"}`}>
                {getStatusText(connection)}
              </div>
            </div>

            {/* Status Indicator for Connected Platforms */}
            {isConnected(connection.status) && (
              <>
                <div className="absolute -top-1 -right-1">
                  <div className="w-2 h-2 rounded-full bg-[#00CEC9] shadow-lg shadow-[#00CEC9]/30" />
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDisconnectPlatform(connection.id);
                  }}
                  className="absolute top-1 right-1 w-5 h-5 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors opacity-0 group-hover:opacity-100"
                >
                  <Unlink className="w-3 h-3 text-[#ff2e63]" />
                </button>
              </>
            )}

            {/* Loading State */}
            {connectingPlatform === connection.id && (
              <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                <Loader2 className="w-6 h-6 text-[#00CEC9] animate-spin" />
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Modal */}
      <ConnectionModal
        show={showModal}
        content={modalContent}
        onClose={closeModal}
        onConfirmDisconnect={confirmDisconnect}
      />
    </div>
  );
};

export default AccountConnections;
