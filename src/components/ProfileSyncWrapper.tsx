"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { useProfileSync } from "@/hooks/useProfileSync";
import { motion, AnimatePresence } from "framer-motion";

interface ProfileSyncWrapperProps {
  children: React.ReactNode;
}

export default function ProfileSyncWrapper({
  children,
}: ProfileSyncWrapperProps) {
  const { isSignedIn, userId } = useAuth();
  const { isSyncing, syncResult, syncError } = useProfileSync();
  const [hasShownSync, setHasShownSync] = useState(false);

  // 标记为已显示同步状态，避免重复显示
  useEffect(() => {
    if (syncResult || syncError) {
      setHasShownSync(true);
    }
  }, [syncResult, syncError]);

  // 如果用户未登录，直接显示内容
  if (!isSignedIn) {
    return <>{children}</>;
  }

  // 如果正在同步且还没有显示过同步状态，显示同步界面
  if (isSyncing && !hasShownSync) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#4A148C] to-[#311B92] flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl p-8 shadow-2xl max-w-md mx-4"
        >
          <div className="text-center space-y-4">
            <div className="relative">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 mx-auto border-4 border-blue-200 border-t-blue-600 rounded-full"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="w-8 h-8 bg-blue-600 rounded-full opacity-20"
                />
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-xl font-bold text-gray-900">
                正在同步用户资料
              </h3>
              <p className="text-gray-600">
                请稍候，我们正在为您准备个性化内容...
              </p>
            </div>

            <div className="bg-blue-50 rounded-lg p-3">
              <div className="flex items-center justify-center space-x-2 text-blue-700 text-sm">
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="w-2 h-2 bg-blue-500 rounded-full"
                />
                <span>连接到安全数据库</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  // 如果同步成功，显示简短的成功消息然后显示内容
  if (syncResult && !hasShownSync) {
    return (
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="min-h-screen bg-gradient-to-br from-[#4A148C] to-[#311B92] flex items-center justify-center"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl p-8 shadow-2xl max-w-md mx-4"
          >
            <div className="text-center space-y-4">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", delay: 0.2 }}
                className="w-16 h-16 mx-auto bg-green-500 rounded-full flex items-center justify-center"
              >
                <svg
                  className="w-8 h-8 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </motion.div>

              <div className="space-y-2">
                <h3 className="text-xl font-bold text-gray-900">同步完成！</h3>
                <p className="text-gray-600">{syncResult.message}</p>
              </div>

              <motion.div
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 1, delay: 0.5 }}
                className="h-1 bg-green-200 rounded-full overflow-hidden"
              >
                <motion.div
                  initial={{ x: "-100%" }}
                  animate={{ x: "100%" }}
                  transition={{ duration: 1, delay: 0.5 }}
                  className="h-full bg-green-500"
                />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </AnimatePresence>
    );
  }

  // 如果同步失败，显示错误消息但仍然允许继续使用
  if (syncError && !hasShownSync) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#4A148C] to-[#311B92] flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-2xl p-8 shadow-2xl max-w-md mx-4"
        >
          <div className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", delay: 0.2 }}
              className="w-16 h-16 mx-auto bg-yellow-500 rounded-full flex items-center justify-center"
            >
              <svg
                className="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </motion.div>

            <div className="space-y-2">
              <h3 className="text-xl font-bold text-gray-900">同步遇到问题</h3>
              <p className="text-gray-600">
                数据同步失败，但您仍然可以正常使用应用
              </p>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setHasShownSync(true)}
              className="w-full py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              继续使用
            </motion.button>
          </div>
        </motion.div>
      </div>
    );
  }

  // 默认显示内容
  return <>{children}</>;
}
