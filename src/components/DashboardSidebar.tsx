import React, { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import {
  Youtube,
  Zap,
  ChevronRight,
  CheckCircle2,
  <PERSON>rkles,
  User,
  Wrench,
  BookOpen,
  X,
  Gamepad2,
  Settings,
  LogOut,
  Home,
  Trophy,
  Share2,
} from "lucide-react";
import { CreatorKitTask, CreatorKitCategory } from "@/types/creator-kit";
import { PixelProgress } from "@/components/ui/pixel-progress";
import { useRouter } from "next/navigation";
import { useProfile } from "@/hooks/useProfile";
import { useAuth } from "@/hooks/useAuth";

interface ActivityOverviewProps {
  completedTasks: number;
  totalRpx: number;
  nextGoal: string;
  unlockedAchievements: number;
}

interface DashboardSidebarProps {
  activityOverview: ActivityOverviewProps;
  creatorKitTasks: CreatorKitTask[];
  onTaskComplete: (taskId: string) => void;
  onTaskSelect: (taskId: string) => void;
  onAchievementUnlock: (achievement: string) => void;
}

const DashboardSidebar: React.FC<DashboardSidebarProps> = ({
  activityOverview,
  creatorKitTasks,
  onTaskComplete,
  onTaskSelect,
  onAchievementUnlock,
}) => {
  const { profile, isLoading: profileLoading } = useProfile();
  const { user, signOut } = useAuth();
  
  const [isCreatorKitModalOpen, setIsCreatorKitModalOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState<
    CreatorKitCategory | "all"
  >("all");
  const [_, setIsMobile] = useState(false);
  const router = useRouter();

  // Get user data from profile
  const userName = profile?.full_name || "Creator";
  const avatar = profile?.avatar_url || "/lovable-uploads/f04b73ef-11c1-4465-968e-07be3d440339.png";
  const joinDate = profile?.created_at 
    ? new Date(profile.created_at).toLocaleDateString("en-US", {
        month: "short",
        year: "numeric",
      })
    : new Date().toLocaleDateString("en-US", {
        month: "short", 
        year: "numeric",
      });
  const youtubeId = profile?.youtube_id;
  const subscribers = profile?.subscribers || 0;
  const views = profile?.views || 0;

  // Calculate experience data
  const currentLevel = profile?.level || 1;
  const experiencePoints = profile?.experience_points || 0;
  
  // Calculate XP progress for current level (same logic as ExperienceStatus)
  const currentLevelBaseXp = (currentLevel - 1) * 100;
  const nextLevelTotalXp = currentLevel * 100;
  const currentLevelProgress = experiencePoints - currentLevelBaseXp;
  const xpForNextLevel = 100; // Each level requires 100 XP
  const xpProgress = Math.max(0, Math.min(currentLevelProgress, xpForNextLevel));

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const categories: CreatorKitCategory[] = ["account", "tools", "guide"];
  const filteredTasks =
    activeCategory === "all"
      ? creatorKitTasks
      : creatorKitTasks.filter((task) => task.category === activeCategory);

  const completedTasks = creatorKitTasks.filter(
    (task) => task.isCompleted
  ).length;
  const totalTasks = creatorKitTasks.length;
  const progressPercentage = (completedTasks / totalTasks) * 100;

  const getCategoryIcon = (category: CreatorKitCategory) => {
    switch (category) {
      case "account":
        return <User className="w-4 h-4" />;
      case "tools":
        return <Wrench className="w-4 h-4" />;
      case "guide":
        return <BookOpen className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const handleCreatorKitToggle = () => {
    setIsCreatorKitModalOpen(!isCreatorKitModalOpen);
    if (!isCreatorKitModalOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.push("/");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const getUserDisplayName = () => {
    if (user?.name) return user.name;
    if (profile?.full_name) return profile.full_name;
    return "Creator";
  };

  const getUserEmail = () => {
    return user?.email || "用户邮箱";
  };

  return (
    <div className="flex relative">
      {/* Main Sidebar */}
      <aside className="w-full md:w-[340px] flex-shrink-0">
        <div className="sticky top-28 space-y-4">
          {/* Profile Card */}
          <Card className="relative bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748] backdrop-blur-sm border border-[#4ecdc4]/20 rounded-2xl px-6 py-5 shadow-2xl overflow-hidden group hover:shadow-[#4ecdc4]/20 transition-all duration-300">
            {/* Enhanced Background Effects */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_70%,rgba(78,205,196,0.1)_0%,transparent_50%)]" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(96,239,255,0.08)_0%,transparent_50%)]" />

            {/* Corner decorations */}
            <div className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-[#4ecdc4]/30 rounded-tl-2xl group-hover:border-[#4ecdc4]/50 transition-colors duration-300" />
            <div className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-[#60efff]/30 rounded-tr-2xl group-hover:border-[#60efff]/50 transition-colors duration-300" />
            <div className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-[#60efff]/30 rounded-bl-2xl group-hover:border-[#60efff]/50 transition-colors duration-300" />
            <div className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-[#4ecdc4]/30 rounded-br-2xl group-hover:border-[#4ecdc4]/50 transition-colors duration-300" />

            <div className="relative flex flex-col gap-5">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h2 className="text-2xl font-black text-white mb-2 group-hover:text-[#4ecdc4] transition-colors duration-300 drop-shadow-lg">
                    {userName}
                  </h2>
                  <span className="inline-block bg-[#2a2a3e]/50 text-[#4ecdc4] px-4 py-2 rounded-xl text-sm font-bold group-hover:bg-[#4ecdc4]/10 group-hover:text-[#4ecdc4] transition-all duration-300 backdrop-blur-sm border border-[#4ecdc4]/20">
                    Joined {joinDate}
                  </span>
                </div>
                <div className="relative">
                  <img
                    src={avatar}
                    alt={userName}
                    className="w-24 h-24 rounded-2xl border-4 border-[#4ecdc4]/30 object-cover group-hover:border-[#4ecdc4]/50 transition-all duration-300 shadow-lg shadow-[#4ecdc4]/20"
                  />
                  {/* Avatar glow effect */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-[#4ecdc4]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </div>

              {/* User Info Display */}
              <div className="bg-[#2a2a3e]/30 rounded-xl p-3 border border-[#2a2a3e]">
                <div className="flex flex-col gap-1">
                  <div className="text-sm font-medium text-white">
                    {getUserDisplayName()}
                  </div>
                  <div className="text-xs text-[#00ff87]/70">
                    {getUserEmail()}
                  </div>
                </div>
              </div>

              {/* User Menu Options */}
              <div className="space-y-2">
                {/* Creator Center */}
                <button
                  onClick={() => router.push("/dashboard")}
                  className="w-full bg-[#2a2a3e]/50 text-white py-2.5 rounded-lg flex items-center gap-3 hover:bg-[#2a2a3e]/70 hover:text-[#00ff87] transition-all duration-300 border border-[#2a2a3e] px-4 group/menu"
                >
                  <User className="w-4 h-4 group-hover/menu:scale-110 transition-transform duration-300" />
                  <span className="font-medium">創作者中心</span>
                </button>



                {/* Service Management */}
                <button
                  onClick={() => router.push("/services")}
                  className="w-full bg-[#2a2a3e]/50 text-white py-2.5 rounded-lg flex items-center gap-3 hover:bg-[#2a2a3e]/70 hover:text-[#00ff87] transition-all duration-300 border border-[#2a2a3e] px-4 group/menu"
                >
                  <Gamepad2 className="w-4 h-4 group-hover/menu:scale-110 transition-transform duration-300" />
                  <span className="font-medium">服務管理</span>
                </button>

                {/* Home */}
                <button
                  onClick={() => router.push("/")}
                  className="w-full bg-[#60efff]/10 text-[#60efff] py-2.5 rounded-lg flex items-center gap-3 hover:bg-[#60efff]/20 transition-all duration-300 border border-[#60efff]/20 px-4 group/menu"
                >
                  <Home className="w-4 h-4 group-hover/menu:scale-110 transition-transform duration-300" />
                  <span className="font-medium">首頁</span>
                </button>

                {/* Leaderboard */}
                <button
                  onClick={() => router.push("/leaderboard")}
                  className="w-full bg-[#ff2e63]/10 text-[#ff2e63] py-2.5 rounded-lg flex items-center gap-3 hover:bg-[#ff2e63]/20 transition-all duration-300 border border-[#ff2e63]/20 px-4 group/menu"
                >
                  <Trophy className="w-4 h-4 group-hover/menu:scale-110 transition-transform duration-300" />
                  <span className="font-medium">榜單</span>
                </button>

                {/* Share */}
                <button
                  className="w-full bg-[#00ff87]/10 text-[#00ff87] py-2.5 rounded-lg flex items-center gap-3 hover:bg-[#00ff87]/20 transition-all duration-300 border border-[#00ff87]/20 px-4 group/menu relative"
                >
                  <Share2 className="w-4 h-4 group-hover/menu:scale-110 transition-transform duration-300" />
                  <span className="font-medium">分享邀請</span>
                  {/* Notification dot */}
                  <span className="absolute -top-1 -right-1 w-2 h-2">
                    <span className="absolute inline-flex h-full w-full rounded-full bg-[#00ff87] opacity-75 animate-ping" />
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-[#00ff87]" />
                  </span>
                </button>

                {/* Settings */}
                <button
                  onClick={() => router.push("/settings")}
                  className="w-full bg-[#2a2a3e]/50 text-white py-2.5 rounded-lg flex items-center gap-3 hover:bg-[#2a2a3e]/70 hover:text-[#00ff87] transition-all duration-300 border border-[#2a2a3e] px-4 group/menu"
                >
                  <Settings className="w-4 h-4 group-hover/menu:scale-110 transition-transform duration-300" />
                  <span className="font-medium">設置</span>
                </button>

                {/* Logout */}
                <button
                  onClick={handleLogout}
                  className="w-full bg-red-500/10 text-red-400 py-2.5 rounded-lg flex items-center gap-3 hover:bg-red-500/20 hover:text-red-300 transition-all duration-300 border border-red-500/20 px-4 group/menu"
                >
                  <LogOut className="w-4 h-4 group-hover/menu:scale-110 transition-transform duration-300" />
                  <span className="font-medium">退出登錄</span>
                </button>
              </div>

              {/* XP Progress Section */}
              <div className="relative p-4 bg-[#2a2a3e]/50 rounded-xl border border-[#2a2a3e] hover:bg-[#2a2a3e]/70 transition-all duration-300 group/xp">
                {/* Corner decorations */}
                <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-[#2a2a3e] rounded-tl-xl group-hover/xp:border-[#00ff87]/30 transition-colors duration-300" />
                <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-[#2a2a3e] rounded-tr-xl group-hover/xp:border-[#00ff87]/30 transition-colors duration-300" />
                <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-[#2a2a3e] rounded-bl-xl group-hover/xp:border-[#00ff87]/30 transition-colors duration-300" />
                <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-[#2a2a3e] rounded-br-xl group-hover/xp:border-[#00ff87]/30 transition-colors duration-300" />

                {/* Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="relative group-hover/xp:animate-bounce">
                      <div className="w-8 h-8 rounded-full bg-[#2a2a3e] flex items-center justify-center group-hover/xp:bg-[#00ff87]/20 transition-colors duration-300">
                        <Zap className="w-5 h-5 text-[#00ff87]" />
                      </div>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-gray-300 font-medium group-hover/xp:text-[#00ff87] transition-colors duration-300">
                        创作者等级
                      </span>
                      <span className="text-xs text-gray-500 group-hover/xp:text-gray-400 transition-colors duration-300">
                        Lv.{currentLevel}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 bg-[#1a1a2e]/60 px-2 py-1 rounded-lg group-hover/xp:bg-[#2a2a3e] transition-colors duration-300">
                    <span className="text-[#00ff87] text-sm font-medium">
                      {xpProgress}/{xpForNextLevel} XP
                    </span>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="h-1.5 bg-[#1a1a2e] rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-[#00ff87] to-[#60efff] rounded-full transition-all duration-500"
                    style={{
                      width: `${(xpProgress / xpForNextLevel) * 100}%`,
                    }}
                  />
                </div>

                {/* Next Level Info */}
                <div className="mt-3 grid grid-cols-2 gap-2">
                  <div className="flex flex-col gap-1 p-2 bg-[#1a1a2e]/60 rounded-lg group-hover/xp:bg-[#2a2a3e] transition-colors duration-300">
                    <div className="flex items-center gap-1">
                      <div className="w-1.5 h-1.5 bg-[#00ff87] rounded-full animate-pulse" />
                      <span className="text-xs text-gray-500">距离升级</span>
                    </div>
                    <span className="text-[#00ff87] font-medium group-hover/xp:scale-110 transition-transform duration-300">
                      {xpForNextLevel - xpProgress} XP
                    </span>
                  </div>
                  <div className="flex flex-col gap-1 p-2 bg-[#1a1a2e]/60 rounded-lg group-hover/xp:bg-[#2a2a3e] transition-colors duration-300">
                    <div className="flex items-center gap-1">
                      <div className="w-1.5 h-1.5 bg-[#00ff87] rounded-full animate-pulse" />
                      <span className="text-xs text-gray-500">当前特权</span>
                    </div>
                    <span className="text-white font-medium group-hover/xp:scale-110 transition-transform duration-300">
                      {currentLevel >= 5 ? "超级创作者" : "基础任务"}
                    </span>
                  </div>
                </div>
              </div>

              {youtubeId && (
                <div className="relative flex flex-col gap-3 mt-2 p-4 bg-[#2a2a3e]/50 rounded-xl border border-[#2a2a3e] hover:bg-[#2a2a3e]/70 transition-all duration-300 group/youtube">
                  {/* Corner decorations */}
                  <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-[#2a2a3e] rounded-tl-xl group-hover/youtube:border-[#00ff87]/30 transition-colors duration-300" />
                  <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-[#2a2a3e] rounded-tr-xl group-hover/youtube:border-[#00ff87]/30 transition-colors duration-300" />
                  <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-[#2a2a3e] rounded-bl-xl group-hover/youtube:border-[#00ff87]/30 transition-colors duration-300" />
                  <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-[#2a2a3e] rounded-br-xl group-hover/youtube:border-[#00ff87]/30 transition-colors duration-300" />

                  {/* Header */}
                  <div className="flex items-center gap-2">
                    <div className="relative group-hover/youtube:animate-bounce">
                      <div className="w-8 h-8 rounded-full bg-[#2a2a3e] flex items-center justify-center group-hover/youtube:bg-[#ff0000]/20 transition-colors duration-300">
                        <Youtube className="w-5 h-5 text-[#ff0000]" />
                      </div>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-gray-300 font-medium group-hover/youtube:text-[#00ff87] transition-colors duration-300">
                        YouTube 频道
                      </span>
                      <span className="text-xs text-gray-500 group-hover/youtube:text-gray-400 transition-colors duration-300">
                        @{youtubeId}
                      </span>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex flex-col gap-1 p-2 bg-[#1a1a2e]/60 rounded-lg group-hover/youtube:bg-[#2a2a3e] transition-colors duration-300">
                      <div className="flex items-center gap-1">
                        <div className="w-1.5 h-1.5 bg-[#00ff87] rounded-full animate-pulse" />
                        <span className="text-xs text-gray-500">订阅数</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-lg font-bold text-[#00ff87] group-hover/youtube:scale-110 transition-transform duration-300">
                          {subscribers.toLocaleString()}
                        </span>
                        <span className="text-xs text-gray-500">位创作者</span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1 p-2 bg-[#1a1a2e]/60 rounded-lg group-hover/youtube:bg-[#2a2a3e] transition-colors duration-300">
                      <div className="flex items-center gap-1">
                        <div className="w-1.5 h-1.5 bg-[#00ff87] rounded-full animate-pulse" />
                        <span className="text-xs text-gray-500">观看次数</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-lg font-bold text-[#00ff87] group-hover/youtube:scale-110 transition-transform duration-300">
                          {views.toLocaleString()}
                        </span>
                        <span className="text-xs text-gray-500">次播放</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* User Menu Card */}
          <Card className="relative bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl px-6 py-4 shadow-lg overflow-hidden group hover:bg-[#1a1a2e]/90 transition-all duration-300">
            {/* Corner decorations */}
            <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#2a2a3e] rounded-tl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
            <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#2a2a3e] rounded-tr-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
            <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#2a2a3e] rounded-bl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
            <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#2a2a3e] rounded-br-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />

            <div className="space-y-3">
              <h3 className="text-lg font-bold text-[#00ff87] mb-4">快速導航</h3>
              
              {/* Quick Access Buttons */}
              <div className="space-y-2">
                {/* Home */}
                <button
                  onClick={() => router.push("/")}
                  className="w-full bg-[#60efff]/10 text-[#60efff] py-3 rounded-lg flex items-center gap-3 hover:bg-[#60efff]/20 transition-all duration-300 border border-[#60efff]/20 px-4 group/nav"
                >
                  <Home className="w-5 h-5 group-hover/nav:scale-110 transition-transform duration-300" />
                  <span className="font-medium">返回首頁</span>
                </button>

                {/* Leaderboard */}
                <button
                  onClick={() => router.push("/leaderboard")}
                  className="w-full bg-[#ff2e63]/10 text-[#ff2e63] py-3 rounded-lg flex items-center gap-3 hover:bg-[#ff2e63]/20 transition-all duration-300 border border-[#ff2e63]/20 px-4 group/nav"
                >
                  <Trophy className="w-5 h-5 group-hover/nav:scale-110 transition-transform duration-300" />
                  <span className="font-medium">創作者榜單</span>
                </button>

                {/* Share */}
                <button
                  className="w-full bg-[#00ff87]/10 text-[#00ff87] py-3 rounded-lg flex items-center gap-3 hover:bg-[#00ff87]/20 transition-all duration-300 border border-[#00ff87]/20 px-4 group/nav relative"
                >
                  <Share2 className="w-5 h-5 group-hover/nav:scale-110 transition-transform duration-300" />
                  <span className="font-medium">分享邀請碼</span>
                  {/* Notification dot */}
                  <span className="absolute -top-1 -right-1 w-2 h-2">
                    <span className="absolute inline-flex h-full w-full rounded-full bg-[#00ff87] opacity-75 animate-ping" />
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-[#00ff87]" />
                  </span>
                </button>
              </div>
            </div>
          </Card>

          {/* Creator Kit Toggle */}
          <button
            onClick={handleCreatorKitToggle}
            className="relative w-full bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-4 flex items-center justify-between hover:bg-[#1a1a2e]/90 transition-all duration-300 group"
          >
            {/* Corner decorations */}
            <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#2a2a3e] rounded-tl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
            <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#2a2a3e] rounded-tr-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
            <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#2a2a3e] rounded-bl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
            <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#2a2a3e] rounded-br-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />

            <div className="flex items-center gap-3">
              <div className="w-2 h-8 rounded-full bg-gradient-to-b from-[#00ff87] to-[#60efff] animate-pulse group-hover:scale-110 transition-transform duration-300" />
              <div>
                <h2 className="text-xl font-bold text-[#00ff87] group-hover:text-[#60efff] transition-colors duration-300">
                  创作者启动套件
                </h2>
                <p className="text-gray-500 text-sm group-hover:text-gray-400 transition-colors duration-300">
                  查看并完成创作者任务
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 bg-[#2a2a3e]/50 px-2 py-1 rounded-lg group-hover:bg-[#2a2a3e]/70 transition-colors duration-300">
                <Sparkles className="w-4 h-4 text-[#00ff87] group-hover:animate-bounce" />
                <span className="text-[#00ff87] text-sm font-medium">
                  {completedTasks}/{totalTasks}
                </span>
              </div>
              <ChevronRight className="w-5 h-5 text-[#00ff87] group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </button>
        </div>
      </aside>

      {/* Creator Kit Modal */}
      {isCreatorKitModalOpen && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 animate-fade-in"
            onClick={handleCreatorKitToggle}
          />

          {/* Modal Content */}
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            <div className="w-full max-w-4xl h-[80vh] bg-gradient-to-br from-[#1a1a2e]/95 to-[#1a1a2e]/90 backdrop-blur-sm border border-[#00ff87]/20 rounded-2xl overflow-hidden flex flex-col">
              {/* Header */}
              <div className="p-6 border-b border-[#00ff87]/20 flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-8 rounded-full bg-gradient-to-b from-[#00ff87] to-[#60efff] animate-pulse" />
                  <h2 className="text-xl font-bold text-[#00ff87]">
                    创作者启动套件
                  </h2>
                </div>
                <button
                  onClick={handleCreatorKitToggle}
                  className="text-[#00ff87] hover:text-[#60efff] transition-colors p-2 hover:bg-[#00ff87]/10 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Progress Bar */}
              <div className="p-6 border-b border-[#00ff87]/20">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Zap className="w-5 h-5 text-[#00ff87]" />
                    <span className="text-gray-300">任务进度</span>
                  </div>
                  <span className="text-[#00ff87] text-sm font-medium">
                    {completedTasks}/{totalTasks} 已完成
                  </span>
                </div>
                <PixelProgress
                  value={progressPercentage}
                  color="#00ff87"
                  size="md"
                  showValue={false}
                />
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-6">
                {/* Category Filter */}
                <div className="flex gap-2 mb-6 overflow-x-auto pb-2">
                  <button
                    onClick={() => setActiveCategory("all")}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                      activeCategory === "all"
                        ? "bg-[#00ff87] text-[#1a1a2e] shadow-lg shadow-[#00ff87]/20"
                        : "bg-[#00ff87]/10 text-[#00ff87] hover:bg-[#00ff87]/20"
                    }`}
                  >
                    全部
                  </button>
                  {categories.map((category) => (
                    <button
                      key={category}
                      onClick={() => setActiveCategory(category)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-all flex items-center gap-2 ${
                        activeCategory === category
                          ? "bg-[#00ff87] text-[#1a1a2e] shadow-lg shadow-[#00ff87]/20"
                          : "bg-[#00ff87]/10 text-[#00ff87] hover:bg-[#00ff87]/20"
                      }`}
                    >
                      {getCategoryIcon(category)}
                      {category === "account"
                        ? "账号设置"
                        : category === "tools"
                        ? "生产力工具"
                        : "创作指南"}
                    </button>
                  ))}
                </div>

                {/* Tasks List */}
                <div className="space-y-4">
                  {filteredTasks.map((task) => (
                    <div
                      key={task.id}
                      className="relative bg-gradient-to-br from-[#1a1a2e]/80 to-[#1a1a2e]/60 backdrop-blur-sm border border-[#00ff87]/20 rounded-xl p-4 hover:border-[#00ff87]/40 transition-all cursor-pointer group"
                      onClick={() => onTaskSelect(task.id)}
                    >
                      {/* Corner decorations */}
                      <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-[#00ff87]/30 rounded-tl-xl" />
                      <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-[#00ff87]/30 rounded-tr-xl" />
                      <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-[#00ff87]/30 rounded-bl-xl" />
                      <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-[#00ff87]/30 rounded-br-xl" />

                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-[#00ff87] font-medium group-hover:text-[#60efff] transition-colors">
                          {task.title}
                        </h3>
                        {task.isCompleted && (
                          <CheckCircle2 className="w-5 h-5 text-[#00ff87] animate-bounce" />
                        )}
                      </div>
                      <p className="text-gray-400 text-sm mb-3 group-hover:text-gray-300 transition-colors">
                        {task.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <PixelProgress
                            value={(task.progress / task.totalSteps) * 100}
                            color="#00ff87"
                            size="sm"
                            showValue={false}
                          />
                          <span className="text-[#00ff87] text-sm font-medium">
                            {task.progress}/{task.totalSteps}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 bg-[#00ff87]/10 px-2 py-1 rounded-lg group-hover:bg-[#00ff87]/20 transition-colors">
                          <Sparkles className="w-4 h-4 text-[#00ff87]" />
                          <span className="text-[#00ff87] text-sm font-medium">
                            {task.reward} RPx
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default DashboardSidebar;
