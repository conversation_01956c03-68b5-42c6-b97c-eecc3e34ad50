import React from "react";

interface HexLinearProgressProps {
  progress: number; // 0-100
  width?: number;
  height?: number;
  title?: string; // 称号
  level?: number; // 等级
}

const HexLinearProgress: React.FC<HexLinearProgressProps> = ({
  progress,
  width = 800,
  height = 64,
  title = "新手创作者", // 默认称号
  level = 1, // 默认等级
}) => {
  const hexRadius = 8; // 保持小尺寸
  const rows = 3; // 改回三行
  // 根据容器宽度计算每行可容纳的六边形数量
  const hexWidth = hexRadius * Math.sqrt(3); // 尖角朝上六边形的宽度
  const hexHeight = hexRadius * 2; // 尖角朝上六边形的高度
  const colSpacing = hexWidth; // 列间距
  const rowSpacing = hexHeight * 0.75; // 行间距（蜂窝标准）
  
  // 计算每行可以容纳的六边形数量
  const colsPerRow = Math.floor(width / colSpacing); // 使用完整宽度
  
  // 创建真正的蜂窝网格
  const hexagons: Array<{ x: number; y: number; id: number; row: number; col: number }> = [];
  let hexId = 0;

  for (let row = 0; row < rows; row++) {
    const isOffsetRow = row % 2 === 1; 
    const hexesInRow = isOffsetRow ? colsPerRow - 1 : colsPerRow; // 奇数行少一个以保持对齐
    const offsetX = isOffsetRow ? colSpacing * 0.5 : 0;
    const startX = offsetX; // 从0开始
    
    for (let col = 0; col < hexesInRow; col++) {
      const x = startX + col * colSpacing;
      const y = height/2 - rowSpacing + row * rowSpacing; // 调整垂直位置以适应三行
      hexagons.push({ x, y, id: hexId, row, col });
      hexId++;
    }
  }

  // 计算每行的填充状态
  const getHexStatus = (hex: { row: number; col: number }) => {
    const hexesInThisRow = hex.row % 2 === 1 ? colsPerRow - 1 : colsPerRow;
    const progressInRow = (progress / 100) * hexesInThisRow; 
    const filledInRow = Math.floor(progressInRow);
    const partialInRow = progressInRow % 1;
    
    if (hex.col < filledInRow) {
      return { status: 'filled', opacity: 1 };
    } else if (hex.col === filledInRow && partialInRow > 0) {
      return { status: 'partial', opacity: partialInRow };
    } else if (hex.col <= filledInRow + 1) {
      return { status: 'nearFront', opacity: 0.6 };
    } else {
      return { status: 'empty', opacity: 0.3 };
    }
  };

  // 创建尖角朝上的六边形路径（真正的蜂窝形状）
  const createHexPath = (x: number, y: number) => {
    const centerX = x;
    const centerY = y;
    
    // 尖角朝上的六边形坐标
    const points: string[] = [];
    for (let i = 0; i < 6; i++) {
      const angle = (Math.PI / 180) * (60 * i - 90); // 从顶部开始，尖角朝上
      const px = centerX + hexRadius * Math.cos(angle);
      const py = centerY + hexRadius * Math.sin(angle);
      points.push(`${px},${py}`);
    }
    
    return `M ${points[0]} L ${points[1]} L ${points[2]} L ${points[3]} L ${points[4]} L ${points[5]} Z`;
  };

  // 计算总的已填充六边形数量
  const totalFilled = hexagons.reduce((count, hex) => {
    const status = getHexStatus(hex);
    return count + (status.status === 'filled' ? 1 : status.status === 'partial' ? status.opacity : 0);
  }, 0);

  // 计算六边形区域的中心
  const hexAreaCenterY = height / 2;

  return (
    <div className="flex flex-col items-center">
      <svg width={width} height={height} className="overflow-visible">
        {/* 蜂窝网格 */}
        {hexagons.map((hex, index) => {
          const hexStatus = getHexStatus(hex);
          const delay = (hex.row * 0.05) + (hex.col * 0.01); 

          let fillColor = "none";
          let strokeColor = "#475569";
          let opacity = hexStatus.opacity;
          let strokeWidth = 0.8;

          if (hexStatus.status === 'filled') {
            fillColor = "rgba(0, 255, 135, 0.3)";
            strokeColor = "#00ff87";
            strokeWidth = 2;
          } else if (hexStatus.status === 'partial') {
            fillColor = "rgba(96, 239, 255, 0.3)";
            strokeColor = "#60efff";
            strokeWidth = 1.5;
          } else if (hexStatus.status === 'nearFront') {
            strokeColor = "#60efff";
            strokeWidth = 1;
          }

          return (
            <g key={index}>
              <path
                d={createHexPath(hex.x, hex.y)}
                fill={fillColor}
                stroke={strokeColor}
                strokeWidth={strokeWidth}
                opacity={opacity}
                className="transition-all duration-400 ease-out"
                style={{
                  transitionDelay: `${delay}s`,
                  filter: (hexStatus.status === 'filled' || hexStatus.status === 'partial') ? "drop-shadow(0 0 3px rgba(0, 255, 135, 0.5))" : "none",
                  transform: (hexStatus.status === 'filled' || hexStatus.status === 'partial') ? "scale(1.03)" : "scale(1)",
                  transformOrigin: `${hex.x}px ${hex.y}px`
                }}
              />

              {(hexStatus.status === 'filled' || hexStatus.status === 'partial') && (
                <path
                  d={createHexPath(hex.x, hex.y)}
                  fill="none"
                  stroke={strokeColor}
                  strokeWidth="1"
                  opacity="0.6"
                  className="animate-pulse"
                  style={{
                    filter: "blur(1px)",
                    transform: "scale(1.15)",
                    transformOrigin: `${hex.x}px ${hex.y}px`
                  }}
                />
              )}
            </g>
          );
        })}

        {/* 左侧称号 */}
        <text
          x="40"
          y={hexAreaCenterY}
          className="fill-[#cbd5e1] text-base font-medium"
          textAnchor="start"
          dominantBaseline="middle"
          style={{
            filter: "drop-shadow(0 0 3px rgba(0, 0, 0, 1))",
            paintOrder: "stroke",
            stroke: "rgba(0, 0, 0, 0.8)",
            strokeWidth: "2px"
          }}
        >
          {title}
        </text>

        {/* 右侧进度和等级 */}
        <g transform={`translate(${width - 100}, ${hexAreaCenterY})`}>
          {/* 背景 */}
          <rect
            x="-40"
            y="-12"
            width="90"
            height="24"
            rx="4"
            fill="rgba(26, 27, 30, 0.8)"
            filter="blur(2px)"
          />
          
          {/* 进度百分比 */}
          <text
            x="-15"
            y="0"
            className="fill-[#00ff87] text-base font-mono font-bold"
            textAnchor="middle"
            dominantBaseline="middle"
            style={{
              filter: "drop-shadow(0 0 3px rgba(0, 255, 135, 1))",
              paintOrder: "stroke",
              stroke: "rgba(0, 0, 0, 0.8)",
              strokeWidth: "2px"
            }}
          >
            {progress.toFixed(0)}%
          </text>

          {/* 等级 */}
          <text
            x="30"
            y="0"
            className="fill-[#60efff] text-sm font-medium"
            textAnchor="start"
            dominantBaseline="middle"
            style={{
              filter: "drop-shadow(0 0 3px rgba(96, 239, 255, 1))",
              paintOrder: "stroke",
              stroke: "rgba(0, 0, 0, 0.8)",
              strokeWidth: "2px"
            }}
          >
            Lv.{level}
          </text>
        </g>
      </svg>
    </div>
  );
};

export default HexLinearProgress; 