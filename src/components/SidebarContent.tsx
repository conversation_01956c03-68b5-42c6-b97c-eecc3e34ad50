"use client";

import React from "react";
import { Youtube, Plus, Settings } from "lucide-react";
import { Card } from "@/components/ui/card";
import { useProfile } from "@/hooks/useProfile";
import EnhancedProgressBar from "@/components/ui/EnhancedProgressBar";

interface SidebarContentProps {
  className?: string;
}

const SidebarContent: React.FC<SidebarContentProps> = ({ className = "" }) => {
  const { profile, isLoading: profileLoading } = useProfile();

  // Get user data from profile (using real backend data)
  const youtubeId = profile?.youtube_id;
  const walletAddress = profile?.wallet_address;

  // YouTube analytics data (from backend or mock)
  const youtubeStats = {
    views: profile?.views || 0,
    likes: Math.floor((profile?.views || 0) * 0.05), // Mock: ~5% like rate
    comments: Math.floor((profile?.views || 0) * 0.02), // Mock: ~2% comment rate
  };

  // Connection status
  const connections = {
    youtube: !!youtubeId,
    wallet: !!walletAddress,
  };

  // Productivity tools data
  const productivityTools = [
    { name: "OBS Studio", icon: "📽️", url: "#" },
    { name: "Canva", icon: "🎨", url: "#" },
    { name: "Pinterest", icon: "📌", url: "#" },
    { name: "ChatGPT", icon: "🧠", url: "#" },
    { name: "Notion", icon: "📒", url: "#" },
    { name: "剪映 CapCut", icon: "✂️", url: "#" },
    { name: "Google Workspace", icon: "☁️", url: "#" },
  ];

  // Goals/Plans data with auto status calculation (mix of real and mock data)
  const goalsData = [
    {
      label: "訂閱者",
      current: profile?.subscribers || 0, // Real data from backend
      total: 1000, // Mock target
    },
    {
      label: "觀看次數",
      current: profile?.views || 0, // Real data from backend
      total: 10000, // Mock target
    },
    {
      label: "經驗值",
      current: profile?.experience_points || 0, // Real data from backend
      total: 500, // Mock target for next milestone
    },
    {
      label: "個人資料完成度",
      current: profile?.profile_completion_percentage || 0, // Real data from backend
      total: 100,
    },
    {
      label: "影片發布", // Mock goal since we don't have video count
      current: youtubeId ? 3 : 0, // Mock: if has YouTube, assume some videos
      total: 10,
    },
  ];

  // Calculate goals with auto status
  const goals = goalsData.map(goal => {
    const percentage = (goal.current / goal.total) * 100;
    let status: "completed" | "in_progress" | "not_started";

    if (percentage >= 100) {
      status = "completed";
    } else if (percentage > 0) {
      status = "in_progress";
    } else {
      status = "not_started";
    }

    return {
      ...goal,
      percentage,
      status,
    };
  });

  if (profileLoading) {
    return (
      <div className={`${className}`}>
        <div className="animate-pulse flex flex-col gap-4">
          <div className="h-8 bg-[#2a2a3e] rounded-md w-3/4"></div>
          <div className="h-6 bg-[#2a2a3e] rounded-md w-1/2"></div>
          <div className="h-20 bg-[#2a2a3e] rounded-md w-full"></div>
          <div className="h-10 bg-[#2a2a3e] rounded-md w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>


      {/* Section 3: Productivity Tools - Gaming Style */}
      <Card className="relative bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl px-6 py-4 shadow-lg overflow-hidden group hover:border-[#4ecdc4]/40 transition-all duration-300">
        {/* Gaming style header decoration */}
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#4ecdc4]/30 to-transparent" />

        <div className="relative space-y-4">
          {/* Header - Improved layout */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-white flex items-center gap-3">
                <span className="text-xl">🎮</span>
                基礎知識
              </h3>
              <div className="text-xs bg-[#4ecdc4]/20 text-[#4ecdc4] px-3 py-1 rounded-full border border-[#4ecdc4]/30 font-medium">
                {productivityTools.length} 個工具
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-[#4ecdc4] rounded-full animate-pulse" />
              <h4 className="text-sm font-medium text-[#4ecdc4]">🔧 生產力工具 - 從這裡開始！</h4>
            </div>
          </div>

          {/* Tools Grid - Gaming Style (Fixed hover) */}
          <div className="grid grid-cols-3 gap-3">
            {productivityTools.map((tool, index) => (
              <button
                key={index}
                className="relative bg-[#243447]/80 rounded-lg p-3 border border-[#475569] hover:bg-[#2d4059]/90 hover:border-[#4ecdc4]/60 transition-all duration-300 text-center group overflow-hidden"
                onClick={() => window.open(tool.url, '_blank')}
              >
                {/* Gaming style corner indicators */}
                <div className="absolute top-1 left-1 w-2 h-2 border-t border-l border-[#4ecdc4]/50 rounded-tl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute top-1 right-1 w-2 h-2 border-t border-r border-[#4ecdc4]/50 rounded-tr opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Tool content - Fixed z-index */}
                <div className="relative z-10">
                  <div className="text-lg mb-1 group-hover:scale-110 transition-transform duration-300">{tool.icon}</div>
                  <div className="text-xs text-[#cbd5e1] group-hover:text-white transition-colors duration-300 font-medium">{tool.name}</div>
                </div>

                {/* Gaming style hover effect - Lower z-index */}
                <div className="absolute inset-0 bg-gradient-to-r from-[#4ecdc4]/10 to-[#f39c12]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0" />
              </button>
            ))}
          </div>

          {/* Action Buttons - Gaming Style */}
          <div className="flex gap-2">
            <button className="flex-1 bg-[#4ecdc4]/15 text-[#4ecdc4] py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-[#4ecdc4]/25 transition-all duration-300 border border-[#4ecdc4]/30 font-medium shadow-lg hover:shadow-[#4ecdc4]/20">
              <Plus className="w-4 h-4" />
              添加工具
            </button>
            <button className="flex-1 bg-[#f39c12]/15 text-[#f39c12] py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-[#f39c12]/25 transition-all duration-300 border border-[#f39c12]/30 font-medium shadow-lg hover:shadow-[#f39c12]/20">
              <Settings className="w-4 h-4" />
              自訂設定
            </button>
          </div>
        </div>
      </Card>

      {/* Section 4: Goals/Plans - Gaming Style */}
      <Card className="relative bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl px-6 py-4 shadow-lg overflow-hidden group hover:border-[#4ecdc4]/40 transition-all duration-300">
        {/* Gaming style top accent */}
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4ecdc4]/30 via-[#f39c12]/30 to-[#8e44ad]/30" />

        <div className="relative space-y-4">
          {/* Header - Improved layout */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-white flex items-center gap-3">
                <span className="text-xl">🎯</span>
                任務計劃
              </h3>
              <div className="text-xs bg-[#4ecdc4]/20 text-[#4ecdc4] px-3 py-1 rounded-full border border-[#4ecdc4]/30 font-medium">
                {goals.length} 個任務
              </div>
            </div>

            {/* Status legend - Compact layout */}
            <div className="flex items-center gap-1 text-xs">
              <span className="text-[#94a3b8] mr-2">狀態：</span>
              <div className="flex items-center gap-1 text-[#4ecdc4] bg-[#4ecdc4]/10 px-2 py-1 rounded border border-[#4ecdc4]/20">
                <span>✅</span>已完成
              </div>
              <div className="flex items-center gap-1 text-[#f39c12] bg-[#f39c12]/10 px-2 py-1 rounded border border-[#f39c12]/20">
                <span>⏳</span>進行中
              </div>
              <div className="flex items-center gap-1 text-[#e74c3c] bg-[#e74c3c]/10 px-2 py-1 rounded border border-[#e74c3c]/20">
                <span>❌</span>未開始
              </div>
            </div>
          </div>

          {/* Goals with Progress Bars */}
          <div className="space-y-3">
            {goals.map((goal, index) => (
              <EnhancedProgressBar
                key={index}
                current={goal.current}
                total={goal.total}
                percentage={goal.percentage}
                label={goal.label}
                status={goal.status}
                variant="goal"
                size="sm"
                showStatus={true}
                animated={true}
              />
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SidebarContent;
