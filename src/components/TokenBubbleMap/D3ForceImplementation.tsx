import React, {
  useState,
  useRef,
  useMemo,
  useEffect,
  useCallback,
  memo,
  useLayoutEffect,
} from "react";
import { motion, PanInfo, useAnimationControls } from "framer-motion";
import * as d3 from "d3";

// ======================================================
// Types
// ======================================================

/**
 * 代币池数据结构
 */
export interface TokenPoolData {
  totalAssets: number;
  change24h: number;
  tokens: TokenData[];
  recentTransactions: Transaction[];
  taskPool: TaskPool;
}

/**
 * 代币数据
 */
interface TokenData {
  symbol: string;
  amount: number;
  usdValue: number;
  percentage: number;
  change24h?: number;
}

/**
 * D3节点数据
 */
interface D3Node extends d3.SimulationNodeDatum {
  symbol: string;
  usdValue: number;
  size: number;
  index: number;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
  fx?: number | null;
  fy?: number | null;
}

/**
 * 交易数据
 */
interface Transaction {
  type: "in" | "out";
  amount: number;
  symbol: string;
  timestamp: string;
  status: "success" | "pending" | "failed";
}

/**
 * 任务池数据
 */
interface TaskPool {
  total: number;
  used: number;
  byType: {
    type: string;
    amount: number;
    percentage: number;
  }[];
}

/**
 * 碰撞事件
 */
interface CollisionEvent {
  id: string;
  symbol1: string;
  symbol2: string;
  timestamp: number;
  position: { x: number; y: number };
}

/**
 * 组件属性
 */
interface TokenBubbleMapProps {
  data: TokenPoolData;
  maxBubbles?: number;
  minBubbleSize?: number;
  maxBubbleSize?: number;
  containerHeight?: number;
  onBubbleClick?: (token: TokenData) => void;
}

// ======================================================
// Constants
// ======================================================

/**
 * 泡泡渐变颜色配置
 */
const BUBBLE_GRADIENTS = [
  ["#00ff87", "#60efff"],
  ["#7C3AED", "#3B82F6"],
  ["#F59E0B", "#EF4444"],
  ["#EC4899", "#8B5CF6"],
  ["#10B981", "#3B82F6"],
];

// 为了提高性能的常量配置
const SIMULATION_CONFIG = {
  // ====== 基础物理模拟参数 ======

  /**
   * 初始alpha值 - 控制模拟开始时的活跃度
   * 较大的值会导致初始运动更加剧烈，较小的值则更平缓
   * 推荐范围: 0.1-0.5，较低的值(0.1-0.2)使运动更平滑
   */
  ALPHA: 0.3,

  /**
   * alpha衰减率 - 控制模拟的冷却速度
   * 较大的值会使模拟快速稳定，较小的值则使过渡更加持久
   * 推荐范围: 0.005-0.03，较低的值使运动更持久
   */
  ALPHA_DECAY: 0.015,

  /**
   * 最小alpha值 - 当alpha值低于此值时，模拟将停止
   * 影响模拟的最终精度和稳定判断标准
   * 推荐值: 0.001-0.01
   */
  ALPHA_MIN: 0.001,

  /**
   * 速度衰减 - 类似于空气阻力，影响泡泡运动的减速程度
   * 值越大，泡泡越快停下，运动越平滑；值越小，泡泡惯性越大
   * 推荐范围: 0.3-0.8，较高的值(>0.6)使运动更平滑
   */
  VELOCITY_DECAY: 0.7,

  /**
   * 中心引力强度 - 将泡泡吸引到画布中心的力量
   * 较大的值会使泡泡更紧密地围绕中心，较小的值允许更自由分布
   * 推荐范围: 0.01-0.2，较低的值使分布更自然
   */
  CENTER_STRENGTH: 0.08,

  /**
   * 碰撞强度 - 控制泡泡碰撞反弹的强度
   * 较大的值使碰撞更加精确但可能造成抖动，较小的值允许轻微重叠但更平滑
   * 推荐范围: 0.2-1.0，中等值(~0.5)在精确性和平滑性间取得平衡
   */
  COLLISION_STRENGTH: 0.7,

  /**
   * 碰撞迭代次数 - 每一帧中计算碰撞的精度
   * 较高的值提高碰撞精度但消耗更多性能，较低的值则相反
   * 推荐范围: 1-4，通常2-3次迭代足够
   */
  COLLISION_ITERATIONS: 3,

  /**
   * 电荷强度 - D3内置的互斥力基础强度
   * 负值为排斥力，正值为吸引力，绝对值越大力越强
   * 推荐范围: -50到-5，较小的负值(-15到-10)使排斥更温和
   */
  CHARGE_STRENGTH: -15,

  /**
   * 边界力强度 - 将泡泡约束在容器内的力量
   * 较大的值使泡泡更难接近边界，较小的值则相反
   * 推荐范围: 0.05-0.2，中等值在约束与自由间取得平衡
   */
  BOUNDS_STRENGTH: 0.06,

  /**
   * 边界内边距 - 防止泡泡紧贴容器边缘的空间
   * 单位为像素，较大的值在边缘留出更多空间
   * 推荐范围: 1-10像素
   */
  PADDING: 3,

  // ====== 自定义互斥力参数 ======

  /**
   * 互斥力基础强度 - 控制泡泡间相互排斥的力量
   * 负值为排斥力，绝对值越大力越强
   * 推荐范围: -150到-20，较小的值(-40到-30)使排斥更平和
   */
  REPULSION_STRENGTH: -45,

  /**
   * Barnes-Hut算法精度 - 控制互斥力计算的近似程度
   * 值越接近1计算越快但精度越低，值越接近0越精确但越慢
   * 推荐范围: 0.5-0.95，0.9为常用值平衡性能与精度
   */
  REPULSION_THETA: 0.85,

  /**
   * 最小互斥距离 - 互斥力开始衰减的最小距离
   * 在此距离内互斥力最强，单位为像素
   * 推荐范围: 5-20像素
   */
  REPULSION_DISTANCE_MIN: 15,

  /**
   * 最大互斥距离 - 互斥力影响的最大范围
   * 超过此距离的泡泡不会相互排斥，单位为像素
   * 推荐范围: 100-300像素，较小的值提高性能
   */
  REPULSION_DISTANCE_MAX: 120,

  /**
   * 大小影响因子 - 泡泡大小对互斥力的影响程度
   * 值越大，大泡泡的排斥力相对越强
   * 推荐范围: 0.1-1.0，中等值使大小差异的影响更平衡
   */
  REPULSION_SIZE_FACTOR: 0.3,
};

// ======================================================
// Utility Functions
// ======================================================

/**
 * 生成随机浮动动画
 */
const generateFloatAnimation = () => {
  const amplitude = Math.random() * 3 + 2;
  return {
    y: [0, amplitude, -amplitude, 0],
    transition: {
      y: {
        repeat: Infinity,
        duration: 4 + Math.random() * 2,
        ease: "easeInOut",
        repeatType: "mirror",
      },
    },
  };
};

/**
 * 计算泡泡大小
 */
const calculateBubbleSize = (
  value: number,
  totalValue: number,
  containerWidth: number,
  minSize = 60,
  maxSize?: number
): number => {
  const calculatedMaxSize = maxSize || Math.min(140, containerWidth * 0.25);
  return Math.max(
    minSize,
    Math.min(calculatedMaxSize, (value / totalValue) * 600)
  );
};

// ======================================================
// TokenBubble Component
// ======================================================

interface TokenBubbleProps {
  token: TokenData;
  node: D3Node;
  totalValue: number;
  isSelected: boolean;
  containerSize: { width: number; height: number };
  onSelect: (symbol: string) => void;
  simulation: d3.Simulation<D3Node, undefined>;
  minBubbleSize?: number;
  maxBubbleSize?: number;
}

// 针对节流函数的类型优化
const throttle = <T extends (...args: Array<unknown>) => unknown>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= wait) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * 单个泡泡组件 - 优化使用memo和纯组件
 */
const TokenBubble = memo<TokenBubbleProps>(
  ({
    token,
    node,
    totalValue,
    isSelected,
    containerSize,
    onSelect,
    simulation,
    minBubbleSize,
    maxBubbleSize,
  }) => {
    // Animation controls
    const controls = useAnimationControls();
    const bubbleRef = useRef<HTMLDivElement>(null);
    const [collision, setCollision] = useState<string | null>(null);
    const [lastVelocity, setLastVelocity] = useState({ vx: 0, vy: 0 });
    const isDraggingRef = useRef(false);
    const timeoutRef = useRef<number | null>(null);

    // Memoize floating animation
    const floatAnimation = useMemo(() => generateFloatAnimation(), []);

    // Calculate bubble size
    const size = useMemo(
      () =>
        calculateBubbleSize(
          token.usdValue,
          totalValue,
          containerSize.width,
          minBubbleSize,
          maxBubbleSize
        ),
      [
        token.usdValue,
        totalValue,
        containerSize.width,
        minBubbleSize,
        maxBubbleSize,
      ]
    );

    // 使用useRef缓存上一次的position以避免不必要的重新计算
    const lastPositionRef = useRef({ x: 0, y: 0 });

    // 只在x或y有明显变化时重新计算安全位置
    const safePosition = useMemo(() => {
      const nodeX = node.x || 0;
      const nodeY = node.y || 0;

      // 检查位置是否有足够变化来触发重新计算
      if (
        Math.abs(nodeX - lastPositionRef.current.x) < 0.5 &&
        Math.abs(nodeY - lastPositionRef.current.y) < 0.5
      ) {
        return lastPositionRef.current;
      }

      const maxX = containerSize.width / 2 - size / 2;
      const maxY = containerSize.height / 2 - size / 2;

      const newPosition = {
        x: Math.max(-maxX, Math.min(maxX, nodeX)),
        y: Math.max(-maxY, Math.min(maxY, nodeY)),
      };

      // 更新最后位置ref
      lastPositionRef.current = newPosition;
      return newPosition;
    }, [node.x, node.y, containerSize.width, containerSize.height, size]);

    // 减少useEffect依赖项，优化性能
    const handleVelocityChange = useCallback(() => {
      if (!node.vx || !node.vy || isDraggingRef.current) return;

      const vx = node.vx;
      const vy = node.vy;
      const velocityMagnitude = Math.sqrt(vx * vx + vy * vy);
      const lastMagnitude = Math.sqrt(
        lastVelocity.vx * lastVelocity.vx + lastVelocity.vy * lastVelocity.vy
      );

      // 只有在速度明显变化时才应用动画效果
      if (velocityMagnitude > 1 && velocityMagnitude > lastMagnitude * 1.5) {
        controls.start({
          scale: [1, 1.2, 0.9, 1],
          transition: { duration: 0.3 },
        });
        setCollision(token.symbol);
        setTimeout(() => setCollision(null), 300);
      }

      setLastVelocity({ vx, vy });
    }, [controls, token.symbol]);

    // 主动监听速度变化而不是被动依赖
    useEffect(() => {
      // 节流处理速度变化检测，每100ms最多执行一次
      const throttledVelocityCheck = throttle(() => {
        handleVelocityChange();
      }, 100);

      // 设置间隔检查而不是依赖重渲染
      const intervalId = setInterval(throttledVelocityCheck, 100);

      return () => {
        clearInterval(intervalId);
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, [handleVelocityChange]);

    // Apply floating animation on mount
    useEffect(() => {
      controls.start(floatAnimation);

      // Cleanup timeouts on unmount
      return () => {
        if (timeoutRef.current) {
          window.clearTimeout(timeoutRef.current);
        }
      };
    }, [controls, floatAnimation]);

    // Handle drag
    const handleDrag = useCallback(
      (_: MouseEvent | TouchEvent | PointerEvent) => {
        isDraggingRef.current = true;

        // Clear any existing release timeout
        if (timeoutRef.current) {
          window.clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        // Apply boundary constraints
        const constrainedPos = safePosition;

        // Update the node's fixed position (this will be used by d3-force)
        node.fx = constrainedPos.x;
        node.fy = constrainedPos.y;

        // Restart the simulation with a gentle alpha
        simulation.alpha(0.2).restart();
      },
      [node, simulation, safePosition]
    );

    // Handle drag end
    const handleDragEnd = useCallback(() => {
      if (!simulation) return;

      // 更新节点位置
      node.fx = null;
      node.fy = null;

      // 使用较高的alpha值重启模拟，使释放后的运动更有活力
      simulation.alpha(0.25);
      simulation.restart();

      // 使用setTimeout确保在下一帧执行
      setTimeout(() => {
        if (simulation) {
          // 再次调整alpha值，使运动更持久
          simulation.alpha(0.2);
          simulation.restart();
        }
      }, 50);
    }, [node, simulation]);

    // Handle click with a check to prevent unwanted selections after drag
    const handleClick = useCallback(() => {
      // Only process click if not dragging
      if (!isDraggingRef.current) {
        onSelect(isSelected ? "" : token.symbol);
      }
    }, [onSelect, isSelected, token.symbol]);

    // Select gradient color
    const gradient = useMemo(() => {
      const safeIndex = Math.abs(node.index) % BUBBLE_GRADIENTS.length;
      return BUBBLE_GRADIENTS[safeIndex];
    }, [node.index]);

    // 判断是否需要渲染 - 如果在可视区域外可以跳过渲染
    const isInViewport = useMemo(() => {
      const maxX = containerSize.width / 2 + size;
      const maxY = containerSize.height / 2 + size;
      const x = safePosition.x;
      const y = safePosition.y;

      // 添加额外边距确保部分可见的泡泡也被渲染
      return x > -maxX && x < maxX && y > -maxY && y < maxY;
    }, [safePosition.x, safePosition.y, containerSize, size]);

    // 如果不在视口内，返回null跳过渲染
    if (!isInViewport) return null;

    return (
      <motion.div
        ref={bubbleRef}
        initial={{ opacity: 1 }}
        animate={controls}
        style={{
          width: size,
          height: size,
          position: "absolute",
          left: `calc(50% + ${safePosition.x}px)`,
          top: `calc(50% + ${safePosition.y}px)`,
          transform: "translate(-50%, -50%)",
          zIndex: isSelected ? 50 : 0,
          willChange: "transform, opacity", // 提示浏览器这些属性会变化
          touchAction: "none",
        }}
        drag
        dragMomentum={false}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        whileDrag={{ scale: 1.05 }}
        whileHover={{
          scale: 1.1,
          transition: { duration: 0.3 },
        }}
        whileTap={{ scale: 0.95 }}
        onDragStart={() => {
          isDraggingRef.current = true;
          onSelect(token.symbol);
        }}
        onClick={handleClick}
        className="group cursor-pointer"
      >
        {/* 泡泡背景 */}
        <div
          className="absolute inset-0 rounded-full"
          style={{
            background: `radial-gradient(circle at 30% 30%, ${gradient[0]}20, ${gradient[1]}40)`,
            boxShadow: `0 0 15px ${gradient[0]}15`,
          }}
        />

        {/* 碰撞波纹效果 */}
        {collision && (
          <motion.div
            initial={{ scale: 0.8, opacity: 0.3 }}
            animate={{
              scale: [0.8, 1.2, 1],
              opacity: [0.3, 0.7, 0],
            }}
            transition={{ duration: 0.6 }}
            className="absolute inset-0 rounded-full"
            style={{
              background: `radial-gradient(circle at 50% 50%, ${gradient[0]}70, transparent 80%)`,
            }}
          />
        )}

        {/* 选中效果 */}
        {isSelected && (
          <motion.div
            initial={{ scale: 0.8, opacity: 0.3 }}
            animate={{ scale: 1.1, opacity: 0.4 }}
            transition={{ duration: 0.3 }}
            className="absolute inset-0 rounded-full"
            style={{
              background: `radial-gradient(circle at 50% 50%, ${gradient[0]}25, transparent 70%)`,
            }}
          />
        )}

        {/* 泡泡内容 */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="text-center p-2">
            <div className="font-bold text-xl tracking-wider mb-2 text-white/90">
              {token.symbol}
            </div>
            <div className="text-sm text-white/70">
              ${(token.usdValue / 1000).toFixed(1)}K
            </div>
          </div>
        </div>

        {/* 边框效果 */}
        <div className="absolute inset-0 rounded-full border border-white/10" />

        {/* 光晕效果 */}
        <div
          className="absolute inset-0 rounded-full opacity-20"
          style={{
            background: `radial-gradient(circle at 50% 50%, ${gradient[0]}15, transparent 70%)`,
          }}
        />
      </motion.div>
    );
  },
  // 添加自定义比较函数，仅在关键属性变化时重新渲染
  (prevProps, nextProps) => {
    // 检查关键属性的变化
    return (
      prevProps.isSelected === nextProps.isSelected &&
      prevProps.token.symbol === nextProps.token.symbol &&
      prevProps.token.usdValue === nextProps.token.usdValue &&
      prevProps.containerSize.width === nextProps.containerSize.width &&
      prevProps.containerSize.height === nextProps.containerSize.height &&
      // 只有当位置变化超过阈值时才重新渲染
      (!prevProps.node.x ||
        !nextProps.node.x ||
        !prevProps.node.y ||
        !nextProps.node.y ||
        (Math.abs((prevProps.node.x || 0) - (nextProps.node.x || 0)) < 0.5 &&
          Math.abs((prevProps.node.y || 0) - (nextProps.node.y || 0)) < 0.5))
    );
  }
);

TokenBubble.displayName = "TokenBubble";

// ======================================================
// CollisionEffect Component
// ======================================================

interface CollisionEffectProps {
  event: CollisionEvent;
  onComplete: () => void;
}

/**
 * 碰撞特效组件
 */
const CollisionEffect = memo<CollisionEffectProps>(({ event, onComplete }) => {
  return (
    <motion.div
      key={`collision-${event.id}`}
      className="absolute w-2 h-2 rounded-full bg-white"
      style={{
        left: `calc(50% + ${event.position.x}px)`,
        top: `calc(50% + ${event.position.y}px)`,
        transform: "translate(-50%, -50%)",
      }}
      initial={{ scale: 0, opacity: 0.8 }}
      animate={{
        scale: [0, 3, 5],
        opacity: [0.8, 0.4, 0],
      }}
      transition={{ duration: 0.6 }}
      onAnimationComplete={onComplete}
    />
  );
});

CollisionEffect.displayName = "CollisionEffect";

// ======================================================
// TokenBubbleMap Component with D3-force
// ======================================================

/**
 * 主组件 - 性能优化版本
 */
const TokenBubbleMapD3: React.FC<TokenBubbleMapProps> = ({
  data,
  maxBubbles = 10,
  minBubbleSize = 60,
  maxBubbleSize,
  containerHeight = 400,
  onBubbleClick,
}) => {
  // State
  const [selectedToken, setSelectedToken] = useState<string>("");
  const [containerSize, setContainerSize] = useState({
    width: 400,
    height: containerHeight,
  });
  const [collisionEvents, setCollisionEvents] = useState<CollisionEvent[]>([]);

  // 使用useRef而不是state来存储nodes，避免不必要的重渲染
  const [nodes, setNodes] = useState<D3Node[]>([]);
  const nodesRef = useRef<D3Node[]>([]);

  // 优化：使用useRef来跟踪高消耗的计算和避免重渲染
  const simulationRef = useRef<d3.Simulation<D3Node, undefined> | null>(null);
  const requestAnimationFrameRef = useRef<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 使用节流函数来限制状态更新频率
  const throttledSetNodes = useRef(
    throttle((newNodes: D3Node[]) => {
      // 深度复制节点防止引用问题
      const nodesCopy = newNodes.map((node) => ({ ...node }));
      setNodes(nodesCopy);
    }, 33) // 约30fps的更新率
  ).current;

  // 优化：使用useRef缓存containerSize measurements来避免不必要的响应
  const containerSizeRef = useRef(containerSize);
  containerSizeRef.current = containerSize; // 保持最新

  // Filter and prepare token data - 添加记忆化
  const validTokens = useMemo(() => {
    return data.tokens
      .filter(
        (token) => token && token.symbol && token.usdValue && token.usdValue > 0
      )
      .slice(0, maxBubbles);
  }, [data.tokens, maxBubbles]);

  // Calculate total value - 添加记忆化
  const totalValue = useMemo(() => {
    return validTokens.reduce((sum, t) => sum + t.usdValue, 0) || 1;
  }, [validTokens]);

  // 优化：响应式测量容器大小并使用更高效的方法
  useLayoutEffect(() => {
    const updateContainerSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        if (
          Math.abs(rect.width - containerSize.width) > 5 ||
          Math.abs(rect.height - containerSize.height) > 5
        ) {
          setContainerSize({
            width: Math.floor(rect.width),
            height: Math.floor(rect.height),
          });
        }
      }
    };

    // 初始测量
    updateContainerSize();

    // 使用ResizeObserver而不是window resize event
    const resizeObserver = new ResizeObserver(
      throttle(updateContainerSize, 100)
    );
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Initialize D3 Force Simulation - 优化性能
  useEffect(() => {
    if (!containerSize.width || !containerSize.height) return;

    // 清理现有资源
    if (requestAnimationFrameRef.current) {
      cancelAnimationFrame(requestAnimationFrameRef.current);
    }

    if (simulationRef.current) {
      simulationRef.current.stop();
    }

    // 创建初始节点 - 使用更高效的方法
    const newNodes: D3Node[] = validTokens.map((token, index) => {
      const size = calculateBubbleSize(
        token.usdValue,
        totalValue,
        containerSize.width,
        minBubbleSize,
        maxBubbleSize
      );

      // 使用黄金比例计算初始位置
      const phi = (1 + Math.sqrt(5)) / 2;
      const angle = index * phi * Math.PI * 2;
      const radiusFactor = 0.35;
      const maxRadius =
        Math.min(containerSize.width, containerSize.height) * radiusFactor;
      const radius = Math.min(maxRadius, 60 + (index % 8) * 25);

      return {
        symbol: token.symbol,
        usdValue: token.usdValue,
        size,
        index,
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
      };
    });

    // 存储节点引用以避免不必要的重新渲染
    nodesRef.current = newNodes;
    setNodes(newNodes);

    // 创建力模拟 - 使用优化配置
    const simulation = d3
      .forceSimulation<D3Node>(newNodes)
      .alpha(SIMULATION_CONFIG.ALPHA)
      .alphaDecay(SIMULATION_CONFIG.ALPHA_DECAY)
      .alphaMin(SIMULATION_CONFIG.ALPHA_MIN)
      .velocityDecay(SIMULATION_CONFIG.VELOCITY_DECAY)
      .force(
        "center",
        d3.forceCenter<D3Node>(0, 0).strength(SIMULATION_CONFIG.CENTER_STRENGTH)
      )
      .force(
        "collision",
        d3
          .forceCollide<D3Node>()
          .radius((d) => (d.size / 2) * 1.05)
          .strength(SIMULATION_CONFIG.COLLISION_STRENGTH)
          .iterations(SIMULATION_CONFIG.COLLISION_ITERATIONS)
      )
      .force(
        "charge",
        d3
          .forceManyBody<D3Node>()
          .strength((d) => -Math.pow(d.size || 30, 0.75))
          .theta(SIMULATION_CONFIG.REPULSION_THETA)
          .distanceMin(SIMULATION_CONFIG.REPULSION_DISTANCE_MIN)
          .distanceMax(SIMULATION_CONFIG.REPULSION_DISTANCE_MAX)
      )
      .force("repulsion", createCustomRepulsionForce(newNodes))
      .force("x", d3.forceX(0).strength(SIMULATION_CONFIG.BOUNDS_STRENGTH))
      .force("y", d3.forceY(0).strength(SIMULATION_CONFIG.BOUNDS_STRENGTH))
      .force("bounds", () => {
        const maxX = containerSize.width / 2;
        const maxY = containerSize.height / 2;
        const padding = SIMULATION_CONFIG.PADDING;

        for (const node of newNodes) {
          if (!node.x || !node.y) continue;

          const r = node.size / 2;

          // 应用严格边界约束
          const constrainedX = Math.max(
            -maxX + r + padding,
            Math.min(maxX - r - padding, node.x)
          );
          const constrainedY = Math.max(
            -maxY + r + padding,
            Math.min(maxY - r - padding, node.y)
          );

          // 仅当位置超出边界时应用约束并反弹
          if (Math.abs(node.x) > maxX - r - padding) {
            node.x = constrainedX;
            if (node.vx) node.vx *= -0.7;
          }

          if (Math.abs(node.y) > maxY - r - padding) {
            node.y = constrainedY;
            if (node.vy) node.vy *= -0.7;
          }
        }

        // 在边界代码中添加速度限制
        for (const node of newNodes) {
          if (!node.x || !node.y || !node.vx || !node.vy) continue;

          // 限制最大速度，避免泡泡移动过快
          const maxSpeed = 3;
          const speed = Math.sqrt(node.vx * node.vx + node.vy * node.vy);
          if (speed > maxSpeed) {
            const ratio = maxSpeed / speed;
            node.vx *= ratio;
            node.vy *= ratio;
          }
        }
      });

    // 优化碰撞检测 - 使用四叉树
    let quadtree: d3.Quadtree<D3Node> | null = null;
    let lastCollisions = new Set<string>();

    // 更高效的动画帧更新
    const updateNodesPositions = () => {
      // 构建四叉树用于空间查询优化
      quadtree = d3
        .quadtree<D3Node>()
        .x((d) => d.x || 0)
        .y((d) => d.y || 0)
        .addAll(newNodes);

      // 检测并记录新碰撞
      const currentCollisions = new Set<string>();
      const newCollisions: CollisionEvent[] = [];

      // 更高效的碰撞检测：对每个节点，只检查可能碰撞的附近节点
      for (const node1 of newNodes) {
        if (!node1.x || !node1.y) continue;

        const radius = node1.size / 2;
        const searchRadius = radius * 2; // 搜索附近节点的半径

        // 使用四叉树查找潜在碰撞节点
        quadtree.visit((quad, x1, y1, x2, y2) => {
          // 检查是否为叶子节点（有data属性）
          if (!quad.length) {
            // TypeScript类型守卫
            if ("data" in quad) {
              const node2 = quad.data;
              if (!node2 || node1 === node2) return false;

              if (!node1.x || !node1.y || !node2.x || !node2.y) return false;

              const dx = node1.x - node2.x;
              const dy = node1.y - node2.y;
              const distance = Math.sqrt(dx * dx + dy * dy);
              const minDistance = (node1.size / 2 + node2.size / 2) * 0.9;

              const pairKey = [node1.symbol, node2.symbol].sort().join("-");
              currentCollisions.add(pairKey);

              if (distance < minDistance && !lastCollisions.has(pairKey)) {
                // 创建唯一ID
                const collisionId = `${node1.symbol}-${
                  node2.symbol
                }-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                newCollisions.push({
                  id: collisionId,
                  symbol1: node1.symbol,
                  symbol2: node2.symbol,
                  timestamp: Date.now(),
                  position: {
                    x: (node1.x + node2.x) / 2,
                    y: (node1.y + node2.y) / 2,
                  },
                });
              }
            }
            return false;
          }

          // 快速剪枝：如果当前四叉树节点与搜索圆不相交，则跳过该分支
          const nodeX = node1.x;
          const nodeY = node1.y;

          if (nodeX === undefined || nodeY === undefined) {
            return false;
          }

          const dx = nodeX - Math.max(x1, Math.min(nodeX, x2));
          const dy = nodeY - Math.max(y1, Math.min(nodeY, y2));

          return dx * dx + dy * dy > searchRadius * searchRadius;
        });
      }

      // 添加新碰撞事件
      if (newCollisions.length > 0) {
        setCollisionEvents((prev) => [...prev, ...newCollisions].slice(-10));
      }

      lastCollisions = currentCollisions;

      // 应用额外边界检查
      for (const node of newNodes) {
        if (!node.x || !node.y) continue;

        const r = node.size / 2;
        const maxX = containerSize.width / 2;
        const maxY = containerSize.height / 2;
        const padding = SIMULATION_CONFIG.PADDING;

        // 严格边界强制
        node.x = Math.max(
          -maxX + r + padding,
          Math.min(maxX - r - padding, node.x)
        );
        node.y = Math.max(
          -maxY + r + padding,
          Math.min(maxY - r - padding, node.y)
        );
      }

      // 使用节流函数更新节点位置以减少渲染
      if (
        simulation.alpha() > simulation.alphaMin() ||
        newCollisions.length > 0 ||
        simulation.alpha() < 0.01 // 稳定状态下减少更新
      ) {
        throttledSetNodes(newNodes);

        // 当模拟处于活跃状态时继续动画帧循环
        if (simulation.alpha() > simulation.alphaMin()) {
          requestAnimationFrameRef.current =
            requestAnimationFrame(updateNodesPositions);
        } else {
          // 模拟已经稳定，可以停止频繁更新
          requestAnimationFrameRef.current = null;
        }
      } else {
        // 最终更新
        throttledSetNodes(newNodes);
        requestAnimationFrameRef.current = null;
      }
    };

    // 设置tick处理器
    simulation.on("tick", () => {
      // 避免重复的动画帧请求
      if (!requestAnimationFrameRef.current) {
        requestAnimationFrameRef.current =
          requestAnimationFrame(updateNodesPositions);
      }
    });

    simulationRef.current = simulation;

    return () => {
      simulation.stop();
      if (requestAnimationFrameRef.current) {
        cancelAnimationFrame(requestAnimationFrameRef.current);
        requestAnimationFrameRef.current = null;
      }
    };
  }, [
    validTokens,
    totalValue,
    containerSize,
    minBubbleSize,
    maxBubbleSize,
    throttledSetNodes,
  ]);

  // Remove collision effect after animation
  const removeCollisionEvent = useCallback((timestamp: number, id: string) => {
    setCollisionEvents((prev) => prev.filter((e) => e.id !== id));
  }, []);

  // Handle token selection
  const handleTokenSelect = useCallback(
    (symbol: string) => {
      setSelectedToken(symbol);
      if (symbol && onBubbleClick) {
        const token = validTokens.find((t) => t.symbol === symbol);
        if (token) onBubbleClick(token);
      }
    },
    [validTokens, onBubbleClick]
  );

  // Get token details for UI display
  const selectedTokenData = useMemo(
    () =>
      selectedToken
        ? validTokens.find((t) => t.symbol === selectedToken)
        : null,
    [selectedToken, validTokens]
  );

  // 使用记忆化优化渲染
  const bubbleElements = useMemo(() => {
    return nodes.map((node) => {
      const token = validTokens.find((t) => t.symbol === node.symbol);
      if (!token) return null;

      return (
        <TokenBubble
          key={token.symbol}
          token={token}
          node={node}
          totalValue={totalValue}
          isSelected={selectedToken === token.symbol}
          onSelect={handleTokenSelect}
          containerSize={containerSize}
          simulation={simulationRef.current!}
          minBubbleSize={minBubbleSize}
          maxBubbleSize={maxBubbleSize}
        />
      );
    });
  }, [
    nodes,
    validTokens,
    totalValue,
    selectedToken,
    handleTokenSelect,
    containerSize,
    minBubbleSize,
    maxBubbleSize,
  ]);

  return (
    <div
      ref={containerRef}
      className="relative overflow-hidden rounded-xl bg-[#1a1a2e]"
      style={{ height: containerHeight }}
    >
      {/* 背景层 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[length:24px_24px]" />
      <div className="absolute inset-0 bg-gradient-to-br from-[#00ff8708] to-[#60efff08]" />

      {/* 碰撞特效层 */}
      {collisionEvents.map((event) => (
        <CollisionEffect
          key={event.id}
          event={event}
          onComplete={() => removeCollisionEvent(event.timestamp, event.id)}
        />
      ))}

      {/* 边缘装饰 */}
      <div className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-white/5 rounded-tl-xl" />
      <div className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-white/5 rounded-tr-xl" />
      <div className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-white/5 rounded-bl-xl" />
      <div className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-white/5 rounded-br-xl" />

      {/* 泡泡图 - 使用记忆化的元素数组 */}
      <div className="relative h-full">{bubbleElements}</div>

      {/* 提示文本 */}
      {!selectedToken && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white/40 text-sm px-4 py-1 bg-white/5 rounded-full">
          点击、拖动并撞击泡泡
        </div>
      )}

      {/* 选中状态信息 */}
      {selectedToken && selectedTokenData && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.2 }}
          className="absolute bottom-4 left-4 right-4 bg-white/10 backdrop-blur-md rounded-xl p-4 min-h-[60px] max-w-[calc(100%-32px)] mx-auto"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-white font-bold">{selectedToken}</span>
              <span className="text-white/60 text-sm">
                ${selectedTokenData.usdValue.toLocaleString()}
              </span>
            </div>
            <div className="text-white/60 text-sm">拖拽泡泡来探索更多信息</div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

// 创建自定义互斥力
const createCustomRepulsionForce = (nodes: D3Node[]) => {
  const strength = SIMULATION_CONFIG.REPULSION_STRENGTH;
  const theta = SIMULATION_CONFIG.REPULSION_THETA;
  const distanceMin = SIMULATION_CONFIG.REPULSION_DISTANCE_MIN;
  const distanceMax = SIMULATION_CONFIG.REPULSION_DISTANCE_MAX;
  const sizeFactor = SIMULATION_CONFIG.REPULSION_SIZE_FACTOR;

  // 缓动函数 - 使力的变化更平滑
  const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);

  // 返回一个自定义力场函数
  return function repulsionForce(alpha: number) {
    // 使用缓动函数调整alpha值，同时增强初始效果
    const easedAlpha = easeOutCubic(alpha) * 1.2; // 从0.8增加到1.2增强效果

    // 构建四叉树用于加速计算
    const tree = d3
      .quadtree<D3Node>()
      .x((d) => d.x || 0)
      .y((d) => d.y || 0)
      .addAll(nodes);

    // 对每个节点应用互斥力
    for (const node of nodes) {
      if (!node.x || !node.y) continue;

      // 根据泡泡大小调整互斥强度，增加大小因子影响
      const nodeSize = node.size || 0;
      const adjustedStrength = strength * (1 + (nodeSize * sizeFactor) / 50); // 从100减小到50增强效果

      // 在四叉树中寻找可能对当前节点产生影响的其他节点
      tree.visit((quad, x1, y1, x2, y2) => {
        if (!quad.length) {
          if ("data" in quad) {
            const otherNode = quad.data;

            // 跳过自己
            if (!otherNode || node === otherNode) return false;

            // 计算节点间距离与方向
            const dx = (node.x || 0) - (otherNode.x || 0);
            const dy = (node.y || 0) - (otherNode.y || 0);
            const l = Math.sqrt(dx * dx + dy * dy);
            const otherSize = otherNode.size || 0;

            // 根据两个泡泡的大小调整互斥距离，确保足够的互斥范围
            const combinedSize = (nodeSize + otherSize) / 2;
            const minDistance = Math.max(distanceMin, combinedSize * 0.7); // 从0.5增加到0.7
            const maxDistance = Math.min(distanceMax, combinedSize * 4); // 从3增加到4

            // 计算两个节点的理想间距（基于它们的大小）
            const idealDistance = (nodeSize + otherSize) / 1.8; // 理想间距为直径和的一半多一点

            // 如果距离在有效范围内，应用互斥力
            if (l > 0 && l < maxDistance) {
              // 基于当前距离与理想距离的比例计算力量
              let forceStrength;

              if (l < idealDistance) {
                // 如果太近，使用更强的排斥力
                forceStrength =
                  (adjustedStrength * easedAlpha) / Math.pow(l, 1.5);
              } else {
                // 如果在可接受范围内，使用较弱的排斥力
                forceStrength =
                  (adjustedStrength * easedAlpha) / Math.pow(l, 1.2);
              }

              // 应用一个平滑的距离衰减曲线
              let f = forceStrength;
              if (l < minDistance) {
                // 在最小距离内施加更强的力
                f =
                  forceStrength * (1 + easeOutCubic(1 - l / minDistance) * 1.5);
              } else {
                // 随距离增加平滑衰减互斥力
                const t = (l - minDistance) / (maxDistance - minDistance);
                f = forceStrength * (1 - t * t); // 二次曲线衰减更平滑
              }

              // 根据重叠程度调整力量
              const radiiSum = (nodeSize / 2 + otherSize / 2) * 1.1; // 期望的最小距离
              if (l < radiiSum) {
                // 当泡泡重叠时，增加额外的强制排斥力
                const overlapFactor = Math.pow(1 - l / radiiSum, 2) * 5;
                f = f * (1 + overlapFactor);
              }

              // 限制最大力量，防止剧烈运动，但增加上限
              const maxForce = 3.0; // 从2.0增加到3.0
              f = Math.max(-maxForce, Math.min(maxForce, f));

              // 应用力，确保足够强度以避免重叠
              const fx = dx * f;
              const fy = dy * f;

              if (node.vx !== undefined && node.vy !== undefined) {
                // 重叠时允许更大的速度变化
                const speedLimit = l < radiiSum ? 2 : 1; // 重叠时速度限制提高
                node.vx += Math.max(-speedLimit, Math.min(speedLimit, fx));
                node.vy += Math.max(-speedLimit, Math.min(speedLimit, fy));
              }
            }
          }
          return false;
        }

        // Barnes-Hut优化
        const nx = node.x || 0;
        const ny = node.y || 0;
        const centerX = (x1 + x2) / 2;
        const centerY = (y1 + y2) / 2;
        const dx = centerX - nx;
        const dy = centerY - ny;
        const l = Math.sqrt(dx * dx + dy * dy);

        // 如果区域的大小除以距离小于theta，表示这个区域可以作为整体处理
        // 否则继续深入遍历子节点
        return (x2 - x1) / l < theta;
      });
    }
  };
};

export default TokenBubbleMapD3;
