"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Heart, ExternalLink, Monitor, Smartphone, Globe, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { ToolWithUserData, Platform } from '@/types/tools';

interface ToolCardProps {
  tool: ToolWithUserData;
  onFavoriteToggle?: (toolId: string) => void;
  onUse?: (toolId: string) => void;
  isLoading?: boolean;
  className?: string;
}

// 平台图标映射
const platformIcons: Record<Platform, React.ComponentType<{ className?: string }>> = {
  Web: Globe,
  Windows: Monitor,
  Mac: Monitor,
  iOS: Smartphone,
  Android: Smartphone,
};

// 分类颜色映射
const categoryColors = {
  video: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  image: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  audio: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  ai: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  design: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300',
  productivity: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  analytics: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
  other: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
};

// 分类中文名映射
const categoryLabels = {
  video: '视频剪辑',
  image: '图片处理',
  audio: '音频制作',
  ai: 'AI助手',
  design: '设计工具',
  productivity: '效率工具',
  analytics: '数据分析',
  other: '其他工具',
};

export const ToolCard: React.FC<ToolCardProps> = ({
  tool,
  onFavoriteToggle,
  onUse,
  isLoading = false,
  className
}) => {
  const isFavorited = tool.user_data?.is_favorite || false;
  const useCount = tool.user_data?.use_count || 0;

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isLoading && onFavoriteToggle) {
      onFavoriteToggle(tool.id);
    }
  };

  const handleUseClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isLoading && onUse) {
      onUse(tool.id);
    }
    // 打开工具网站
    if (tool.website_url) {
      window.open(tool.website_url, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <Card className={cn(
      "group relative overflow-hidden transition-all duration-200 hover:shadow-lg hover:scale-[1.02]",
      isLoading && "opacity-50 pointer-events-none",
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {/* 工具图标 */}
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
              {tool.icon_url ? (
                <img 
                  src={tool.icon_url} 
                  alt={tool.name}
                  className="w-8 h-8 rounded object-cover"
                  onError={(e) => {
                    // 图标加载失败时显示默认图标
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : null}
              <Zap className={cn(
                "w-6 h-6 text-gray-500",
                tool.icon_url && "hidden"
              )} />
            </div>
            
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold truncate">
                {tool.name}
              </CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge 
                  variant="secondary" 
                  className={cn("text-xs", categoryColors[tool.category])}
                >
                  {categoryLabels[tool.category]}
                </Badge>
                {tool.is_free && (
                  <Badge variant="outline" className="text-xs text-green-600 border-green-600">
                    免费
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          {/* 收藏按钮 */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleFavoriteClick}
                  disabled={isLoading}
                  className={cn(
                    "h-8 w-8 p-0 transition-colors",
                    isFavorited 
                      ? "text-red-500 hover:text-red-600" 
                      : "text-gray-400 hover:text-red-500"
                  )}
                >
                  <Heart 
                    className={cn(
                      "h-4 w-4 transition-all",
                      isFavorited && "fill-current"
                    )} 
                  />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isFavorited ? '取消收藏' : '添加收藏'}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 工具描述 */}
        {tool.description && (
          <CardDescription className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
            {tool.description}
          </CardDescription>
        )}

        {/* 平台支持 */}
        <div className="flex items-center space-x-2 mb-4">
          <span className="text-xs text-gray-500">支持平台:</span>
          <div className="flex space-x-1">
            {tool.platform.map((platform) => {
              const IconComponent = platformIcons[platform];
              return (
                <TooltipProvider key={platform}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="p-1 rounded bg-gray-100 dark:bg-gray-800">
                        <IconComponent className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      {platform}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              );
            })}
          </div>
        </div>

        {/* 使用统计 */}
        {tool.user_data && (
          <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
            <span>使用次数: {useCount}</span>
            {tool.user_data.last_used_at && (
              <span>
                最后使用: {new Date(tool.user_data.last_used_at).toLocaleDateString()}
              </span>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          <Button
            onClick={handleUseClick}
            disabled={isLoading || !tool.website_url}
            className="flex-1"
            size="sm"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            使用工具
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
