"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Monitor, Smartphone, Globe, Check, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { ToolWithUserData, Platform } from '@/types/tools';

interface SimpleToolCardProps {
  tool: ToolWithUserData;
  onToggleSelection?: (toolId: string) => void;
  isLoading?: boolean;
  className?: string;
}

// 平台图标映射
const platformIcons: Record<Platform, React.ComponentType<{ className?: string }>> = {
  Web: Globe,
  Windows: Monitor,
  Mac: Monitor,
  iOS: Smartphone,
  Android: Smartphone,
};

// 分类中文名映射
const categoryLabels = {
  video: '视频剪辑',
  image: '图片处理',
  audio: '音频制作',
  ai: 'AI助手',
  design: '设计工具',
  productivity: '效率工具',
  analytics: '数据分析',
  other: '其他工具',
};

export const SimpleToolCard: React.FC<SimpleToolCardProps> = ({
  tool,
  onToggleSelection,
  isLoading = false,
  className
}) => {
  const isSelected = tool.is_selected || false;

  const handleToggleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isLoading && onToggleSelection) {
      onToggleSelection(tool.id);
    }
  };

  const handleUseClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // 打开工具网站
    if (tool.website_url) {
      window.open(tool.website_url, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <button
      className={cn(
        "relative bg-[#243447]/80 rounded-lg p-3 border border-[#475569] hover:bg-[#2d4059]/90 hover:border-[#4ecdc4]/60 transition-all duration-300 text-center group overflow-hidden w-full",
        isSelected && "bg-[#4ecdc4]/20 border-[#4ecdc4]",
        isLoading && "opacity-50 pointer-events-none",
        className
      )}
      onClick={handleToggleClick}
      disabled={isLoading}
    >
      {/* Gaming style corner indicators */}
      <div className="absolute top-1 left-1 w-2 h-2 border-t border-l border-[#4ecdc4]/50 rounded-tl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute top-1 right-1 w-2 h-2 border-t border-r border-[#4ecdc4]/50 rounded-tr opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-1 left-1 w-2 h-2 border-b border-l border-[#4ecdc4]/50 rounded-bl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-1 right-1 w-2 h-2 border-b border-r border-[#4ecdc4]/50 rounded-br opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      {/* 选择状态指示器 */}
      <div className="absolute top-2 right-2">
        {isSelected ? (
          <div className="w-5 h-5 bg-[#4ecdc4] rounded-full flex items-center justify-center">
            <Check className="w-3 h-3 text-white" />
          </div>
        ) : (
          <div className="w-5 h-5 border-2 border-gray-400 rounded-full flex items-center justify-center group-hover:border-[#4ecdc4]">
            <Plus className="w-3 h-3 text-gray-400 group-hover:text-[#4ecdc4]" />
          </div>
        )}
      </div>

      {/* 工具图标 */}
      <div className="mb-2">
        {tool.icon_url ? (
          <img
            src={tool.icon_url}
            alt={tool.name}
            className="w-8 h-8 mx-auto rounded object-cover"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
        ) : (
          <div className="w-8 h-8 mx-auto bg-[#4ecdc4]/20 rounded flex items-center justify-center">
            <span className="text-lg">{tool.name.charAt(0)}</span>
          </div>
        )}
      </div>

      {/* 工具名称 */}
      <div className="text-sm font-medium text-white group-hover:text-[#4ecdc4] transition-colors duration-300 mb-1">
        {tool.name}
      </div>

      {/* 分类标签 */}
      <div className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
        {categoryLabels[tool.category]}
      </div>

      {/* 免费标识 */}
      {tool.is_free && (
        <Badge variant="outline" className="mt-2 text-xs text-green-400 border-green-400">
          免费
        </Badge>
      )}

      {/* 使用按钮 */}
      {tool.website_url && (
        <Button
          onClick={handleUseClick}
          variant="ghost"
          size="sm"
          className="absolute bottom-2 left-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        >
          <ExternalLink className="h-3 w-3" />
        </Button>
      )}
    </button>
  );
};
