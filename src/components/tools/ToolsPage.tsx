"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Zap, TrendingUp, Star } from 'lucide-react';
import { ToolsGrid } from './ToolsGrid';
import { ToolFiltersComponent } from './ToolFilters';
import type { 
  ToolFilters, 
  GetToolsRequest, 
  GetToolsResponse,
  GetUserToolsResponse,
  ApiResponse 
} from '@/types/tools';

// API 调用函数
const fetchTools = async (params: GetToolsRequest): Promise<GetToolsResponse> => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });
  
  const response = await fetch(`/api/tools?${searchParams}`);
  if (!response.ok) {
    throw new Error('获取工具列表失败');
  }
  
  const result: ApiResponse<GetToolsResponse> = await response.json();
  if (!result.success) {
    throw new Error(result.error?.message || '获取工具列表失败');
  }
  
  return result.data!;
};

const fetchUserTools = async (favoritesOnly: boolean = false): Promise<GetUserToolsResponse> => {
  const searchParams = new URLSearchParams();
  if (favoritesOnly) {
    searchParams.append('favorites_only', 'true');
  }
  
  const response = await fetch(`/api/user/tools?${searchParams}`);
  if (!response.ok) {
    throw new Error('获取用户工具失败');
  }
  
  const result: ApiResponse<GetUserToolsResponse> = await response.json();
  if (!result.success) {
    throw new Error(result.error?.message || '获取用户工具失败');
  }
  
  return result.data!;
};

const toggleFavorite = async (toolId: string) => {
  const response = await fetch('/api/user/tools', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      action: 'toggle_favorite',
      tool_id: toolId,
    }),
  });
  
  if (!response.ok) {
    throw new Error('操作失败');
  }
  
  const result: ApiResponse<any> = await response.json();
  if (!result.success) {
    throw new Error(result.error?.message || '操作失败');
  }
  
  return result.data;
};

const recordUsage = async (toolId: string) => {
  const response = await fetch('/api/user/tools', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      action: 'record_usage',
      tool_id: toolId,
    }),
  });
  
  if (!response.ok) {
    throw new Error('记录使用失败');
  }
  
  const result: ApiResponse<any> = await response.json();
  if (!result.success) {
    throw new Error(result.error?.message || '记录使用失败');
  }
  
  return result.data;
};

export const ToolsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [filters, setFilters] = useState<ToolFilters>({});
  const [currentPage, setCurrentPage] = useState(0);
  const queryClient = useQueryClient();

  // 构建查询参数
  const getToolsParams: GetToolsRequest = {
    ...filters,
    limit: 20,
    offset: currentPage * 20,
  };

  // 查询所有工具
  const {
    data: toolsData,
    isLoading: isLoadingTools,
    isError: isErrorTools,
    refetch: refetchTools
  } = useQuery({
    queryKey: ['tools', getToolsParams],
    queryFn: () => fetchTools(getToolsParams),
    staleTime: 5 * 60 * 1000, // 5分钟
  });

  // 查询用户收藏的工具
  const {
    data: favoriteToolsData,
    isLoading: isLoadingFavorites,
    isError: isErrorFavorites,
    refetch: refetchFavorites
  } = useQuery({
    queryKey: ['user-tools', 'favorites'],
    queryFn: () => fetchUserTools(true),
    staleTime: 2 * 60 * 1000, // 2分钟
  });

  // 切换收藏状态
  const favoriteMutation = useMutation({
    mutationFn: toggleFavorite,
    onSuccess: () => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['tools'] });
      queryClient.invalidateQueries({ queryKey: ['user-tools'] });
      toast.success('操作成功');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // 记录使用
  const usageMutation = useMutation({
    mutationFn: recordUsage,
    onSuccess: () => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['tools'] });
      queryClient.invalidateQueries({ queryKey: ['user-tools'] });
    },
    onError: (error: Error) => {
      console.error('记录使用失败:', error);
      // 使用记录失败不显示错误提示，因为不影响用户体验
    },
  });

  // 处理筛选器变化
  const handleFiltersChange = useCallback((newFilters: ToolFilters) => {
    setFilters(newFilters);
    setCurrentPage(0); // 重置分页
  }, []);

  // 处理收藏切换
  const handleFavoriteToggle = useCallback((toolId: string) => {
    favoriteMutation.mutate(toolId);
  }, [favoriteMutation]);

  // 处理工具使用
  const handleUse = useCallback((toolId: string) => {
    usageMutation.mutate(toolId);
  }, [usageMutation]);

  // 处理加载更多
  const handleLoadMore = useCallback(() => {
    setCurrentPage(prev => prev + 1);
  }, []);

  // 重试函数
  const handleRetry = useCallback(() => {
    if (activeTab === 'all') {
      refetchTools();
    } else {
      refetchFavorites();
    }
  }, [activeTab, refetchTools, refetchFavorites]);

  // 当筛选条件变化时重置分页
  useEffect(() => {
    setCurrentPage(0);
  }, [filters]);

  const currentTools = activeTab === 'all' ? toolsData?.tools || [] : favoriteToolsData?.tools || [];
  const isLoading = activeTab === 'all' ? isLoadingTools : isLoadingFavorites;
  const isError = activeTab === 'all' ? isErrorTools : isErrorFavorites;
  const hasMore = activeTab === 'all' ? toolsData?.has_more || false : false;

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          创作工具推荐
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          发现和收藏优质的创作工具，提升你的创作效率
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 左侧筛选器 */}
        <div className="lg:col-span-1">
          <ToolFiltersComponent
            filters={filters}
            onFiltersChange={handleFiltersChange}
            className="sticky top-6"
          />
        </div>

        {/* 右侧内容区域 */}
        <div className="lg:col-span-3">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="all" className="flex items-center space-x-2">
                <Zap className="h-4 w-4" />
                <span>全部工具</span>
                {toolsData?.total && (
                  <Badge variant="secondary" className="ml-2">
                    {toolsData.total}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="favorites" className="flex items-center space-x-2">
                <Heart className="h-4 w-4" />
                <span>我的收藏</span>
                {favoriteToolsData?.total && (
                  <Badge variant="secondary" className="ml-2">
                    {favoriteToolsData.total}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              <ToolsGrid
                tools={currentTools}
                isLoading={isLoading}
                isError={isError}
                hasMore={hasMore}
                onLoadMore={handleLoadMore}
                onFavoriteToggle={handleFavoriteToggle}
                onUse={handleUse}
                onRetry={handleRetry}
                loadingToolId={favoriteMutation.isPending ? favoriteMutation.variables : undefined}
                emptyMessage="没有找到符合条件的工具，试试调整筛选条件。"
              />
            </TabsContent>

            <TabsContent value="favorites" className="space-y-6">
              <ToolsGrid
                tools={currentTools}
                isLoading={isLoading}
                isError={isError}
                hasMore={false} // 收藏页面不支持分页加载
                onFavoriteToggle={handleFavoriteToggle}
                onUse={handleUse}
                onRetry={handleRetry}
                loadingToolId={favoriteMutation.isPending ? favoriteMutation.variables : undefined}
                emptyMessage="你还没有收藏任何工具，去发现一些有用的工具吧！"
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};
