"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, Settings, Wrench, RefreshCw } from 'lucide-react';
import { SimpleToolCard } from './ToolCard';
import { TOOL_CATEGORIES } from '@/types/tools';
import type { 
  GetToolsResponse, 
  GetUserToolsResponse,
  ApiResponse,
  ToolCategory 
} from '@/types/tools';

// API 调用函数
const fetchTools = async (category?: ToolCategory): Promise<GetToolsResponse> => {
  const searchParams = new URLSearchParams();
  if (category) {
    searchParams.append('category', category);
  }
  
  const response = await fetch(`/api/tools?${searchParams}`);
  if (!response.ok) {
    throw new Error('获取工具列表失败');
  }
  
  const result: ApiResponse<GetToolsResponse> = await response.json();
  if (!result.success) {
    throw new Error(result.error?.message || '获取工具列表失败');
  }
  
  return result.data!;
};

const fetchUserTools = async (): Promise<GetUserToolsResponse> => {
  const response = await fetch('/api/user/tools');
  if (!response.ok) {
    throw new Error('获取用户工具失败');
  }
  
  const result: ApiResponse<GetUserToolsResponse> = await response.json();
  if (!result.success) {
    throw new Error(result.error?.message || '获取用户工具失败');
  }
  
  return result.data!;
};

const toggleToolSelection = async (toolId: string) => {
  const response = await fetch('/api/user/tools', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      tool_id: toolId,
    }),
  });
  
  if (!response.ok) {
    throw new Error('操作失败');
  }
  
  const result: ApiResponse<any> = await response.json();
  if (!result.success) {
    throw new Error(result.error?.message || '操作失败');
  }
  
  return result.data;
};

interface ToolSelectorProps {
  className?: string;
}

export const ToolSelector: React.FC<ToolSelectorProps> = ({ className }) => {
  const [selectedCategory, setSelectedCategory] = useState<ToolCategory | 'all'>('all');
  const [showAllTools, setShowAllTools] = useState(false);
  const queryClient = useQueryClient();

  // 查询所有工具
  const {
    data: allToolsData,
    isLoading: isLoadingAllTools,
    isError: isErrorAllTools,
    refetch: refetchAllTools
  } = useQuery({
    queryKey: ['tools', selectedCategory === 'all' ? undefined : selectedCategory],
    queryFn: () => fetchTools(selectedCategory === 'all' ? undefined : selectedCategory),
    staleTime: 5 * 60 * 1000, // 5分钟
  });

  // 查询用户选择的工具
  const {
    data: userToolsData,
    isLoading: isLoadingUserTools,
    isError: isErrorUserTools,
    refetch: refetchUserTools
  } = useQuery({
    queryKey: ['user-tools'],
    queryFn: fetchUserTools,
    staleTime: 2 * 60 * 1000, // 2分钟
  });

  // 切换选择状态
  const selectionMutation = useMutation({
    mutationFn: toggleToolSelection,
    onSuccess: () => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['tools'] });
      queryClient.invalidateQueries({ queryKey: ['user-tools'] });
      toast.success('操作成功');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const handleToggleSelection = (toolId: string) => {
    selectionMutation.mutate(toolId);
  };

  const handleRetry = () => {
    if (showAllTools) {
      refetchAllTools();
    } else {
      refetchUserTools();
    }
  };

  const currentTools = showAllTools ? allToolsData?.tools || [] : userToolsData?.tools || [];
  const isLoading = showAllTools ? isLoadingAllTools : isLoadingUserTools;
  const isError = showAllTools ? isErrorAllTools : isErrorUserTools;

  // 按分类分组工具
  const toolsByCategory = currentTools.reduce((acc, tool) => {
    if (!acc[tool.category]) {
      acc[tool.category] = [];
    }
    acc[tool.category].push(tool);
    return acc;
  }, {} as Record<ToolCategory, typeof currentTools>);

  if (isError) {
    return (
      <Card className={className}>
        <div className="p-4 text-center">
          <p className="text-red-500 mb-2">加载失败</p>
          <Button onClick={handleRetry} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            重试
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className="relative space-y-4 p-4">
        {/* Header */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-white flex items-center gap-3">
              <Wrench className="w-5 h-5 text-[#4ecdc4]" />
              创作工具
            </h3>
            <div className="text-xs bg-[#4ecdc4]/20 text-[#4ecdc4] px-3 py-1 rounded-full border border-[#4ecdc4]/30 font-medium">
              {currentTools.length} 个工具
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="w-2 h-2 bg-[#4ecdc4] rounded-full animate-pulse" />
            <h4 className="text-sm font-medium text-[#4ecdc4]">
              {showAllTools ? '🔧 选择你的工具' : '⭐ 我的工具箱'}
            </h4>
          </div>
        </div>

        {/* 分类筛选 */}
        {showAllTools && (
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('all')}
              className="text-xs"
            >
              全部
            </Button>
            {TOOL_CATEGORIES.map((category) => (
              <Button
                key={category.key}
                variant={selectedCategory === category.key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.key)}
                className="text-xs"
              >
                {category.label}
              </Button>
            ))}
          </div>
        )}

        {/* 工具网格 */}
        {isLoading ? (
          <div className="grid grid-cols-3 gap-3">
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="bg-[#243447]/80 rounded-lg p-3 border border-[#475569] animate-pulse"
              >
                <div className="w-8 h-8 bg-gray-600 rounded mx-auto mb-2" />
                <div className="h-3 bg-gray-600 rounded mb-1" />
                <div className="h-2 bg-gray-600 rounded w-2/3 mx-auto" />
              </div>
            ))}
          </div>
        ) : currentTools.length > 0 ? (
          <div className="grid grid-cols-3 gap-3">
            {currentTools.map((tool) => (
              <SimpleToolCard
                key={tool.id}
                tool={tool}
                onToggleSelection={handleToggleSelection}
                isLoading={selectionMutation.isPending}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <p className="text-gray-400 text-sm">
              {showAllTools ? '没有找到工具' : '还没有选择任何工具'}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button 
            onClick={() => setShowAllTools(!showAllTools)}
            className="flex-1 bg-[#4ecdc4]/15 text-[#4ecdc4] py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-[#4ecdc4]/25 transition-all duration-300 border border-[#4ecdc4]/30 font-medium shadow-lg hover:shadow-[#4ecdc4]/20"
          >
            <Plus className="w-4 h-4" />
            {showAllTools ? '返回工具箱' : '添加工具'}
          </Button>
          <Button 
            onClick={handleRetry}
            className="flex-1 bg-[#f39c12]/15 text-[#f39c12] py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-[#f39c12]/25 transition-all duration-300 border border-[#f39c12]/30 font-medium shadow-lg hover:shadow-[#f39c12]/20"
          >
            <Settings className="w-4 h-4" />
            刷新
          </Button>
        </div>
      </div>
    </Card>
  );
};
