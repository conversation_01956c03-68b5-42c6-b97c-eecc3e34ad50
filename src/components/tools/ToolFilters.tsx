"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Filter, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TOOL_CATEGORIES, PLATFORMS } from '@/types/tools';
import type { ToolFilters, ToolCategory, Platform } from '@/types/tools';

interface ToolFiltersProps {
  filters: ToolFilters;
  onFiltersChange: (filters: ToolFilters) => void;
  className?: string;
  showAsCard?: boolean;
}

export const ToolFiltersComponent: React.FC<ToolFiltersProps> = ({
  filters,
  onFiltersChange,
  className,
  showAsCard = true
}) => {
  const handleSearchChange = (value: string) => {
    onFiltersChange({
      ...filters,
      search: value || undefined
    });
  };

  const handleCategoryChange = (value: string) => {
    onFiltersChange({
      ...filters,
      category: value === 'all' ? undefined : (value as ToolCategory)
    });
  };

  const handlePlatformChange = (value: string) => {
    onFiltersChange({
      ...filters,
      platform: value === 'all' ? undefined : (value as Platform)
    });
  };

  const handleIsFreeChange = (value: string) => {
    let isFree: boolean | 'all';
    switch (value) {
      case 'true':
        isFree = true;
        break;
      case 'false':
        isFree = false;
        break;
      default:
        isFree = 'all';
    }
    
    onFiltersChange({
      ...filters,
      is_free: isFree === 'all' ? undefined : isFree
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      category: undefined,
      platform: undefined,
      is_free: undefined,
      search: undefined
    });
  };

  const hasActiveFilters = !!(
    filters.search || 
    filters.category || 
    filters.platform || 
    filters.is_free !== undefined
  );

  const content = (
    <div className="space-y-4">
      {/* 搜索框 */}
      <div className="space-y-2">
        <Label htmlFor="search">搜索工具</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            id="search"
            placeholder="输入工具名称或描述..."
            value={filters.search || ''}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* 分类筛选 */}
      <div className="space-y-2">
        <Label>工具分类</Label>
        <Select 
          value={filters.category || 'all'} 
          onValueChange={handleCategoryChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="选择分类" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部分类</SelectItem>
            {TOOL_CATEGORIES.map((category) => (
              <SelectItem key={category.key} value={category.key}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 平台筛选 */}
      <div className="space-y-2">
        <Label>支持平台</Label>
        <Select 
          value={filters.platform || 'all'} 
          onValueChange={handlePlatformChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="选择平台" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部平台</SelectItem>
            {PLATFORMS.map((platform) => (
              <SelectItem key={platform.key} value={platform.key}>
                {platform.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 免费/付费筛选 */}
      <div className="space-y-2">
        <Label>价格类型</Label>
        <Select 
          value={
            filters.is_free === undefined 
              ? 'all' 
              : filters.is_free 
                ? 'true' 
                : 'false'
          } 
          onValueChange={handleIsFreeChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="选择价格类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="true">免费工具</SelectItem>
            <SelectItem value="false">付费工具</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 活跃筛选器显示 */}
      {hasActiveFilters && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm">当前筛选</Label>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="h-6 px-2 text-xs"
            >
              <X className="h-3 w-3 mr-1" />
              清除
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {filters.search && (
              <Badge variant="secondary" className="text-xs">
                搜索: {filters.search}
              </Badge>
            )}
            {filters.category && (
              <Badge variant="secondary" className="text-xs">
                分类: {TOOL_CATEGORIES.find(c => c.key === filters.category)?.label}
              </Badge>
            )}
            {filters.platform && (
              <Badge variant="secondary" className="text-xs">
                平台: {PLATFORMS.find(p => p.key === filters.platform)?.label}
              </Badge>
            )}
            {filters.is_free !== undefined && (
              <Badge variant="secondary" className="text-xs">
                {filters.is_free ? '免费' : '付费'}
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );

  if (showAsCard) {
    return (
      <Card className={className}>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            筛选工具
          </CardTitle>
        </CardHeader>
        <CardContent>
          {content}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {content}
    </div>
  );
};
