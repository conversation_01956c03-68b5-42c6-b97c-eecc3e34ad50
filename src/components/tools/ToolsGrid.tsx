"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Package, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ToolCard } from './ToolCard';
import type { ToolWithUserData } from '@/types/tools';

interface ToolsGridProps {
  tools: ToolWithUserData[];
  isLoading?: boolean;
  isError?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  onFavoriteToggle?: (toolId: string) => void;
  onUse?: (toolId: string) => void;
  onRetry?: () => void;
  loadingToolId?: string;
  className?: string;
  emptyMessage?: string;
  errorMessage?: string;
}

// 加载骨架屏组件
const ToolCardSkeleton: React.FC = () => (
  <Card className="overflow-hidden">
    <div className="p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <Skeleton className="w-12 h-12 rounded-lg" />
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <div className="flex space-x-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-12" />
            </div>
          </div>
        </div>
        <Skeleton className="w-8 h-8 rounded" />
      </div>
      <Skeleton className="h-4 w-full mb-2" />
      <Skeleton className="h-4 w-3/4 mb-4" />
      <div className="flex space-x-2 mb-4">
        <Skeleton className="h-6 w-6" />
        <Skeleton className="h-6 w-6" />
        <Skeleton className="h-6 w-6" />
      </div>
      <Skeleton className="h-9 w-full" />
    </div>
  </Card>
);

// 空状态组件
const EmptyState: React.FC<{ message: string }> = ({ message }) => (
  <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
    <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
      <Package className="w-8 h-8 text-gray-400" />
    </div>
    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
      暂无工具
    </h3>
    <p className="text-gray-500 dark:text-gray-400 max-w-sm">
      {message}
    </p>
  </div>
);

// 错误状态组件
const ErrorState: React.FC<{ message: string; onRetry?: () => void }> = ({ 
  message, 
  onRetry 
}) => (
  <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
    <div className="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center mb-4">
      <AlertCircle className="w-8 h-8 text-red-500" />
    </div>
    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
      加载失败
    </h3>
    <p className="text-gray-500 dark:text-gray-400 max-w-sm mb-4">
      {message}
    </p>
    {onRetry && (
      <Button onClick={onRetry} variant="outline" size="sm">
        <RefreshCw className="w-4 h-4 mr-2" />
        重试
      </Button>
    )}
  </div>
);

export const ToolsGrid: React.FC<ToolsGridProps> = ({
  tools,
  isLoading = false,
  isError = false,
  hasMore = false,
  onLoadMore,
  onFavoriteToggle,
  onUse,
  onRetry,
  loadingToolId,
  className,
  emptyMessage = "没有找到符合条件的工具，试试调整筛选条件。",
  errorMessage = "加载工具列表时出现错误，请稍后重试。"
}) => {
  // 错误状态
  if (isError && tools.length === 0) {
    return (
      <div className={cn("grid grid-cols-1", className)}>
        <ErrorState message={errorMessage} onRetry={onRetry} />
      </div>
    );
  }

  // 空状态
  if (!isLoading && tools.length === 0) {
    return (
      <div className={cn("grid grid-cols-1", className)}>
        <EmptyState message={emptyMessage} />
      </div>
    );
  }

  return (
    <div className={className}>
      {/* 工具网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {tools.map((tool) => (
          <ToolCard
            key={tool.id}
            tool={tool}
            onFavoriteToggle={onFavoriteToggle}
            onUse={onUse}
            isLoading={loadingToolId === tool.id}
          />
        ))}
        
        {/* 加载中的骨架屏 */}
        {isLoading && tools.length === 0 && (
          <>
            {Array.from({ length: 8 }).map((_, index) => (
              <ToolCardSkeleton key={`skeleton-${index}`} />
            ))}
          </>
        )}
      </div>

      {/* 加载更多按钮 */}
      {hasMore && !isLoading && (
        <div className="flex justify-center mt-8">
          <Button
            onClick={onLoadMore}
            variant="outline"
            size="lg"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                加载中...
              </>
            ) : (
              '加载更多'
            )}
          </Button>
        </div>
      )}

      {/* 底部加载指示器 */}
      {isLoading && tools.length > 0 && (
        <div className="flex justify-center mt-8">
          <div className="flex items-center space-x-2 text-gray-500">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>加载更多工具...</span>
          </div>
        </div>
      )}
    </div>
  );
};
