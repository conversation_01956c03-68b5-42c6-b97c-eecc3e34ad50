import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PixelIcon } from "@/components/ui/pixel-icon";
import {
  Share2,
  Home,
  Trophy,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import HexLinearProgress from "./progress/HexLinearProgress";
import { useProfile } from "@/hooks/useProfile";
import { usePlatformConnections } from "@/hooks/usePlatformConnections";

// SVG 小蜜蜂装饰组件
const MiniCartoonBee = ({ className = "", scale = 1 }) => (
  <svg
    className={className}
    width={20 * scale}
    height={20 * scale}
    viewBox="0 0 20 20"
    fill="none"
  >
    {/* 蜜蜂身体 */}
    <ellipse
      cx="10"
      cy="12"
      rx="4"
      ry="6"
      fill="#FFD700"
      stroke="#FF8C00"
      strokeWidth="0.5"
    />

    {/* 蜜蜂条纹 */}
    <ellipse cx="10" cy="10" rx="3" ry="1" fill="#2D2D2D" />
    <ellipse cx="10" cy="12" rx="3" ry="1" fill="#2D2D2D" />
    <ellipse cx="10" cy="14" rx="3" ry="1" fill="#2D2D2D" />

    {/* 蜜蜂头部 */}
    <circle
      cx="10"
      cy="6"
      r="2.5"
      fill="#FFD700"
      stroke="#FF8C00"
      strokeWidth="0.5"
    />

    {/* 眼睛 */}
    <circle cx="9" cy="5.5" r="0.5" fill="white" />
    <circle cx="11" cy="5.5" r="0.5" fill="white" />
    <circle cx="9" cy="5.5" r="0.3" fill="black" />
    <circle cx="11" cy="5.5" r="0.3" fill="black" />

    {/* 触角 */}
    <line x1="9" y1="4" x2="8.5" y2="2.5" stroke="#2D2D2D" strokeWidth="0.5" />
    <line
      x1="11"
      y1="4"
      x2="11.5"
      y2="2.5"
      stroke="#2D2D2D"
      strokeWidth="0.5"
    />
    <circle cx="8.5" cy="2.5" r="0.3" fill="#FF8C00" />
    <circle cx="11.5" cy="2.5" r="0.3" fill="#FF8C00" />

    {/* 翅膀 */}
    <ellipse
      cx="7"
      cy="8"
      rx="2"
      ry="3"
      fill="rgba(255,255,255,0.7)"
      stroke="#60efff"
      strokeWidth="0.3"
    />
    <ellipse
      cx="13"
      cy="8"
      rx="2"
      ry="3"
      fill="rgba(255,255,255,0.7)"
      stroke="#60efff"
      strokeWidth="0.3"
    />
  </svg>
);

// SVG 小星星装饰组件
const MiniCartoonStar = ({ className = "", scale = 1, color = "#FFD700" }) => (
  <svg
    className={className}
    width={12 * scale}
    height={12 * scale}
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M6 1 L7 4 L10 4 L8 6 L9 9 L6 7 L3 9 L4 6 L2 4 L5 4 Z"
      fill={color}
      stroke="rgba(255,255,255,0.3)"
      strokeWidth="0.5"
    />
    <circle cx="6" cy="6" r="1" fill="rgba(255,255,255,0.5)" />
  </svg>
);

interface DashboardHeaderProps {
  variant?: "dashboard" | "homepage";
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  variant = "dashboard",
}) => {
  const { user } = useAuth();
  const router = useRouter();
  const { profile, isLoading: isProfileLoading } = useProfile();
  const getCompletionPercentage = usePlatformConnections((state) => state.getCompletionPercentage);

  // 只在 dashboard 模式下显示进度条
  if (variant !== "dashboard") {
    return null;
  }

  // 计算显示数据
  const level = profile?.level || 1;
  const experiencePoints = profile?.experience_points || 0;
  const pointsPerLevel = 100; // 每级100经验值
  
  // 计算当前等级的进度
  const currentLevelPoints = experiencePoints % pointsPerLevel; // 当前等级已获得的经验值
  const progress = (currentLevelPoints / pointsPerLevel) * 100; // 转换为百分比

  // 使用繁体字默认称号
  const title = "新手創作者";

  return (
    <header className="fixed top-0 left-0 right-0 z-50 w-full">
      {/* Decorative top border with animated gradient */}
      <div className="h-1 w-full bg-gradient-to-r from-transparent via-[#00ff87]/30 via-[#60efff]/30 to-transparent animate-pulse" />

      {/* Main header content */}
      <div className="bg-[#1a1a2e]/95 border-b border-[#2a2a3e] backdrop-blur-sm h-16">
        <div className="w-full h-full px-6">
          <div className="flex h-full items-center justify-center">
            {/* HexLinearProgress 占据整个导航栏空间 */}
            <div className="w-full h-full flex items-center">
              <HexLinearProgress
                progress={progress}
                width={typeof window !== 'undefined' ? window.innerWidth - 48 : 1200}
                height={64}
                title={title}
                level={level}
              />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;
