"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import EnhancedProgressBar from "@/components/ui/EnhancedProgressBar";
import { useProfile } from "@/hooks/useProfile";
import {
  Home,
  Youtube,
  Plus,
  Settings,
} from "lucide-react";

interface HomeSidebarProps {
  className?: string;
}

const HomeSidebar: React.FC<HomeSidebarProps> = ({ className = "" }) => {
  const { profile, isLoading: profileLoading } = useProfile();

  // Get user data from profile (using real backend data)
  const userName = profile?.full_name || "Creator";
  const avatar = profile?.avatar_url || "/lovable-uploads/f04b73ef-11c1-4465-968e-07be3d440339.png";
  const youtubeId = profile?.youtube_id;
  const walletAddress = profile?.wallet_address;

  // User level and experience data (from backend)
  const currentLevel = profile?.level || 1;
  const experiencePoints = profile?.experience_points || 0;

  // Mock title based on level (since backend doesn't have this)
  const getMockTitle = (level: number) => {
    if (level >= 5) return "內容之王 👑";
    if (level >= 3) return "熱點製造機 ⭐️";
    if (level >= 2) return "內容探索者 🔍";
    return "新手上路 🌱";
  };

  const title = getMockTitle(currentLevel);

  // Calculate XP progress for current level
  const currentLevelBaseXp = (currentLevel - 1) * 100;
  const currentLevelProgress = experiencePoints - currentLevelBaseXp;
  const xpForNextLevel = 100;
  const xpProgress = Math.max(0, Math.min(currentLevelProgress, xpForNextLevel));
  const xpPercentage = (xpProgress / xpForNextLevel) * 100;

  // YouTube analytics data (from backend or mock)
  const youtubeStats = {
    views: profile?.views || 0,
    likes: Math.floor((profile?.views || 0) * 0.05), // Mock: ~5% like rate
    comments: Math.floor((profile?.views || 0) * 0.02), // Mock: ~2% comment rate
  };

  // Connection status (based on real backend data)
  const connections = {
    youtube: !!youtubeId, // true if youtube_id exists
    wallet: !!walletAddress, // true if wallet_address exists
  };

  // Productivity tools data
  const productivityTools = [
    { name: "OBS Studio", icon: "📽️", url: "#" },
    { name: "Canva", icon: "🎨", url: "#" },
    { name: "Pinterest", icon: "📌", url: "#" },
    { name: "ChatGPT", icon: "🧠", url: "#" },
    { name: "Notion", icon: "📒", url: "#" },
    { name: "剪映 CapCut", icon: "✂️", url: "#" },
    { name: "Google Workspace", icon: "☁️", url: "#" },
  ];

  // Goals/Plans data with auto status calculation (mix of real and mock data)
  const goalsData = [
    {
      label: "訂閱者",
      current: profile?.subscribers || 0, // Real data from backend
      total: 1000, // Mock target
    },
    {
      label: "觀看次數",
      current: profile?.views || 0, // Real data from backend
      total: 10000, // Mock target
    },
    {
      label: "經驗值",
      current: profile?.experience_points || 0, // Real data from backend
      total: 500, // Mock target for next milestone
    },
    {
      label: "個人資料完成度",
      current: profile?.profile_completion_percentage || 0, // Real data from backend
      total: 100,
    },
    {
      label: "影片發布", // Mock goal since we don't have video count
      current: youtubeId ? 3 : 0, // Mock: if has YouTube, assume some videos
      total: 10,
    },
  ];

  // Calculate goals with auto status
  const goals = goalsData.map(goal => {
    const percentage = (goal.current / goal.total) * 100;
    let status: "completed" | "in_progress" | "not_started";

    if (percentage >= 100) {
      status = "completed";
    } else if (percentage > 0) {
      status = "in_progress";
    } else {
      status = "not_started";
    }

    return {
      ...goal,
      percentage,
      status,
    };
  });

  if (profileLoading) {
    return (
      <div className="w-full md:w-[340px] flex-shrink-0 bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl px-6 py-4 shadow-lg">
        <div className="animate-pulse flex flex-col gap-4">
          <div className="h-8 bg-[#2a2a3e] rounded-md w-3/4"></div>
          <div className="h-6 bg-[#2a2a3e] rounded-md w-1/2"></div>
          <div className="h-20 bg-[#2a2a3e] rounded-md w-full"></div>
          <div className="h-10 bg-[#2a2a3e] rounded-md w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <aside className={`w-full md:w-[340px] flex-shrink-0 ${className}`}>
      <div className="sticky top-28 space-y-5">
        {/* Home Tab Header - Optimized Layout */}
        <Card className="relative bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl px-6 py-5 shadow-lg hover:bg-[#2d3748]/95 hover:border-[#4ecdc4]/40 transition-all duration-300 group overflow-hidden">
          {/* Gaming corner decorations */}
          <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-[#4ecdc4]/60 rounded-tl-xl" />
          <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-[#4ecdc4]/60 rounded-tr-xl" />
          <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-[#4ecdc4]/60 rounded-bl-xl" />
          <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-[#4ecdc4]/60 rounded-br-xl" />

          {/* Animated background pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-3 left-3 w-1 h-1 bg-[#4ecdc4] rounded-full animate-pulse" />
            <div className="absolute top-5 right-5 w-1 h-1 bg-[#f39c12] rounded-full animate-pulse delay-300" />
            <div className="absolute bottom-3 left-5 w-1 h-1 bg-[#8e44ad] rounded-full animate-pulse delay-700" />
          </div>

          <div className="relative space-y-4">
            {/* Top row: Icon + Title + Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Home className="w-6 h-6 text-white" />
                  </div>
                  {/* Gaming style glow effect */}
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] opacity-0 group-hover:opacity-30 blur-md transition-opacity duration-300" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white group-hover:text-[#4ecdc4] transition-colors duration-300">
                    創作者基地
                  </h1>
                  <div className="text-xs text-[#94a3b8] flex items-center gap-2 mt-1">
                    <span className="w-2 h-2 bg-[#4ecdc4] rounded-full animate-pulse" />
                    系統運行正常
                  </div>
                </div>
              </div>

              {/* Status badge */}
              <div className="text-xs bg-[#4ecdc4]/20 text-[#4ecdc4] px-3 py-1 rounded-full border border-[#4ecdc4]/30 font-medium">
                ONLINE
              </div>
            </div>

            {/* Bottom row: User stats */}
            <div className="flex items-center justify-between">
              <div className="text-xs text-[#94a3b8]">
                當前用戶資訊
              </div>
              <div className="bg-[#0f1419]/80 rounded-lg px-3 py-2 border border-[#2d3748]/50">
                <div className="flex items-center gap-3 text-xs">
                  <div className="text-[#4ecdc4] font-mono">
                    ID: {profile?.user_numeric_id || 'N/A'}
                  </div>
                  <div className="w-1 h-1 bg-[#2d3748] rounded-full" />
                  <div className="text-[#f39c12] font-mono">
                    Lv.{currentLevel}
                  </div>
                  <div className="w-1 h-1 bg-[#2d3748] rounded-full" />
                  <div className="text-[#8e44ad] font-mono">
                    {experiencePoints}XP
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Section 1: User Profile & Progress (Gaming Style) */}
        <Card className="relative bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl px-6 py-6 shadow-lg overflow-hidden group hover:bg-[#2d3748]/95 hover:border-[#4ecdc4]/40 transition-all duration-300">
          {/* Gaming style hexagonal corners */}
          <div className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-[#475569] rounded-tl-xl group-hover:border-[#4ecdc4]/60 transition-colors duration-300" />
          <div className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-[#475569] rounded-tr-xl group-hover:border-[#4ecdc4]/60 transition-colors duration-300" />
          <div className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-[#475569] rounded-bl-xl group-hover:border-[#4ecdc4]/60 transition-colors duration-300" />
          <div className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-[#475569] rounded-br-xl group-hover:border-[#4ecdc4]/60 transition-colors duration-300" />

          {/* Gaming style background pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-3 left-8 w-1 h-4 bg-[#4ecdc4] rounded-full transform rotate-45" />
            <div className="absolute top-8 right-6 w-1 h-4 bg-[#f39c12] rounded-full transform -rotate-45" />
            <div className="absolute bottom-6 left-12 w-1 h-4 bg-[#8e44ad] rounded-full transform rotate-12" />
          </div>

          <div className="space-y-4">
            {/* User Info - Optimized Compact Layout */}
            <div className="space-y-4">
              {/* Main user info row */}
              <div className="flex items-center gap-4">
                {/* Gaming style avatar with level indicator */}
                <div className="relative flex-shrink-0">
                  <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-[#4ecdc4]/20 to-[#f39c12]/20 p-1 border border-[#475569] group-hover:border-[#4ecdc4]/60 transition-colors duration-300">
                    <img
                      src={avatar}
                      alt={userName}
                      className="w-full h-full rounded-lg object-cover"
                    />
                  </div>
                  {/* Gaming style level indicator */}
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r from-[#4ecdc4] to-[#6ee7de] rounded-full flex items-center justify-center border-2 border-[#334155] shadow-lg">
                    <span className="text-xs font-bold text-white">{currentLevel}</span>
                  </div>
                  {/* Gaming style glow effect */}
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-[#4ecdc4]/20 to-[#f39c12]/20 opacity-0 group-hover:opacity-50 blur-sm transition-opacity duration-300" />
                </div>

                {/* Name, title and badges */}
                <div className="flex-1 min-w-0 space-y-2">
                  {/* Name and VIP badge */}
                  <div className="flex items-center gap-2">
                    <h2 className="text-lg font-bold text-white">{userName}</h2>
                    {profile?.is_premium_member && (
                      <div className="bg-gradient-to-r from-[#f39c12] to-[#f5b041] text-white text-xs font-bold px-2 py-1 rounded-full border border-[#f39c12]/30 shadow-lg">
                        VIP
                      </div>
                    )}
                  </div>

                  {/* Title and role */}
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-[#4ecdc4] font-medium">{title}</div>
                    <div className="text-xs text-[#94a3b8] bg-[#0f1419]/60 px-2 py-1 rounded border border-[#2d3748]/50">
                      {profile?.role || "創作者"}
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced progress bar with embedded text */}
              <div className="bg-[#0f1419]/80 rounded-lg p-3 border border-[#2d3748]/50 hover:border-[#4ecdc4]/40 transition-colors duration-300">
                {/* Wide progress bar with embedded info */}
                <div className="mb-3">
                  <div className="text-xs text-[#94a3b8] mb-2">等級進度</div>

                  {/* Wide progress bar container */}
                  <div className="relative h-12 bg-[#1a2332]/80 rounded-lg overflow-hidden border border-[#475569]/30">
                    {/* Progress fill */}
                    <div
                      className="absolute inset-y-0 left-0 bg-gradient-to-r from-[#4ecdc4]/90 to-[#f39c12]/90 transition-all duration-1000 rounded-lg"
                      style={{ width: `${xpPercentage}%` }}
                    />

                    {/* Embedded text content */}
                    <div className="absolute inset-0 flex items-center justify-between px-4 z-10">
                      {/* Left side: Current level */}
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-[#4ecdc4]/20 rounded-full flex items-center justify-center border border-[#4ecdc4]/40">
                          <span className="text-xs font-bold text-[#4ecdc4]">{currentLevel}</span>
                        </div>
                        <span className="text-sm font-medium text-white">Lv.{currentLevel}</span>
                      </div>

                      {/* Center: Progress info */}
                      <div className="text-center">
                        <div className="text-sm font-bold text-white">{xpProgress} / {xpForNextLevel} XP</div>
                        <div className="text-xs text-white/80">{xpPercentage.toFixed(0)}% 完成</div>
                      </div>

                      {/* Right side: Next level */}
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-white/80">Lv.{currentLevel + 1}</span>
                        <div className="w-6 h-6 bg-[#f39c12]/20 rounded-full flex items-center justify-center border border-[#f39c12]/40">
                          <span className="text-xs font-bold text-[#f39c12]">{currentLevel + 1}</span>
                        </div>
                      </div>
                    </div>

                    {/* Progress indicator line */}
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-[#1a2332]/50">
                      <div
                        className="h-full bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] transition-all duration-1000"
                        style={{ width: `${xpPercentage}%` }}
                      />
                    </div>
                  </div>
                </div>

                {/* Compact stats grid */}
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div className="bg-[#1a2332]/50 rounded-lg py-2 border border-[#475569]/30">
                    <div className="text-[#f39c12] text-lg mb-1">⚡</div>
                    <div className="text-xs text-[#94a3b8]">總經驗</div>
                    <div className="text-sm text-[#cbd5e1] font-medium">{experiencePoints}</div>
                  </div>
                  <div className="bg-[#1a2332]/50 rounded-lg py-2 border border-[#475569]/30">
                    <div className="text-[#4ecdc4] text-lg mb-1">🏆</div>
                    <div className="text-xs text-[#94a3b8]">完成度</div>
                    <div className="text-sm text-[#cbd5e1] font-medium">{profile?.profile_completion_percentage || 0}%</div>
                  </div>
                  <div className="bg-[#1a2332]/50 rounded-lg py-2 border border-[#475569]/30">
                    <div className="text-[#8e44ad] text-lg mb-1">🎯</div>
                    <div className="text-xs text-[#94a3b8]">目標</div>
                    <div className="text-sm text-[#cbd5e1] font-medium">升級</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>



        {/* Section 3: Productivity Tools - Gaming Style */}
        <Card className="relative bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl px-6 py-4 shadow-lg overflow-hidden group hover:border-[#4ecdc4]/40 transition-all duration-300">
          {/* Gaming style header decoration */}
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#4ecdc4]/30 to-transparent" />

          <div className="relative space-y-4">
            {/* Header - Improved layout */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-white flex items-center gap-3">
                  <span className="text-xl">🎮</span>
                  基礎知識
                </h3>
                <div className="text-xs bg-[#4ecdc4]/20 text-[#4ecdc4] px-3 py-1 rounded-full border border-[#4ecdc4]/30 font-medium">
                  {productivityTools.length} 個工具
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-[#4ecdc4] rounded-full animate-pulse" />
                <h4 className="text-sm font-medium text-[#4ecdc4]">🔧 生產力工具 - 從這裡開始！</h4>
              </div>
            </div>

            {/* Tools Grid - Gaming Style (Fixed hover) */}
            <div className="grid grid-cols-2 gap-3">
              {productivityTools.map((tool, index) => (
                <button
                  key={index}
                  className="relative bg-[#243447]/80 rounded-lg p-3 border border-[#475569] hover:bg-[#2d4059]/90 hover:border-[#4ecdc4]/60 transition-all duration-300 text-center group overflow-hidden"
                  onClick={() => window.open(tool.url, '_blank')}
                >
                  {/* Gaming style corner indicators */}
                  <div className="absolute top-1 left-1 w-2 h-2 border-t border-l border-[#4ecdc4]/50 rounded-tl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="absolute top-1 right-1 w-2 h-2 border-t border-r border-[#4ecdc4]/50 rounded-tr opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Tool content - Fixed z-index */}
                  <div className="relative z-10">
                    <div className="text-lg mb-1 group-hover:scale-110 transition-transform duration-300">{tool.icon}</div>
                    <div className="text-xs text-[#cbd5e1] group-hover:text-white transition-colors duration-300 font-medium">{tool.name}</div>
                  </div>

                  {/* Gaming style hover effect - Lower z-index */}
                  <div className="absolute inset-0 bg-gradient-to-r from-[#4ecdc4]/10 to-[#f39c12]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0" />
                </button>
              ))}
            </div>

            {/* Action Buttons - Gaming Style */}
            <div className="flex gap-2">
              <button className="flex-1 bg-[#4ecdc4]/15 text-[#4ecdc4] py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-[#4ecdc4]/25 transition-all duration-300 border border-[#4ecdc4]/30 font-medium shadow-lg hover:shadow-[#4ecdc4]/20">
                <Plus className="w-4 h-4" />
                添加工具
              </button>
              <button className="flex-1 bg-[#f39c12]/15 text-[#f39c12] py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-[#f39c12]/25 transition-all duration-300 border border-[#f39c12]/30 font-medium shadow-lg hover:shadow-[#f39c12]/20">
                <Settings className="w-4 h-4" />
                自訂設定
              </button>
            </div>
          </div>
        </Card>

        {/* Section 4: Goals/Plans - Gaming Style */}
        <Card className="relative bg-[#1a202c]/95 backdrop-blur-sm border border-[#2d3748] rounded-xl px-6 py-4 shadow-lg overflow-hidden group hover:border-[#4ecdc4]/40 transition-all duration-300">
          {/* Gaming style top accent */}
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4ecdc4]/30 via-[#f39c12]/30 to-[#8e44ad]/30" />

          <div className="relative space-y-4">
            {/* Header - Improved layout */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-white flex items-center gap-3">
                  <span className="text-xl">🎯</span>
                  任務計劃
                </h3>
                <div className="text-xs bg-[#4ecdc4]/20 text-[#4ecdc4] px-3 py-1 rounded-full border border-[#4ecdc4]/30 font-medium">
                  {goals.length} 個任務
                </div>
              </div>

              {/* Status legend - Compact layout */}
              <div className="flex items-center gap-1 text-xs">
                <span className="text-[#94a3b8] mr-2">狀態：</span>
                <div className="flex items-center gap-1 text-[#4ecdc4] bg-[#4ecdc4]/10 px-2 py-1 rounded border border-[#4ecdc4]/20">
                  <span>✅</span>已完成
                </div>
                <div className="flex items-center gap-1 text-[#f39c12] bg-[#f39c12]/10 px-2 py-1 rounded border border-[#f39c12]/20">
                  <span>⏳</span>進行中
                </div>
                <div className="flex items-center gap-1 text-[#e74c3c] bg-[#e74c3c]/10 px-2 py-1 rounded border border-[#e74c3c]/20">
                  <span>❌</span>未開始
                </div>
              </div>
            </div>

            {/* Goals with Progress Bars */}
            <div className="space-y-3">
              {goals.map((goal, index) => (
                <EnhancedProgressBar
                  key={index}
                  current={goal.current}
                  total={goal.total}
                  percentage={goal.percentage}
                  label={goal.label}
                  status={goal.status}
                  variant="goal"
                  size="sm"
                  showStatus={true}
                  animated={true}
                />
              ))}
            </div>
          </div>
        </Card>
      </div>
    </aside>
  );
};

export default HomeSidebar;
