import React, { useState } from "react";
import ServicesList from "./ServicesList";
import ServiceForm from "./ServiceForm";
import type { Service } from "@/types/services";

interface ServicesManagerProps {
  creatorId: string;
}

const ServicesManager: React.FC<ServicesManagerProps> = ({ creatorId }) => {
  const [showForm, setShowForm] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleCreateService = () => {
    setEditingService(null);
    setShowForm(true);
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setShowForm(true);
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingService(null);
  };

  const handleFormSuccess = () => {
    // 刷新服務列表
    setRefreshKey(prev => prev + 1);
  };

  return (
    <>
      <ServicesList
        key={refreshKey}
        creatorId={creatorId}
        onEditService={handleEditService}
        onCreateService={handleCreateService}
      />
      
      <ServiceForm
        isOpen={showForm}
        onClose={handleFormClose}
        onSuccess={handleFormSuccess}
        editingService={editingService}
      />
    </>
  );
};

export default ServicesManager; 