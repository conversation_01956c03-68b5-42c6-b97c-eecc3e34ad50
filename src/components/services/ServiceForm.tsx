import React, { useState, useEffect } from "react";
import { X, <PERSON>rkles, FileText, DollarSign, Tag } from "lucide-react";
import { servicesApi } from "@/services/servicesApi";
import type {
  Service,
  ServiceType,
  CreateServiceRequest,
  UpdateServiceRequest,
} from "@/types/services";

// 服務模板定義（對應PRD要求）
const SERVICE_TEMPLATES = [
  {
    type: "embedded" as ServiceType,
    title: "短視頻口播",
    description: "60-90秒品牌宣傳視頻，口播形式推廣",
    priceMin: 300,
    priceMax: 600,
  },
  {
    type: "embedded" as ServiceType,
    title: "社交圖文",
    description: "Twitter/小紅書圖文推廣，高質量配圖",
    priceMin: 200,
    priceMax: 400,
  },
  {
    type: "custom" as ServiceType,
    title: "定製視頻",
    description: "腳本+拍攝+剪輯完整製作，深度定製內容",
    priceMin: 1000,
    priceMax: 2500,
  },
  {
    type: "custom" as ServiceType,
    title: "品牌合作",
    description: "深度品牌內容合作，長期戰略合作",
    priceMin: 1500,
    priceMax: 3000,
  },
];

interface ServiceFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editingService?: Service | null;
}

const ServiceForm: React.FC<ServiceFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
  editingService,
}) => {
  const [formData, setFormData] = useState<CreateServiceRequest>({
    type: "embedded",
    title: "",
    description: "",
    priceMin: 0,
    priceMax: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showTemplates, setShowTemplates] = useState(false);

  const isEditing = !!editingService;

  // 重置表單
  const resetForm = () => {
    setFormData({
      type: "embedded",
      title: "",
      description: "",
      priceMin: 0,
      priceMax: 0,
    });
    setError(null);
    setShowTemplates(false);
  };

  // 當編輯服務改變時更新表單
  useEffect(() => {
    if (editingService) {
      setFormData({
        type: editingService.type,
        title: editingService.title,
        description: editingService.description,
        priceMin: editingService.priceMin,
        priceMax: editingService.priceMax,
      });
      setShowTemplates(false);
    } else {
      resetForm();
    }
  }, [editingService]);

  // 當模態框打開/關閉時重置表單
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // 應用模板
  const applyTemplate = (template: typeof SERVICE_TEMPLATES[0]) => {
    setFormData({
      type: template.type,
      title: template.title,
      description: template.description,
      priceMin: template.priceMin,
      priceMax: template.priceMax,
    });
    setShowTemplates(false);
  };

  // 表單驗證
  const validateForm = (): string | null => {
    if (!formData.title.trim()) return "服務名稱不能為空";
    if (formData.title.length > 100) return "服務名稱不能超過100個字符";
    if (!formData.description.trim()) return "服務描述不能為空";
    if (formData.description.length > 500) return "服務描述不能超過500個字符";
    if (formData.priceMin < 0) return "最低價格不能小於0";
    if (formData.priceMax < 0) return "最高價格不能小於0";
    if (formData.priceMax < formData.priceMin) return "最高價格不能小於最低價格";
    return null;
  };

  // 提交表單
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (isEditing) {
        // 編輯模式
        const updateData: UpdateServiceRequest = {};
        if (formData.title !== editingService.title) updateData.title = formData.title;
        if (formData.description !== editingService.description) updateData.description = formData.description;
        if (formData.priceMin !== editingService.priceMin) updateData.priceMin = formData.priceMin;
        if (formData.priceMax !== editingService.priceMax) updateData.priceMax = formData.priceMax;

        await servicesApi.updateService(editingService.id, updateData);
      } else {
        // 創建模式
        await servicesApi.createService(formData);
      }

      onSuccess();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "操作失敗");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const filteredTemplates = SERVICE_TEMPLATES.filter(
    (template) => template.type === formData.type
  );

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
      <div className="bg-[#1a1a2e] rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-[#00cec9]/30 relative">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <Sparkles className="w-6 h-6 text-[#00cec9]" />
            {isEditing ? "編輯服務" : "新增服務"}
          </h3>
          <button
            onClick={onClose}
            className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-all"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* 服務類型 */}
          <div className="mb-6">
            <label className="block text-white font-medium mb-3">
              <Tag className="w-4 h-4 inline mr-2" />
              服務類型
            </label>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, type: "embedded" }))}
                className={`px-4 py-2 rounded-lg font-medium transition-all ${
                  formData.type === "embedded"
                    ? "bg-[#00cec9] text-white"
                    : "bg-white/10 text-white/60 hover:bg-white/20"
                }`}
                disabled={isEditing} // 編輯時不允許修改類型
              >
                植入推廣
              </button>
              <button
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, type: "custom" }))}
                className={`px-4 py-2 rounded-lg font-medium transition-all ${
                  formData.type === "custom"
                    ? "bg-[#00cec9] text-white"
                    : "bg-white/10 text-white/60 hover:bg-white/20"
                }`}
                disabled={isEditing} // 編輯時不允許修改類型
              >
                定製推廣
              </button>
            </div>
            {isEditing && (
              <p className="text-white/40 text-sm mt-1">編輯時不能修改服務類型</p>
            )}
          </div>

          {/* 服務模板 */}
          {!isEditing && (
            <div className="mb-6">
              <button
                type="button"
                onClick={() => setShowTemplates(!showTemplates)}
                className="flex items-center gap-2 text-[#00cec9] hover:text-[#00b894] font-medium mb-3"
              >
                <FileText className="w-4 h-4" />
                {showTemplates ? "隱藏模板" : "使用服務模板"}
              </button>

              {showTemplates && (
                <div className="grid gap-2 mb-4">
                  {filteredTemplates.map((template, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => applyTemplate(template)}
                      className="text-left p-3 bg-white/5 rounded-lg border border-white/10 hover:border-[#00cec9]/30 transition-all"
                    >
                      <div className="font-medium text-white">{template.title}</div>
                      <div className="text-white/60 text-sm mb-1">{template.description}</div>
                      <div className="text-[#00cec9] text-sm font-medium">
                        ${template.priceMin} - ${template.priceMax} USDT
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* 服務名稱 */}
          <div className="mb-6">
            <label className="block text-white font-medium mb-2">
              服務名稱 <span className="text-red-400">*</span>
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="請輸入服務名稱（1-100字符）"
              className="w-full px-4 py-3 bg-white/10 text-white border border-white/20 rounded-lg focus:outline-none focus:border-[#00cec9] transition-all"
              maxLength={100}
              required
            />
            <div className="text-white/40 text-sm mt-1">
              {formData.title.length}/100
            </div>
          </div>

          {/* 服務描述 */}
          <div className="mb-6">
            <label className="block text-white font-medium mb-2">
              服務描述 <span className="text-red-400">*</span>
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="請詳細描述您的服務內容（1-500字符）"
              className="w-full px-4 py-3 bg-white/10 text-white border border-white/20 rounded-lg focus:outline-none focus:border-[#00cec9] transition-all resize-none"
              rows={4}
              maxLength={500}
              required
            />
            <div className="text-white/40 text-sm mt-1">
              {formData.description.length}/500
            </div>
          </div>

          {/* 價格區間 */}
          <div className="mb-6">
            <label className="block text-white font-medium mb-2">
              <DollarSign className="w-4 h-4 inline mr-1" />
              價格區間 (USDT) <span className="text-red-400">*</span>
            </label>
            <div className="flex gap-4">
              <div className="flex-1">
                <input
                  type="number"
                  value={formData.priceMin}
                  onChange={(e) => setFormData(prev => ({ ...prev, priceMin: Number(e.target.value) }))}
                  placeholder="最低價格"
                  className="w-full px-4 py-3 bg-white/10 text-white border border-white/20 rounded-lg focus:outline-none focus:border-[#00cec9] transition-all"
                  min={0}
                  step={0.01}
                  required
                />
              </div>
              <div className="flex items-center text-white/60">-</div>
              <div className="flex-1">
                <input
                  type="number"
                  value={formData.priceMax}
                  onChange={(e) => setFormData(prev => ({ ...prev, priceMax: Number(e.target.value) }))}
                  placeholder="最高價格"
                  className="w-full px-4 py-3 bg-white/10 text-white border border-white/20 rounded-lg focus:outline-none focus:border-[#00cec9] transition-all"
                  min={0}
                  step={0.01}
                  required
                />
              </div>
            </div>
          </div>

          {/* 錯誤提示 */}
          {error && (
            <div className="mb-6 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400">
              {error}
            </div>
          )}

          {/* 按鈕區域 */}
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 rounded-lg bg-white/10 text-white hover:bg-white/20 border border-white/20 transition-all"
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#00cec9] to-[#00b894] text-white font-bold shadow hover:scale-105 transition-all disabled:opacity-50 disabled:hover:scale-100"
              disabled={loading}
            >
              {loading ? "保存中..." : isEditing ? "更新服務" : "創建服務"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ServiceForm; 