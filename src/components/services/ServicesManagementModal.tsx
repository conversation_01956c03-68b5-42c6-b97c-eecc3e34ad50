import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON>, HelpCircle } from "lucide-react";
import ServicesManager from "./ServicesManager";

interface ServicesManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  creatorId: string;
}

const ServicesManagementModal: React.FC<ServicesManagementModalProps> = ({
  isOpen,
  onClose,
  creatorId,
}) => {
  const [showUsageTips, setShowUsageTips] = useState(false);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          />

          {/* 弹窗内容 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-5xl max-h-[90vh] bg-gradient-to-br from-[#1a1a2e] via-[#2a2a3e] to-[#1a1a2e] rounded-2xl shadow-2xl border border-white/10 overflow-hidden"
          >
            {/* 標題欄 */}
            <div className="relative bg-gradient-to-r from-[#00cec9]/20 to-[#00b894]/20 px-6 py-4 border-b border-white/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-[#00cec9] to-[#00b894] rounded-xl flex items-center justify-center">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">服務管理</h2>
                    <p className="text-white/60 text-sm">
                      配置和管理您的服務項目，設置合適的價格吸引品牌方合作
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {/* 使用提示按鈕 */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowUsageTips(true)}
                    className="flex items-center gap-2 px-3 py-2 bg-[#00cec9]/20 text-[#00cec9] rounded-lg text-sm font-medium hover:bg-[#00cec9]/30 transition-all border border-[#00cec9]/30"
                  >
                    <HelpCircle className="w-4 h-4" />
                    使用提示
                  </motion.button>
                  {/* 關閉按鈕 */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={onClose}
                    className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-all"
                  >
                    <X className="w-5 h-5" />
                  </motion.button>
                </div>
              </div>
            </div>

            {/* 內容區域 */}
            <div className="relative overflow-y-auto max-h-[calc(90vh-120px)] p-6">
              {/* 背景裝飾 - 蜂窩圖案 */}
              <div
                className="absolute inset-0 opacity-20"
                style={{
                  backgroundImage: `
                    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.08) 1px, transparent 2px),
                    radial-gradient(circle at 75% 25%, rgba(0,206,201,0.06) 1px, transparent 2px),
                    radial-gradient(circle at 50% 75%, rgba(255,255,255,0.04) 1px, transparent 2px)
                  `,
                  backgroundSize: '30px 26px',
                  backgroundPosition: '0 0, 15px 0, 7.5px 13px'
                }}
              />
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(0,206,201,0.1)_0%,transparent_50%)] opacity-30" />

              {/* ServicesManager組件 */}
              <div className="relative">
                <ServicesManager creatorId={creatorId} />
              </div>
            </div>
          </motion.div>

          {/* 使用提示彈窗 */}
          <AnimatePresence>
            {showUsageTips && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="absolute inset-4 z-10 flex items-center justify-center"
              >
                <div
                  className="absolute inset-0 bg-black/40"
                  onClick={() => setShowUsageTips(false)}
                />
                <div className="relative bg-[#1a1a2e]/95 backdrop-blur-sm rounded-xl p-6 border border-[#00cec9]/30 max-w-2xl mx-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white font-bold text-lg flex items-center gap-2">
                      <Sparkles className="w-5 h-5 text-[#00cec9]" />
                      使用提示
                    </h3>
                    <button
                      onClick={() => setShowUsageTips(false)}
                      className="p-1 text-white/60 hover:text-white hover:bg-white/10 rounded transition-all"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="grid gap-3 text-white/70">
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-[#00cec9] rounded-full mt-2 flex-shrink-0" />
                      <p>
                        <strong className="text-white">植入推廣</strong>：適合短視頻口播、社交圖文等標準化推廣服務
                      </p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-[#00cec9] rounded-full mt-2 flex-shrink-0" />
                      <p>
                        <strong className="text-white">定製推廣</strong>：適合定製視頻、品牌合作等深度合作服務
                      </p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-[#00cec9] rounded-full mt-2 flex-shrink-0" />
                      <p>
                        <strong className="text-white">價格設置</strong>：建議根據您的粉絲量、內容質量和市場行情合理定價
                      </p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-[#00cec9] rounded-full mt-2 flex-shrink-0" />
                      <p>
                        <strong className="text-white">服務描述</strong>：詳細描述服務內容和交付物，有助於吸引更多合作機會
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
    </AnimatePresence>
  );
};

export default ServicesManagementModal; 