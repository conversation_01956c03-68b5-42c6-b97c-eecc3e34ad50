import React, { useState, useEffect } from "react";
import { Sparkles, Plus, Edit, Trash2 } from "lucide-react";
import { servicesApi } from "@/services/servicesApi";
import Switch from "@/components/ui/switch";
import type {
  Service,
  ServiceType,
  ServiceStatus,
  MyServicesResponse,
} from "@/types/services";

interface ServicesListProps {
  creatorId: string;
  onEditService: (service: Service) => void;
  onCreateService: () => void;
}

const ServicesList: React.FC<ServicesListProps> = ({
  creatorId,
  onEditService,
  onCreateService,
}) => {
  const [activeTab, setActiveTab] = useState<ServiceType>("embedded");
  const [services, setServices] = useState<MyServicesResponse>({
    embedded: [],
    custom: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 載入服務數據
  const loadServices = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await servicesApi.getMyServices();
      setServices(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "載入服務失敗");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadServices();
  }, []);

  // 切換服務狀態
  const handleToggleStatus = async (service: Service, newStatus: boolean) => {
    try {
      const status: ServiceStatus = newStatus ? "active" : "inactive";
      await servicesApi.toggleServiceStatus(service.id, service.status);

      // 更新本地狀態
      setServices((prev) => ({
        ...prev,
        [activeTab]: prev[activeTab].map((s) =>
          s.id === service.id ? { ...s, status } : s
        ),
      }));
    } catch (err) {
      console.error("切換狀態失敗:", err);
      // TODO: 添加錯誤提示
    }
  };

  // 刪除服務
  const handleDeleteService = async (serviceId: string) => {
    if (!confirm("確定要刪除這個服務嗎？")) return;

    try {
      await servicesApi.deleteService(serviceId);

      // 更新本地狀態
      setServices((prev) => ({
        ...prev,
        [activeTab]: prev[activeTab].filter((s) => s.id !== serviceId),
      }));
    } catch (err) {
      console.error("刪除服務失敗:", err);
      // TODO: 添加錯誤提示
    }
  };

  const currentServices = services[activeTab];

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-white/60">載入中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-400">錯誤: {error}</div>
      </div>
    );
  }

  return (
    <div className="bg-[#1a1a2e] rounded-xl p-6 border border-[#00cec9]/30">
      {/* 標題和新增按鈕 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Sparkles className="w-6 h-6 text-[#00cec9]" />
          <h2 className="text-xl font-bold text-white">我的服務</h2>
        </div>
        <button
          onClick={onCreateService}
          className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-[#00cec9] to-[#00b894] text-white font-medium rounded-lg hover:scale-105 transition-all"
        >
          <Plus className="w-4 h-4" />
          新增服務
        </button>
      </div>

      {/* 標籤切換 */}
      <div className="flex gap-1 mb-6">
        <button
          onClick={() => setActiveTab("embedded")}
          className={`px-4 py-2 rounded-lg font-medium transition-all ${
            activeTab === "embedded"
              ? "bg-[#00cec9] text-white"
              : "bg-white/10 text-white/60 hover:bg-white/20"
          }`}
        >
          植入推廣 ({services.embedded.length})
        </button>
        <button
          onClick={() => setActiveTab("custom")}
          className={`px-4 py-2 rounded-lg font-medium transition-all ${
            activeTab === "custom"
              ? "bg-[#00cec9] text-white"
              : "bg-white/10 text-white/60 hover:bg-white/20"
          }`}
        >
          定製推廣 ({services.custom.length})
        </button>
      </div>

      {/* 服務列表 */}
      {currentServices.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-white/40 mb-4">還沒有任何服務</div>
          <button
            onClick={onCreateService}
            className="text-[#00cec9] hover:text-[#00b894] font-medium"
          >
            點擊新增第一個服務
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {currentServices.map((service) => (
            <div
              key={service.id}
              className="flex items-center justify-between p-4 bg-white/5 rounded-lg border border-white/10 hover:border-[#00cec9]/30 transition-all"
            >
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="font-semibold text-white">{service.title}</h3>
                  <div className="flex items-center gap-2">
                    <span className="text-white/60 text-xs">
                      {service.status === "active" ? "上架" : "下架"}
                    </span>
                    <Switch
                      checked={service.status === "active"}
                      onCheckedChange={(checked) => handleToggleStatus(service, checked)}
                      size="sm"
                    />
                  </div>
                </div>
                <p className="text-white/60 text-sm mb-2">
                  {service.description}
                </p>
                <div className="text-[#00cec9] font-bold">
                  ${service.priceMin} - ${service.priceMax} USDT
                </div>
              </div>

              <div className="flex items-center gap-2 ml-4">
                <button
                  onClick={() => onEditService(service)}
                  className="p-2 text-white/60 hover:text-[#00cec9] hover:bg-white/10 rounded-lg transition-all"
                  title="編輯"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteService(service.id)}
                  className="p-2 text-white/60 hover:text-red-400 hover:bg-white/10 rounded-lg transition-all"
                  title="刪除"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ServicesList; 