import React, { useState, useEffect } from "react";
import { <PERSON>rk<PERSON>, MessageSquare, DollarSign } from "lucide-react";
import { servicesApi } from "@/services/servicesApi";
import type { PublicServicesResponse } from "@/types/services";

interface ServicesDisplayProps {
  creatorId: string;
  showContactButton?: boolean;
  onContactClick?: () => void;
}

const ServicesDisplay: React.FC<ServicesDisplayProps> = ({
  creatorId,
  showContactButton = true,
  onContactClick,
}) => {
  const [servicesData, setServicesData] = useState<PublicServicesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载公开服务数据
  const loadServices = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await servicesApi.getPublicServices(creatorId);
      setServicesData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "加载服务失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (creatorId) {
      loadServices();
    }
  }, [creatorId]);

  // 默认联系方式处理
  const handleContactClick = () => {
    if (onContactClick) {
      onContactClick();
    } else {
      // MVP阶段的默认处理：跳转到统一联系方式页或显示提示
      alert("请通过平台私信功能联系创作者");
    }
  };

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-[#00cec9]/10 to-[#00b894]/10 border border-[#00cec9]/20 rounded-xl p-6">
        <div className="flex items-center gap-2 mb-4">
          <Sparkles className="w-5 h-5 text-[#00cec9]" />
          <span className="text-lg font-bold text-white">服务与报价</span>
        </div>
        <div className="text-center py-8 text-white/60">加载中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-br from-[#00cec9]/10 to-[#00b894]/10 border border-[#00cec9]/20 rounded-xl p-6">
        <div className="flex items-center gap-2 mb-4">
          <Sparkles className="w-5 h-5 text-[#00cec9]" />
          <span className="text-lg font-bold text-white">服务与报价</span>
        </div>
        <div className="text-center py-8 text-red-400">加载失败: {error}</div>
      </div>
    );
  }

  const hasServices = servicesData && (
    servicesData.services.embedded.length > 0 ||
    servicesData.services.custom.length > 0
  );

  return (
    <div className="bg-gradient-to-br from-[#00cec9]/10 to-[#00b894]/10 border border-[#00cec9]/20 rounded-xl p-6 shadow group">
      {/* 标题 */}
      <div className="flex items-center gap-2 mb-6">
        <Sparkles className="w-5 h-5 text-[#00cec9]" />
        <span className="text-lg font-bold text-white">服务与报价</span>
      </div>

      {!hasServices ? (
        <div className="text-center py-8">
          <div className="text-white/60 mb-4">创作者还未设置服务项</div>
          {showContactButton && (
            <button
              onClick={handleContactClick}
              className="px-4 py-2 bg-[#00cec9]/20 text-[#00cec9] rounded-lg font-medium hover:bg-[#00cec9]/30 transition-all"
            >
              联系咨询
            </button>
          )}
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2">
          {/* 植入推广服务 */}
          {servicesData.services.embedded.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-white font-semibold text-lg border-b border-white/20 pb-2">
                植入推广
              </h3>
              <div className="space-y-3">
                {servicesData.services.embedded.map((service) => (
                  <div
                    key={service.id}
                    className="bg-white/5 rounded-lg p-4 border border-white/10 hover:border-[#00cec9]/30 transition-all"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-white">{service.title}</h4>
                      <div className="flex items-center gap-1 text-[#00cec9] font-bold">
                        <DollarSign className="w-4 h-4" />
                        {service.priceMin === service.priceMax
                          ? `${service.priceMin}`
                          : `${service.priceMin} - ${service.priceMax}`}
                      </div>
                    </div>
                    <p className="text-white/70 text-sm mb-3 overflow-hidden" style={{ 
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {service.description}
                    </p>
                    {showContactButton && (
                      <button
                        onClick={handleContactClick}
                        className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-[#00cec9]/20 to-[#00b894]/20 text-[#00cec9] rounded-lg text-sm font-medium hover:from-[#00cec9]/30 hover:to-[#00b894]/30 transition-all"
                      >
                        <MessageSquare className="w-4 h-4" />
                        立即咨询
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 定制推广服务 */}
          {servicesData.services.custom.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-white font-semibold text-lg border-b border-white/20 pb-2">
                定制推广
              </h3>
              <div className="space-y-3">
                {servicesData.services.custom.map((service) => (
                  <div
                    key={service.id}
                    className="bg-white/5 rounded-lg p-4 border border-white/10 hover:border-[#00cec9]/30 transition-all"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-white">{service.title}</h4>
                      <div className="flex items-center gap-1 text-[#00cec9] font-bold">
                        <DollarSign className="w-4 h-4" />
                        {service.priceMin === service.priceMax
                          ? `${service.priceMin}`
                          : `${service.priceMin} - ${service.priceMax}`}
                      </div>
                    </div>
                    <p className="text-white/70 text-sm mb-3 overflow-hidden" style={{ 
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {service.description}
                    </p>
                    {showContactButton && (
                      <button
                        onClick={handleContactClick}
                        className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-[#00cec9]/20 to-[#00b894]/20 text-[#00cec9] rounded-lg text-sm font-medium hover:from-[#00cec9]/30 hover:to-[#00b894]/30 transition-all"
                      >
                        <MessageSquare className="w-4 h-4" />
                        立即咨询
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 通用联系按钮（当有服务但希望显示统一联系方式时） */}
      {hasServices && showContactButton && (
        <div className="mt-6 pt-4 border-t border-white/20">
          <div className="text-center">
            <button
              onClick={handleContactClick}
              className="px-6 py-3 bg-gradient-to-r from-[#00cec9] to-[#00b894] text-white font-bold rounded-lg shadow hover:scale-105 transition-all"
            >
              联系合作
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServicesDisplay; 