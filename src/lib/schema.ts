// TypeScript types for the profiles table (without drizzle)
export interface Profile {
  clerk_user_id: string;
  email: string;
  clerk_username?: string | null;
  email_verified: boolean;
  external_accounts: ExternalAccounts;
  clerk_created_at?: Date | null;
  clerk_updated_at?: Date | null;
  full_name?: string | null;
  avatar_url?: string | null;
  motto?: string | null;
  role: UserRole;
  experience_points: number;
  level: number;
  is_premium_member: boolean;
  user_numeric_id?: number | null;
  youtube_id?: string | null;
  youtube_title?: string | null;
  subscribers: number;
  views: number;
  wallet_address?: string | null;
  profile_completion_percentage: number;
  last_login_at?: Date | null;
  preferred_language: string;
  notification_preferences: NotificationPreferences;
  social_links: SocialLinks;
  created_at: Date;
  updated_at: Date;
}

export interface NewProfile {
  clerk_user_id: string;
  email: string;
  clerk_username?: string;
  email_verified?: boolean;
  external_accounts?: ExternalAccounts;
  clerk_created_at?: Date;
  clerk_updated_at?: Date;
  full_name?: string;
  avatar_url?: string;
  motto?: string;
  role?: UserRole;
  experience_points?: number;
  level?: number;
  is_premium_member?: boolean;
  user_numeric_id?: number;
  youtube_id?: string;
  youtube_title?: string;
  subscribers?: number;
  views?: number;
  wallet_address?: string;
  profile_completion_percentage?: number;
  last_login_at?: Date;
  preferred_language?: string;
  notification_preferences?: NotificationPreferences;
  social_links?: SocialLinks;
}

// Enum types for type safety
export type UserRole = "creator" | "project_owner" | "admin" | "moderator";

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  in_app: boolean;
}

export interface SocialLinks {
  twitter?: string;
  instagram?: string;
  tiktok?: string;
  linkedin?: string;
  website?: string;
  [key: string]: string | undefined;
}

export interface ExternalAccounts {
  google?: {
    id: string;
    email: string;
    verified: boolean;
  };
  github?: {
    id: string;
    username: string;
    email?: string;
  };
  [provider: string]:
    | {
        id: string;
        email?: string;
        username?: string;
        verified?: boolean;
        [key: string]: unknown;
      }
    | undefined;
}
