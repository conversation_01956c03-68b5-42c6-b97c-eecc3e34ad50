// 工具数据库操作工具函数

import postgres from 'postgres';
import type { 
  Tool, 
  NewTool, 
  UserTool, 
  NewUserTool,
  ToolWithUserData,
  GetToolsRequest,
  GetToolsResponse,
  GetUserToolsRequest,
  GetUserToolsResponse,
  ToolCategory,
  Platform,
  ToolStats,
  UserToolStats
} from '@/types/tools';

// 数据库连接
const client = postgres(process.env.DATABASE_URL!);

// 获取工具列表（支持筛选和分页）
export async function getTools(
  request: GetToolsRequest,
  userId?: string
): Promise<GetToolsResponse> {
  try {
    // 构建基础查询
    let query = `
      SELECT 
        t.id, t.name, t.description, t.icon_url, t.website_url, 
        t.category, t.platform, t.is_free, t.created_at, t.updated_at
    `;
    
    // 如果提供了用户ID，则联查用户工具数据
    if (userId) {
      query += `,
        ut.is_favorite, ut.use_count, ut.last_used_at
      FROM tools t
      LEFT JOIN user_tools ut ON t.id = ut.tool_id AND ut.user_id = $1
      `;
    } else {
      query += `
      FROM tools t
      `;
    }
    
    const params: any[] = userId ? [userId] : [];
    let whereConditions: string[] = [];
    
    // 添加筛选条件
    if (request.category) {
      whereConditions.push(`t.category = $${params.length + 1}`);
      params.push(request.category);
    }
    
    if (request.platform) {
      whereConditions.push(`$${params.length + 1} = ANY(t.platform)`);
      params.push(request.platform);
    }
    
    if (request.is_free !== undefined) {
      whereConditions.push(`t.is_free = $${params.length + 1}`);
      params.push(request.is_free);
    }
    
    if (request.search) {
      whereConditions.push(`(t.name ILIKE $${params.length + 1} OR t.description ILIKE $${params.length + 1})`);
      params.push(`%${request.search}%`);
    }
    
    // 添加WHERE子句
    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }
    
    // 添加排序
    query += ` ORDER BY t.created_at DESC`;
    
    // 添加分页
    const limit = request.limit || 20;
    const offset = request.offset || 0;
    
    query += ` LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit + 1, offset); // 多查询一条来判断是否还有更多数据
    
    const result = await client.unsafe(query, params);
    
    // 处理结果
    const hasMore = result.length > limit;
    const tools = result.slice(0, limit);
    
    const toolsWithUserData: ToolWithUserData[] = tools.map((row: any) => ({
      id: row.id,
      name: row.name,
      description: row.description,
      icon_url: row.icon_url,
      website_url: row.website_url,
      category: row.category,
      platform: row.platform,
      is_free: row.is_free,
      created_at: row.created_at,
      updated_at: row.updated_at,
      user_data: userId && row.is_favorite !== null ? {
        is_favorite: row.is_favorite,
        use_count: row.use_count,
        last_used_at: row.last_used_at
      } : null
    }));
    
    return {
      tools: toolsWithUserData,
      total: tools.length, // 这里简化处理，实际应该查询总数
      has_more: hasMore
    };
    
  } catch (error) {
    console.error('Error in getTools:', error);
    throw new Error('获取工具列表失败');
  }
}

// 根据ID获取单个工具
export async function getToolById(toolId: string, userId?: string): Promise<ToolWithUserData | null> {
  try {
    let query = `
      SELECT 
        t.id, t.name, t.description, t.icon_url, t.website_url, 
        t.category, t.platform, t.is_free, t.created_at, t.updated_at
    `;
    
    if (userId) {
      query += `,
        ut.is_favorite, ut.use_count, ut.last_used_at
      FROM tools t
      LEFT JOIN user_tools ut ON t.id = ut.tool_id AND ut.user_id = $2
      WHERE t.id = $1
      `;
    } else {
      query += `
      FROM tools t
      WHERE t.id = $1
      `;
    }
    
    const params = userId ? [toolId, userId] : [toolId];
    const result = await client.unsafe(query, params);
    
    if (result.length === 0) {
      return null;
    }
    
    const row = result[0];
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      icon_url: row.icon_url,
      website_url: row.website_url,
      category: row.category,
      platform: row.platform,
      is_free: row.is_free,
      created_at: row.created_at,
      updated_at: row.updated_at,
      user_data: userId && row.is_favorite !== null ? {
        is_favorite: row.is_favorite,
        use_count: row.use_count,
        last_used_at: row.last_used_at
      } : null
    };
    
  } catch (error) {
    console.error('Error in getToolById:', error);
    throw new Error('获取工具详情失败');
  }
}

// 创建新工具（仅管理员）
export async function createTool(toolData: NewTool): Promise<Tool> {
  try {
    const query = `
      INSERT INTO tools (name, description, icon_url, website_url, category, platform, is_free)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, name, description, icon_url, website_url, category, platform, is_free, created_at, updated_at
    `;
    
    const params = [
      toolData.name,
      toolData.description || null,
      toolData.icon_url || null,
      toolData.website_url || null,
      toolData.category,
      toolData.platform,
      toolData.is_free ?? true
    ];
    
    const result = await client.unsafe(query, params);
    return result[0];
    
  } catch (error) {
    console.error('Error in createTool:', error);
    if (error instanceof Error && error.message.includes('duplicate')) {
      throw new Error('工具名称已存在');
    }
    throw new Error('创建工具失败');
  }
}

// 更新工具信息（仅管理员）
export async function updateTool(toolId: string, updates: Partial<NewTool>): Promise<Tool | null> {
  try {
    const setClause: string[] = [];
    const params: any[] = [];
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        setClause.push(`${key} = $${params.length + 1}`);
        params.push(value);
      }
    });
    
    if (setClause.length === 0) {
      throw new Error('没有提供更新数据');
    }
    
    const query = `
      UPDATE tools 
      SET ${setClause.join(', ')}
      WHERE id = $${params.length + 1}
      RETURNING id, name, description, icon_url, website_url, category, platform, is_free, created_at, updated_at
    `;
    
    params.push(toolId);
    const result = await client.unsafe(query, params);
    
    return result.length > 0 ? result[0] : null;
    
  } catch (error) {
    console.error('Error in updateTool:', error);
    throw new Error('更新工具失败');
  }
}

// 删除工具（仅管理员）
export async function deleteTool(toolId: string): Promise<boolean> {
  try {
    const query = `DELETE FROM tools WHERE id = $1`;
    const result = await client.unsafe(query, [toolId]);
    return result.count > 0;

  } catch (error) {
    console.error('Error in deleteTool:', error);
    throw new Error('删除工具失败');
  }
}

// ===== 用户工具交互相关函数 =====

// 切换工具收藏状态
export async function toggleToolFavorite(userId: string, toolId: string): Promise<UserTool> {
  try {
    // 首先检查用户工具记录是否存在
    const existingQuery = `
      SELECT id, is_favorite, use_count, last_used_at, created_at, updated_at
      FROM user_tools
      WHERE user_id = $1 AND tool_id = $2
    `;

    const existing = await client.unsafe(existingQuery, [userId, toolId]);

    if (existing.length > 0) {
      // 记录存在，切换收藏状态
      const updateQuery = `
        UPDATE user_tools
        SET is_favorite = NOT is_favorite, updated_at = NOW()
        WHERE user_id = $1 AND tool_id = $2
        RETURNING id, user_id, tool_id, is_favorite, use_count, last_used_at, created_at, updated_at
      `;

      const result = await client.unsafe(updateQuery, [userId, toolId]);
      return result[0];
    } else {
      // 记录不存在，创建新记录并设为收藏
      const insertQuery = `
        INSERT INTO user_tools (user_id, tool_id, is_favorite, use_count)
        VALUES ($1, $2, true, 0)
        RETURNING id, user_id, tool_id, is_favorite, use_count, last_used_at, created_at, updated_at
      `;

      const result = await client.unsafe(insertQuery, [userId, toolId]);
      return result[0];
    }

  } catch (error) {
    console.error('Error in toggleToolFavorite:', error);
    throw new Error('切换收藏状态失败');
  }
}

// 记录工具使用
export async function recordToolUsage(userId: string, toolId: string): Promise<UserTool> {
  try {
    // 使用 UPSERT 操作
    const query = `
      INSERT INTO user_tools (user_id, tool_id, use_count, last_used_at)
      VALUES ($1, $2, 1, NOW())
      ON CONFLICT (user_id, tool_id)
      DO UPDATE SET
        use_count = user_tools.use_count + 1,
        last_used_at = NOW(),
        updated_at = NOW()
      RETURNING id, user_id, tool_id, is_favorite, use_count, last_used_at, created_at, updated_at
    `;

    const result = await client.unsafe(query, [userId, toolId]);
    return result[0];

  } catch (error) {
    console.error('Error in recordToolUsage:', error);
    throw new Error('记录工具使用失败');
  }
}

// 获取用户的工具列表（收藏、最近使用等）
export async function getUserTools(
  userId: string,
  request: GetUserToolsRequest
): Promise<GetUserToolsResponse> {
  try {
    let query = `
      SELECT
        t.id, t.name, t.description, t.icon_url, t.website_url,
        t.category, t.platform, t.is_free, t.created_at as tool_created_at, t.updated_at as tool_updated_at,
        ut.is_favorite, ut.use_count, ut.last_used_at
      FROM user_tools ut
      JOIN tools t ON ut.tool_id = t.id
      WHERE ut.user_id = $1
    `;

    const params: any[] = [userId];
    let whereConditions: string[] = [];

    // 添加筛选条件
    if (request.favorites_only) {
      whereConditions.push('ut.is_favorite = true');
    }

    if (request.category) {
      whereConditions.push(`t.category = $${params.length + 1}`);
      params.push(request.category);
    }

    // 添加额外的WHERE条件
    if (whereConditions.length > 0) {
      query += ` AND ${whereConditions.join(' AND ')}`;
    }

    // 添加排序（最近使用的在前）
    query += ` ORDER BY ut.last_used_at DESC`;

    // 添加分页
    const limit = request.limit || 20;
    const offset = request.offset || 0;

    query += ` LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit + 1, offset);

    const result = await client.unsafe(query, params);

    // 处理结果
    const hasMore = result.length > limit;
    const tools = result.slice(0, limit);

    const toolsWithUserData: ToolWithUserData[] = tools.map((row: any) => ({
      id: row.id,
      name: row.name,
      description: row.description,
      icon_url: row.icon_url,
      website_url: row.website_url,
      category: row.category,
      platform: row.platform,
      is_free: row.is_free,
      created_at: row.tool_created_at,
      updated_at: row.tool_updated_at,
      user_data: {
        is_favorite: row.is_favorite,
        use_count: row.use_count,
        last_used_at: row.last_used_at
      }
    }));

    return {
      tools: toolsWithUserData,
      total: tools.length,
      has_more: hasMore
    };

  } catch (error) {
    console.error('Error in getUserTools:', error);
    throw new Error('获取用户工具列表失败');
  }
}

// 获取工具统计信息
export async function getToolStats(): Promise<ToolStats> {
  try {
    const query = `
      SELECT
        COUNT(*) as total_tools,
        COUNT(*) FILTER (WHERE is_free = true) as free_tools_count,
        COUNT(*) FILTER (WHERE is_free = false) as paid_tools_count,
        category,
        COUNT(*) as category_count
      FROM tools
      GROUP BY ROLLUP(category)
    `;

    const result = await client.unsafe(query);

    // 处理结果
    const stats: ToolStats = {
      total_tools: 0,
      tools_by_category: {} as Record<ToolCategory, number>,
      tools_by_platform: {} as Record<Platform, number>,
      free_tools_count: 0,
      paid_tools_count: 0
    };

    result.forEach((row: any) => {
      if (row.category === null) {
        // 总计行
        stats.total_tools = parseInt(row.total_tools);
        stats.free_tools_count = parseInt(row.free_tools_count);
        stats.paid_tools_count = parseInt(row.paid_tools_count);
      } else {
        // 分类统计
        stats.tools_by_category[row.category as ToolCategory] = parseInt(row.category_count);
      }
    });

    return stats;

  } catch (error) {
    console.error('Error in getToolStats:', error);
    throw new Error('获取工具统计失败');
  }
}

// 获取用户工具统计信息
export async function getUserToolStats(userId: string): Promise<UserToolStats> {
  try {
    const query = `
      SELECT
        COUNT(*) FILTER (WHERE is_favorite = true) as total_favorites,
        COUNT(*) as total_tools_used,
        SUM(use_count) as total_usage_count
      FROM user_tools
      WHERE user_id = $1
    `;

    const result = await client.unsafe(query, [userId]);
    const row = result[0];

    // 获取最常用的分类
    const categoryQuery = `
      SELECT t.category, COUNT(*) as count
      FROM user_tools ut
      JOIN tools t ON ut.tool_id = t.id
      WHERE ut.user_id = $1 AND ut.use_count > 0
      GROUP BY t.category
      ORDER BY count DESC
      LIMIT 1
    `;

    const categoryResult = await client.unsafe(categoryQuery, [userId]);
    const mostUsedCategory = categoryResult.length > 0 ? categoryResult[0].category : null;

    return {
      total_favorites: parseInt(row.total_favorites) || 0,
      total_tools_used: parseInt(row.total_tools_used) || 0,
      most_used_category: mostUsedCategory,
      favorite_tools: [], // 这里可以进一步查询具体的收藏工具
      recently_used_tools: [] // 这里可以进一步查询最近使用的工具
    };

  } catch (error) {
    console.error('Error in getUserToolStats:', error);
    throw new Error('获取用户工具统计失败');
  }
}
