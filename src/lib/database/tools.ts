// 简化的工具数据库操作函数 - MVP版本

import postgres from 'postgres';
import type {
  Tool,
  ToolWithUserData,
  GetToolsRequest,
  GetToolsResponse,
  GetUserToolsResponse,
  ToolCategory
} from '@/types/tools';

// 数据库连接
const client = postgres(process.env.DATABASE_URL!);

// 获取所有工具列表（简化版）
export async function getTools(
  request: GetToolsRequest = {},
  userId?: string
): Promise<GetToolsResponse> {
  try {
    let query = `
      SELECT
        t.id, t.name, t.description, t.icon_url, t.website_url,
        t.category, t.platform, t.is_free, t.created_at, t.updated_at
    `;

    // 如果提供了用户ID，则联查用户选择数据
    if (userId) {
      query += `,
        CASE WHEN ut.is_selected IS NOT NULL THEN ut.is_selected ELSE false END as is_selected
      FROM tools t
      LEFT JOIN user_tools ut ON t.id = ut.tool_id AND ut.user_id = $1
      `;
    } else {
      query += `,
        false as is_selected
      FROM tools t
      `;
    }

    const params: any[] = userId ? [userId] : [];

    // 添加分类筛选
    if (request.category) {
      query += ` WHERE t.category = $${params.length + 1}`;
      params.push(request.category);
    }

    // 添加排序
    query += ` ORDER BY t.category, t.name`;

    const result = await client.unsafe(query, params);

    const toolsWithUserData: ToolWithUserData[] = result.map((row: any) => ({
      id: row.id,
      name: row.name,
      description: row.description,
      icon_url: row.icon_url,
      website_url: row.website_url,
      category: row.category,
      platform: row.platform,
      is_free: row.is_free,
      created_at: row.created_at,
      updated_at: row.updated_at,
      is_selected: row.is_selected
    }));

    return {
      tools: toolsWithUserData
    };

  } catch (error) {
    console.error('Error in getTools:', error);
    throw new Error('获取工具列表失败');
  }
}

// 切换工具选择状态（简化版）
export async function toggleToolSelection(userId: string, toolId: string): Promise<boolean> {
  try {
    // 使用 UPSERT 操作切换选择状态
    const query = `
      INSERT INTO user_tools (user_id, tool_id, is_selected)
      VALUES ($1, $2, true)
      ON CONFLICT (user_id, tool_id)
      DO UPDATE SET
        is_selected = NOT user_tools.is_selected,
        updated_at = NOW()
      RETURNING is_selected
    `;

    const result = await client.unsafe(query, [userId, toolId]);
    return result[0].is_selected;

  } catch (error) {
    console.error('Error in toggleToolSelection:', error);
    throw new Error('切换工具选择状态失败');
  }
}

// 获取用户选择的工具列表
export async function getUserSelectedTools(userId: string): Promise<GetUserToolsResponse> {
  try {
    const query = `
      SELECT
        t.id, t.name, t.description, t.icon_url, t.website_url,
        t.category, t.platform, t.is_free, t.created_at, t.updated_at,
        true as is_selected
      FROM user_tools ut
      JOIN tools t ON ut.tool_id = t.id
      WHERE ut.user_id = $1 AND ut.is_selected = true
      ORDER BY t.category, t.name
    `;

    const result = await client.unsafe(query, [userId]);

    const tools: ToolWithUserData[] = result.map((row: any) => ({
      id: row.id,
      name: row.name,
      description: row.description,
      icon_url: row.icon_url,
      website_url: row.website_url,
      category: row.category,
      platform: row.platform,
      is_free: row.is_free,
      created_at: row.created_at,
      updated_at: row.updated_at,
      is_selected: row.is_selected
    }));

    return { tools };

  } catch (error) {
    console.error('Error in getUserSelectedTools:', error);
    throw new Error('获取用户工具列表失败');
  }
}


