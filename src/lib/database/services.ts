// 服务数据库操作工具函数

import postgres from 'postgres';
import type { 
  Service, 
  CreateServiceRequest, 
  UpdateServiceRequest,
  MyServicesResponse,
  PublicServicesResponse,
  ServiceType,
  ServiceStatus
} from '@/types/services';

// 数据库连接
const client = postgres(process.env.DATABASE_URL!);

// 获取创作者的服务列表（按类型分组）
export async function getCreatorServices(
  creatorId: string,
  type?: ServiceType,
  status?: ServiceStatus
): Promise<MyServicesResponse> {
  try {
    // 构建查询条件
    let query = `
      SELECT id, type, title, description, price_min, price_max, status, created_at, updated_at
      FROM services 
      WHERE creator_id = $1
    `;
    const params: any[] = [creatorId];
    
    if (type) {
      query += ` AND type = $${params.length + 1}`;
      params.push(type);
    }
    
    if (status) {
      query += ` AND status = $${params.length + 1}`;
      params.push(status);
    }
    
    query += ` ORDER BY type, created_at DESC`;
    
    const result = await client.unsafe(query, params);
    
    // 按类型分组
    const services: MyServicesResponse = {
      embedded: [],
      custom: []
    };
    
    result.forEach((row: any) => {
      const service: Service = {
        id: row.id,
        type: row.type as ServiceType,
        title: row.title,
        description: row.description,
        priceMin: parseFloat(row.price_min),
        priceMax: parseFloat(row.price_max),
        status: row.status as ServiceStatus,
        createdAt: row.created_at.toISOString(),
        updatedAt: row.updated_at.toISOString()
      };
      
      if (service.type === 'embedded') {
        services.embedded.push(service);
      } else {
        services.custom.push(service);
      }
    });
    
    return services;
  } catch (error) {
    console.error('Error fetching creator services:', error);
    throw new Error('Failed to fetch creator services');
  }
}

// 创建新服务
export async function createService(
  creatorId: string,
  serviceData: CreateServiceRequest
): Promise<Service> {
  try {
    const query = `
      INSERT INTO services (creator_id, type, title, description, price_min, price_max)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, type, title, description, price_min, price_max, status, created_at, updated_at
    `;
    
    const result = await client.unsafe(query, [
      creatorId,
      serviceData.type,
      serviceData.title,
      serviceData.description,
      serviceData.priceMin,
      serviceData.priceMax
    ]);
    
    if (result.length === 0) {
      throw new Error('Failed to create service');
    }
    
    const row = result[0];
    return {
      id: row.id,
      type: row.type as ServiceType,
      title: row.title,
      description: row.description,
      priceMin: parseFloat(row.price_min),
      priceMax: parseFloat(row.price_max),
      status: row.status as ServiceStatus,
      createdAt: row.created_at.toISOString(),
      updatedAt: row.updated_at.toISOString()
    };
  } catch (error) {
    console.error('Error creating service:', error);
    throw new Error('Failed to create service');
  }
}

// 更新服务
export async function updateService(
  serviceId: string,
  creatorId: string,
  updateData: UpdateServiceRequest
): Promise<Service> {
  try {
    // 构建动态更新查询
    const updateFields: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;
    
    if (updateData.title !== undefined) {
      updateFields.push(`title = $${paramIndex}`);
      params.push(updateData.title);
      paramIndex++;
    }
    
    if (updateData.description !== undefined) {
      updateFields.push(`description = $${paramIndex}`);
      params.push(updateData.description);
      paramIndex++;
    }
    
    if (updateData.priceMin !== undefined) {
      updateFields.push(`price_min = $${paramIndex}`);
      params.push(updateData.priceMin);
      paramIndex++;
    }
    
    if (updateData.priceMax !== undefined) {
      updateFields.push(`price_max = $${paramIndex}`);
      params.push(updateData.priceMax);
      paramIndex++;
    }
    
    if (updateFields.length === 0) {
      throw new Error('No fields to update');
    }
    
    // 添加 updated_at
    updateFields.push(`updated_at = NOW()`);
    
    // 添加 WHERE 条件参数
    params.push(serviceId, creatorId);
    
    const query = `
      UPDATE services 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex} AND creator_id = $${paramIndex + 1}
      RETURNING id, type, title, description, price_min, price_max, status, created_at, updated_at
    `;
    
    const result = await client.unsafe(query, params);
    
    if (result.length === 0) {
      throw new Error('Service not found or unauthorized');
    }
    
    const row = result[0];
    return {
      id: row.id,
      type: row.type as ServiceType,
      title: row.title,
      description: row.description,
      priceMin: parseFloat(row.price_min),
      priceMax: parseFloat(row.price_max),
      status: row.status as ServiceStatus,
      createdAt: row.created_at.toISOString(),
      updatedAt: row.updated_at.toISOString()
    };
  } catch (error) {
    console.error('Error updating service:', error);
    throw new Error('Failed to update service');
  }
}

// 更新服务状态
export async function updateServiceStatus(
  serviceId: string,
  creatorId: string,
  status: ServiceStatus
): Promise<{ id: string; status: ServiceStatus; updatedAt: string }> {
  try {
    const query = `
      UPDATE services 
      SET status = $1, updated_at = NOW()
      WHERE id = $2 AND creator_id = $3
      RETURNING id, status, updated_at
    `;
    
    const result = await client.unsafe(query, [status, serviceId, creatorId]);
    
    if (result.length === 0) {
      throw new Error('Service not found or unauthorized');
    }
    
    const row = result[0];
    return {
      id: row.id,
      status: row.status as ServiceStatus,
      updatedAt: row.updated_at.toISOString()
    };
  } catch (error) {
    console.error('Error updating service status:', error);
    throw new Error('Failed to update service status');
  }
}

// 删除服务
export async function deleteService(
  serviceId: string,
  creatorId: string
): Promise<void> {
  try {
    const query = `
      DELETE FROM services 
      WHERE id = $1 AND creator_id = $2
    `;
    
    const result = await client.unsafe(query, [serviceId, creatorId]);
    
    if (result.count === 0) {
      throw new Error('Service not found or unauthorized');
    }
  } catch (error) {
    console.error('Error deleting service:', error);
    throw new Error('Failed to delete service');
  }
}

// 获取创作者公开服务（用于前台展示）
export async function getPublicCreatorServices(
  creatorId: string,
  type?: ServiceType
): Promise<PublicServicesResponse> {
  try {
    // 获取创作者信息
    const creatorQuery = `
      SELECT clerk_user_id, full_name, avatar_url
      FROM profiles 
      WHERE clerk_user_id = $1
    `;
    
    const creatorResult = await client.unsafe(creatorQuery, [creatorId]);
    
    if (creatorResult.length === 0) {
      throw new Error('Creator not found');
    }
    
    // 获取服务信息
    let servicesQuery = `
      SELECT id, type, title, description, price_min, price_max
      FROM services 
      WHERE creator_id = $1 AND status = 'active'
    `;
    const params: any[] = [creatorId];
    
    if (type) {
      servicesQuery += ` AND type = $2`;
      params.push(type);
    }
    
    servicesQuery += ` ORDER BY type, created_at DESC`;
    
    const servicesResult = await client.unsafe(servicesQuery, params);
    
    // 组装响应数据
    const creator = creatorResult[0];
    const services: PublicServicesResponse['services'] = {
      embedded: [],
      custom: []
    };
    
    servicesResult.forEach((row: any) => {
      const service = {
        id: row.id,
        type: row.type as ServiceType,
        title: row.title,
        description: row.description,
        priceMin: parseFloat(row.price_min),
        priceMax: parseFloat(row.price_max)
      };
      
      if (row.type === 'embedded') {
        services.embedded.push(service);
      } else {
        services.custom.push(service);
      }
    });
    
    return {
      creatorInfo: {
        id: creator.clerk_user_id,
        fullName: creator.full_name || 'Unknown Creator',
        avatarUrl: creator.avatar_url || ''
      },
      services
    };
  } catch (error) {
    console.error('Error fetching public creator services:', error);
    throw new Error('Failed to fetch public creator services');
  }
} 