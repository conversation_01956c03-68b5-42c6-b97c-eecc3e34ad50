// 服务模板常量数据

export interface ServiceTemplate {
  title: string;
  description: string;
  priceMin: number;
  priceMax: number;
}

export interface ServiceTemplates {
  embedded: ServiceTemplate[];
  custom: ServiceTemplate[];
}

// 预定义的服务模板数据
export const SERVICE_TEMPLATES: ServiceTemplates = {
  embedded: [
    {
      title: "短视频口播",
      description: "60-90秒品牌宣传视频，包含品牌口播和Logo展示",
      priceMin: 300,
      priceMax: 600
    },
    {
      title: "社交图文",
      description: "Twitter/小红书图文推广，专业内容创作",
      priceMin: 200,
      priceMax: 400
    }
  ],
  custom: [
    {
      title: "定制视频",
      description: "脚本+拍摄+剪辑完整制作，专业视频内容",
      priceMin: 1000,
      priceMax: 2500
    },
    {
      title: "品牌合作",
      description: "深度品牌内容合作，长期合作关系",
      priceMin: 1500,
      priceMax: 3000
    }
  ]
};

// 获取所有模板
export const getAllTemplates = (): ServiceTemplates => SERVICE_TEMPLATES;

// 根据类型获取模板
export const getTemplatesByType = (type: 'embedded' | 'custom'): ServiceTemplate[] => {
  return SERVICE_TEMPLATES[type];
};

// 根据标题查找模板
export const findTemplateByTitle = (title: string): ServiceTemplate | undefined => {
  const allTemplates = [...SERVICE_TEMPLATES.embedded, ...SERVICE_TEMPLATES.custom];
  return allTemplates.find(template => template.title === title);
}; 