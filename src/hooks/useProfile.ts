import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";

// Update interface to match our new schema
export interface UserProfile {
  clerk_user_id: string;
  email: string;
  clerk_username?: string | null;
  full_name: string | null;
  avatar_url: string | null;
  role: string;
  experience_points: number;
  level: number;
  wallet_address: string | null;
  created_at: Date;
  updated_at: Date;
  is_premium_member: boolean;
  youtube_id: string | null;
  youtube_title: string | null;
  subscribers: number;
  views: number;
  motto: string | null;
  user_numeric_id: number | null;
  profile_completion_percentage: number;
  last_login_at?: Date | null;
  preferred_language: string;
  email_verified: boolean;
}

// 工具函数：计算等级
const calculateLevel = (experience: number): number => {
  return Math.floor(experience / 100) + 1;
};

// 工具函数：检查是否可以获得经验
const canGainExperience = (lastLoginAt: Date | null): boolean => {
  if (!lastLoginAt) return true; // 首次登录

  const now = new Date();
  const lastLogin = new Date(lastLoginAt);
  const hoursDiff = (now.getTime() - lastLogin.getTime()) / (1000 * 60 * 60);
  
  return hoursDiff >= 12; // 12小时后可以再次获得经验
};

export function useProfile() {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProfile = async () => {
      if (!user?.id) {
        setProfile(null);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Make API call instead of direct database access
        const response = await fetch(`/api/profile/${user.id}`);

        if (response.ok) {
          const profileData = await response.json();
          setProfile(profileData);
          
          // 检查并处理经验增长
          await checkAndGainExperience(profileData);
        } else if (response.status === 404) {
          // Profile doesn't exist yet
          setProfile(null);
        } else {
          throw new Error(`Failed to load profile: ${response.statusText}`);
        }
      } catch (err) {
        console.error("Error loading profile:", err);
        setError("Failed to load profile");
        setProfile(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, [user?.id]);

  // 检查并增加经验的函数
  const checkAndGainExperience = async (profileData: UserProfile) => {
    if (!user?.id || !canGainExperience(profileData.last_login_at || null)) {
      return;
    }

    try {
      const expGain = 10; // 每次登录获得10经验
      const newExperience = profileData.experience_points + expGain;
      const newLevel = calculateLevel(newExperience);

      // 调用API更新经验和等级
      const response = await fetch(`/api/profile/${user.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          experience_points: newExperience,
          level: newLevel,
          last_login_at: new Date(),
        }),
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setProfile(updatedProfile);
        
        // 可选：显示获得经验的通知
        console.log(`获得了 ${expGain} 经验！当前等级: ${newLevel}`);
      }
    } catch (err) {
      console.error("Error gaining experience:", err);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!profile || !user?.id) {
      throw new Error("No profile or user available");
    }

    try {
      setError(null);

      const response = await fetch(`/api/profile/${user.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error(`Failed to update profile: ${response.statusText}`);
      }

      const updatedProfile = await response.json();
      setProfile(updatedProfile);
    } catch (err) {
      console.error("Error updating profile:", err);
      setError("Failed to update profile");
      throw err;
    }
  };

  return {
    profile,
    isLoading,
    error,
    updateProfile,
    // 暴露经验相关的工具函数
    canGainExperience: profile ? canGainExperience(profile.last_login_at || null) : false,
    nextExperienceIn: profile && profile.last_login_at ? 
      Math.max(0, 12 - (new Date().getTime() - new Date(profile.last_login_at).getTime()) / (1000 * 60 * 60)) : 0,
  };
}
