import { useAuth as useClerkAuth, useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useMemo } from "react";

export function useAuth() {
  const { isSignedIn, isLoaded, signOut } = useClerkAuth();
  const { user } = useUser();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push("/auth");
    } catch (error) {
      console.error("Sign out error:", error);
      throw error;
    }
  };

  const memoizedUser = useMemo(() => {
    return user
      ? {
          id: user.id,
          email: user.emailAddresses[0]?.emailAddress || "",
          name: user.fullName || user.firstName || "",
          image: user.imageUrl || "",
        }
      : null;
  }, [user]);

  return {
    user: memoizedUser,
    isLoading: !isLoaded,
    isAuthenticated: isSignedIn || false,
    signOut: handleSignOut,
    // For backward compatibility
    session: user ? { user } : null,
  };
}
