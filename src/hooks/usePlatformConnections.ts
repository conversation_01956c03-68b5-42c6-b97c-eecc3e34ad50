"use client";

import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

/**
 * Platform Connections Store using Zustand
 *
 * This store manages all platform connections (YouTube, MetaMask, Twitter, etc.)
 * and integrates with ProfileService to persist connection data to the backend.
 *
 * Key Features:
 * - Centralized state management for all platform connections
 * - Automatic profile synchronization via ProfileService
 * - Persistent storage across browser sessions
 * - Error handling and loading states
 * - Real-time connection status updates
 *
 * Integration with ProfileService:
 * - connectYouTube: Updates profile with YouTube channel data
 * - connectMetaMask: Updates profile with wallet address
 * - disconnectPlatform: Removes platform data from profile
 * - loadUserConnections: Syncs connections with existing profile data
 */

// Types
export interface PlatformConnection {
  id: string;
  platform: string;
  status: "未绑定" | "已绑定" | "已认证" | "连接中" | "错误";
  handle?: string;
  address?: string;
  iconType: string;
  required: boolean;
  metadata?: Record<string, unknown>;
  lastConnected?: Date;
  error?: string;
  isLoading?: boolean;
}

export interface ConnectionsData {
  [key: string]: PlatformConnection;
}

// Profile data interface
interface ProfileData {
  youtube_id?: string | null;
  youtube_title?: string | null;
  subscribers?: number;
  views?: number;
  wallet_address?: string | null;
  full_name?: string | null;
  avatar_url?: string | null;
  motto?: string | null;
  social_links?: Record<string, string>;
  [key: string]: unknown;
}

// Platform configurations
export const PLATFORM_CONFIGS = {
  MetaMask: {
    name: "MetaMask",
    iconType: "Wallet",
    required: true,
    description: "连接你的Web3钱包",
    category: "wallet",
  },
  YouTube: {
    name: "YouTube",
    iconType: "Youtube",
    required: true,
    description: "连接你的YouTube频道",
    category: "social",
  },
  Twitter: {
    name: "Twitter",
    iconType: "Twitter",
    required: true,
    description: "连接你的Twitter账号",
    category: "social",
  },
} as const;

// Store interface
interface PlatformConnectionsStore {
  // State
  connections: ConnectionsData;
  isInitialized: boolean;
  globalLoading: boolean;
  lastSyncTime: Date | null;

  // Actions
  initializeConnections: (userId?: string) => Promise<void>;
  loadUserConnections: (userId: string) => Promise<void>;
  connectPlatform: (platformId: string, userId: string) => Promise<void>;
  disconnectPlatform: (platformId: string, userId: string) => Promise<void>;
  updateConnection: (
    platformId: string,
    updates: Partial<PlatformConnection>
  ) => void;
  setConnectionStatus: (
    platformId: string,
    status: PlatformConnection["status"]
  ) => void;
  setConnectionError: (platformId: string, error: string | null) => void;
  setConnectionLoading: (platformId: string, loading: boolean) => void;
  syncWithProfile: (profileData: ProfileData) => void;
  updateProfileSafely: (
    userId: string,
    updates: Partial<ProfileData>
  ) => Promise<boolean>;
  reset: () => void;

  // Platform-specific connection methods
  connectYouTube: (userId: string) => Promise<void>;
  connectMetaMask: (userId: string) => Promise<void>;
  connectTwitter: (userId: string) => Promise<void>;
  refreshYouTubeData: (userId: string) => Promise<void>;
  handleOAuthSuccess: (platformId: string, userId: string) => Promise<void>;

  // Selectors
  getConnection: (platformId: string) => PlatformConnection | undefined;
  getConnectionsByStatus: (
    status: PlatformConnection["status"]
  ) => PlatformConnection[];
  getCompletionPercentage: () => number;
  getRequiredConnections: () => PlatformConnection[];
  getConnectedRequiredCount: () => number;
}

// Create the store
export const usePlatformConnections = create<PlatformConnectionsStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        connections: {},
        isInitialized: false,
        globalLoading: false,
        lastSyncTime: null,

        // Initialize connections
        initializeConnections: async (userId?: string) => {
          const state = get();
          if (state.isInitialized) return;

          set({ globalLoading: true });

          try {
            const initialConnections: ConnectionsData = {};

            // Initialize from platform configs
            Object.entries(PLATFORM_CONFIGS).forEach(([key, config]) => {
              initialConnections[key] = {
                id: key,
                platform: config.name,
                status: "未绑定",
                iconType: config.iconType,
                required: config.required,
                metadata: {},
                isLoading: false,
              };
            });

            set({
              connections: initialConnections,
              isInitialized: true,
              globalLoading: false,
              lastSyncTime: new Date(),
            });

            // If we have a userId, try to load existing connections
            if (userId) {
              await get().loadUserConnections(userId);
            }
          } catch (error) {
            console.error("Error initializing connections:", error);
            set({ globalLoading: false });
          }
        },

        // Load user connections from profile
        loadUserConnections: async (userId: string) => {
          try {
            // Load profile data to sync with connections via API
            let profileData: ProfileData | null = null;
            try {
              const response = await fetch(`/api/profile/${userId}`);
              if (response.ok) {
                profileData = await response.json();
              } else if (response.status !== 404) {
                console.warn(
                  "Failed to load profile data:",
                  response.statusText
                );
              }
            } catch (error) {
              console.warn("Failed to load profile data:", error);
            }

            // Check YouTube connection
            try {
              if (profileData?.youtube_id) {
                // Fallback: Use profile data if available but no OAuth connection
                get().updateConnection("YouTube", {
                  status: "已绑定",
                  handle: `@${
                    profileData.youtube_title || profileData.youtube_id
                  }`,
                  metadata: {
                    channelId: profileData.youtube_id,
                    title: profileData.youtube_title,
                    subscribers: profileData.subscribers || 0,
                    views: profileData.views || 0,
                  },
                  lastConnected: new Date(),
                });
              }
            } catch (error) {
              console.error("Error checking YouTube connection:", error);
            }

            // Check MetaMask connection (from localStorage or other sources)
            if (profileData?.wallet_address) {
              // If MetaMask not available but wallet address in profile
              get().updateConnection("MetaMask", {
                status: "已绑定",
                address: `${profileData.wallet_address.slice(
                  0,
                  6
                )}...${profileData.wallet_address.slice(-4)}`,
                metadata: { fullAddress: profileData.wallet_address },
                lastConnected: new Date(),
              });
            }

            set({ lastSyncTime: new Date() });
          } catch (error) {
            console.error("Error loading user connections:", error);
          }
        },

        // Connect to a platform
        connectPlatform: async (platformId: string, userId: string) => {
          const connection = get().connections[platformId];
          if (!connection || connection.status === "连接中") return;

          get().setConnectionStatus(platformId, "连接中");
          get().setConnectionError(platformId, null);

          try {
            switch (platformId) {
              case "YouTube":
                await get().connectYouTube(userId);
                break;
              case "MetaMask":
                await get().connectMetaMask(userId);
                break;
              case "Twitter":
                await get().connectTwitter(userId);
                break;
              default:
                throw new Error(`Platform ${platformId} not implemented yet`);
            }
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : "连接失败";
            get().setConnectionStatus(platformId, "错误");
            get().setConnectionError(platformId, errorMessage);
            throw error;
          }
        },

        // YouTube connection handler (simplified)
        connectYouTube: async (userId: string) => {
          try {
            // Generate simple OAuth URL for one-time verification
            const clientId = process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID;
            const redirectUri = `${window.location.origin}/api/youtube/verify-callback`;
            const scope = 'https://www.googleapis.com/auth/youtube.readonly';
            
            // Simple state for CSRF protection
            const state = `${userId}_${Date.now()}_${Math.random().toString(36).substring(7)}`;
            
            const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?${new URLSearchParams({
              client_id: clientId!,
              redirect_uri: redirectUri,
              response_type: 'code',
              scope,
              access_type: 'offline',
              prompt: 'consent',
              state,
            })}`;

            // Redirect to Google OAuth (one-time verification)
            window.location.href = authUrl;

          } catch (error) {
            console.error("Error connecting YouTube:", error);
            throw error;
          }
        },

        // MetaMask connection handler
        connectMetaMask: async (userId: string) => {
          if (typeof window === "undefined" || !window.ethereum) {
            throw new Error("请先安装MetaMask钱包");
          }

          const accounts = (await window.ethereum.request({
            method: "eth_requestAccounts",
          })) as string[];

          if (accounts.length > 0) {
            const address = accounts[0];

            // Update connection state
            get().updateConnection("MetaMask", {
              status: "已绑定",
              address: `${address.slice(0, 6)}...${address.slice(-4)}`,
              metadata: { fullAddress: address },
              lastConnected: new Date(),
              error: undefined,
            });

            // Update profile with wallet address
            const profileUpdateSuccess = await get().updateProfileSafely(
              userId,
              {
                wallet_address: address,
              }
            );

            if (!profileUpdateSuccess) {
              console.warn(
                "Failed to update profile with wallet address, but continuing with connection update"
              );
            }

            console.log(
              "MetaMask connected successfully with address:",
              address
            );
          }
        },

        // Twitter connection handler
        connectTwitter: async (userId: string) => {
          throw new Error("Twitter连接功能正在开发中");
        },

        // Refresh YouTube data using public API
        refreshYouTubeData: async (userId: string) => {
          try {
            const response = await fetch("/api/youtube/refresh-data", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
            });

            if (!response.ok) {
              throw new Error("Failed to refresh YouTube data");
            }

            const result = await response.json();

            if (result.success && result.channelData) {
              // Update connection with fresh data
              get().updateConnection("YouTube", {
                status: "已认证",
                handle: `@${result.channelData.title}`,
                metadata: {
                  channelId: result.channelData.channelId,
                  title: result.channelData.title,
                  subscribers: result.channelData.subscribers,
                  views: result.channelData.views,
                  videos: result.channelData.videos,
                  thumbnailUrl: result.channelData.thumbnailUrl,
                },
                lastConnected: new Date(result.lastUpdated),
                error: undefined,
              });
            }

          } catch (error) {
            console.error("Error refreshing YouTube data:", error);
            get().updateConnection("YouTube", {
              status: "错误",
              error: error instanceof Error ? error.message : "Failed to refresh data",
            });
          }
        },

        // Handle OAuth success (called after OAuth callback)
        handleOAuthSuccess: async (platformId: string, userId: string) => {
          try {
            switch (platformId) {
              case "YouTube":
                // Load fresh data after successful verification
                await get().loadUserConnections(userId);
                // Refresh YouTube data to get latest stats
                await get().refreshYouTubeData(userId);
                break;
              default:
                console.warn(`OAuth success handler not implemented for ${platformId}`);
            }
          } catch (error) {
            console.error(`Error handling OAuth success for ${platformId}:`, error);
          }
        },

        // Disconnect from a platform
        disconnectPlatform: async (platformId: string, userId: string) => {
          try {
            switch (platformId) {
              case "YouTube":
                // Clear YouTube data from profile (simple approach)
                await get().updateProfileSafely(userId, {
                  youtube_id: null,
                  youtube_title: null,
                  subscribers: 0,
                  views: 0,
                });
                break;
              case "MetaMask":
                // MetaMask doesn't have a programmatic disconnect, just clear our state
                await get().updateProfileSafely(userId, {
                  wallet_address: null,
                });
                break;
              case "Twitter":
                // TODO: Implement Twitter disconnect
                break;
            }

            get().updateConnection(platformId, {
              status: "未绑定",
              handle: undefined,
              address: undefined,
              metadata: {},
              error: undefined,
              lastConnected: undefined,
            });

            set({ lastSyncTime: new Date() });
            
            console.log(`${platformId} disconnected successfully`);
          } catch (error) {
            console.error("Error disconnecting platform:", error);
            throw error;
          }
        },

        // Update a specific connection
        updateConnection: (
          platformId: string,
          updates: Partial<PlatformConnection>
        ) => {
          set((state) => ({
            connections: {
              ...state.connections,
              [platformId]: {
                ...state.connections[platformId],
                ...updates,
              },
            },
          }));
        },

        // Set connection status
        setConnectionStatus: (
          platformId: string,
          status: PlatformConnection["status"]
        ) => {
          get().updateConnection(platformId, { status });
        },

        // Set connection error
        setConnectionError: (platformId: string, error: string | null) => {
          get().updateConnection(platformId, { error: error || undefined });
        },

        // Set connection loading state
        setConnectionLoading: (platformId: string, loading: boolean) => {
          get().updateConnection(platformId, { isLoading: loading });
        },

        // Sync with profile data
        syncWithProfile: (profileData: ProfileData) => {
          if (!profileData) return;

          const { youtube_id, youtube_title, subscribers, views } = profileData;

          if (youtube_id) {
            get().updateConnection("YouTube", {
              status: "已认证",
              handle: `@${youtube_title || youtube_id}`,
              metadata: {
                channelId: youtube_id,
                title: youtube_title,
                subscribers: subscribers || 0,
                views: views || 0,
              },
              lastConnected: new Date(),
            });
          }

          set({ lastSyncTime: new Date() });
        },

        // Reset store
        reset: () => {
          set({
            connections: {},
            isInitialized: false,
            globalLoading: false,
            lastSyncTime: null,
          });
        },

        // Selector: Get specific connection
        getConnection: (platformId: string) => {
          return get().connections[platformId];
        },

        // Selector: Get connections by status
        getConnectionsByStatus: (status: PlatformConnection["status"]) => {
          return Object.values(get().connections).filter(
            (conn) => conn.status === status
          );
        },

        // Selector: Get completion percentage
        getCompletionPercentage: () => {
          const connections = Object.values(get().connections);
          const connectedCount = connections.filter(
            (c) => c.status === "已绑定" || c.status === "已认证"
          ).length;
          return connections.length > 0
            ? Math.round((connectedCount / connections.length) * 100)
            : 0;
        },

        // Selector: Get required connections
        getRequiredConnections: () => {
          return Object.values(get().connections).filter(
            (conn) => conn.required
          );
        },

        // Selector: Get connected required count
        getConnectedRequiredCount: () => {
          const requiredConnections = get().getRequiredConnections();
          return requiredConnections.filter(
            (c) => c.status === "已绑定" || c.status === "已认证"
          ).length;
        },

        // New method: Update profile safely via API
        updateProfileSafely: async (
          userId: string,
          updates: Partial<ProfileData>
        ) => {
          try {
            const response = await fetch(`/api/profile/${userId}`, {
              method: "PATCH",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(updates),
            });

            if (!response.ok) {
              throw new Error(`Profile update failed: ${response.statusText}`);
            }

            const updatedProfile = await response.json();
            console.log("Profile updated successfully via API:", updates);
            return true;
          } catch (error) {
            console.error("Error updating profile via API:", error);
            return false;
          }
        },
      }),
      {
        name: "platform-connections-store",
        partialize: (state) => ({
          connections: state.connections,
          lastSyncTime: state.lastSyncTime,
        }),
      }
    ),
    {
      name: "platform-connections",
    }
  )
);

// Hook for easier access to common connection operations
export const useConnectionActions = () => {
  const store = usePlatformConnections();

  return {
    connectPlatform: store.connectPlatform,
    disconnectPlatform: store.disconnectPlatform,
    refreshYouTubeData: store.refreshYouTubeData,
    handleOAuthSuccess: store.handleOAuthSuccess,
    getConnection: store.getConnection,
    getCompletionPercentage: store.getCompletionPercentage,
    initializeConnections: store.initializeConnections,
    syncWithProfile: store.syncWithProfile,
  };
};

// Selector hooks for specific data
export const useConnections = () =>
  usePlatformConnections((state) => state.connections);
export const useConnectionStatus = (platformId: string) =>
  usePlatformConnections(
    (state) => state.connections[platformId]?.status || "未绑定"
  );
export const useIsConnectionLoading = (platformId: string) =>
  usePlatformConnections(
    (state) => state.connections[platformId]?.isLoading || false
  );
export const useConnectionError = (platformId: string) =>
  usePlatformConnections((state) => state.connections[platformId]?.error);
export const useCompletionPercentage = () =>
  usePlatformConnections((state) => state.getCompletionPercentage());
export const useIsInitialized = () =>
  usePlatformConnections((state) => state.isInitialized);
