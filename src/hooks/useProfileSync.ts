import { useEffect } from "react";
import { useAuth } from "@clerk/nextjs";
import { useProfileSyncStore } from "@/stores/profileSyncStore";
import type { UserProfile } from "@/hooks/useProfile";

interface ProfileSyncResult {
  success: boolean;
  message: string;
  profile?: UserProfile;
}

export const useProfileSync = () => {
  const { isSignedIn, userId } = useAuth();
  
  // 使用 Zustand store 管理状态
  const {
    hasSynced,
    currentUserId,
    isSyncing,
    syncError,
    syncResult,
    setHasSynced,
    setCurrentUserId,
    setIsSyncing,
    setSyncError,
    setSyncResult,
    resetSyncState,
    resetForNewUser,
  } = useProfileSyncStore();

  const syncProfile = async (): Promise<ProfileSyncResult> => {
    if (!isSignedIn || !userId) {
      throw new Error("User not authenticated");
    }

    setIsSyncing(true);
    setSyncError(null);

    try {
      const response = await fetch("/api/profile/sync", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Sync failed: ${errorText}`);
      }

      const result: ProfileSyncResult = await response.json();
      setSyncResult(result);
      setHasSynced(true);

      console.log("用户资料同步成功:", result.message);
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setSyncError(errorMessage);
      console.error("同步过程中发生错误:", error);
      throw error;
    } finally {
      setIsSyncing(false);
    }
  };

  // 监听用户切换，重置同步状态
  useEffect(() => {
    if (!isSignedIn || !userId) {
      // 用户登出时重置状态
      resetSyncState();
    } else if (userId !== currentUserId) {
      // 用户切换时重置状态
      resetForNewUser(userId);
    }
  }, [isSignedIn, userId, currentUserId, resetSyncState, resetForNewUser]);

  // 自动同步：当用户登录时，且尚未同步过
  useEffect(() => {
    if (isSignedIn && userId && !isSyncing && !hasSynced) {
      syncProfile().catch((error) => {
        console.error("自动同步失败:", error);
      });
    }
  }, [isSignedIn, userId, isSyncing, hasSynced]);

  const clearSync = () => {
    setSyncResult(null);
    setSyncError(null);
    setHasSynced(false);
  };

  return {
    isSyncing,
    syncError,
    syncResult,
    hasSynced,
    syncProfile,
    clearSync,
  };
};
