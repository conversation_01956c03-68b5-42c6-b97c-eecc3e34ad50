import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { usePlatformConnections } from './usePlatformConnections';

export const useSimpleOAuthCallback = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUser();
  const { handleOAuthSuccess, updateConnection } = usePlatformConnections();

  useEffect(() => {
    const handleCallback = async () => {
      if (!user?.id || !searchParams) return;

      const success = searchParams.get('success');
      const error = searchParams.get('error');

      if (success) {
        try {
          switch (success) {
            case 'youtube-verified':
              // Update YouTube connection status
              await handleOAuthSuccess('YouTube', user.id);
              
              // Show success message
              const channelName = searchParams.get('channel');
              console.log(`YouTube channel verified successfully: ${channelName}`);
              
              // Clear URL parameters
              const url = new URL(window.location.href);
              url.searchParams.delete('success');
              url.searchParams.delete('channel');
              router.replace(url.pathname);
              break;
              
            default:
              console.log('OAuth success:', success);
          }
        } catch (error) {
          console.error('Error handling OAuth success:', error);
        }
      }

      if (error) {
        try {
          // Handle OAuth errors
          console.error('OAuth error:', error);
          
          // Update connection status to show error
          if (error.includes('youtube') || error.includes('YouTube')) {
            updateConnection('YouTube', {
              status: '错误',
              error: error,
            });
          }
          
          // Clear URL parameters
          const url = new URL(window.location.href);
          url.searchParams.delete('error');
          router.replace(url.pathname);
        } catch (err) {
          console.error('Error handling OAuth error:', err);
        }
      }
    };

    handleCallback();
  }, [searchParams, user?.id, handleOAuthSuccess, updateConnection, router]);

  return {
    isHandlingCallback: searchParams ? (searchParams.has('success') || searchParams.has('error')) : false,
  };
}; 