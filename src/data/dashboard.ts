import type {
  TokenPoolData,
  BountyTask,
  CareerTask,
  ActivityOverview,
} from "@/types/dashboard";

// 模拟代币池数据
export const mockTokenPoolData: TokenPoolData = {
  totalAssets: 1500000,
  change24h: 2.5,
  tokens: [
    {
      symbol: "USD",
      amount: 500000,
      usdValue: 500000,
      percentage: 33.3,
      change24h: 0.5,
    },
    {
      symbol: "USDC",
      amount: 400000,
      usdValue: 400000,
      percentage: 26.7,
      change24h: -1.2,
    },
    {
      symbol: "ETH",
      amount: 100,
      usdValue: 300000,
      percentage: 20,
      change24h: 3.5,
    },
    {
      symbol: "MATIC",
      amount: 200000,
      usdValue: 200000,
      percentage: 13.3,
      change24h: -2.8,
    },
    {
      symbol: "BNB",
      amount: 100,
      usdValue: 100000,
      percentage: 6.7,
      change24h: 1.7,
    },
  ],
  recentTransactions: [
    {
      type: "in",
      amount: 5000,
      symbol: "USD",
      timestamp: "2024-03-20 10:30",
      status: "success",
    },
    {
      type: "out",
      amount: 2000,
      symbol: "USDC",
      timestamp: "2024-03-20 09:15",
      status: "success",
    },
    {
      type: "in",
      amount: 10000,
      symbol: "ETH",
      timestamp: "2024-03-20 08:45",
      status: "pending",
    },
    {
      type: "out",
      amount: 3000,
      symbol: "MATIC",
      timestamp: "2024-03-20 08:30",
      status: "success",
    },
    {
      type: "in",
      amount: 8000,
      symbol: "BNB",
      timestamp: "2024-03-20 07:20",
      status: "failed",
    },
  ],
  taskPool: {
    total: 1000000,
    used: 350000,
    byType: [
      { type: "短视频创作", amount: 200000, percentage: 57.1 },
      { type: "项目推广", amount: 100000, percentage: 28.6 },
      { type: "社区互动", amount: 50000, percentage: 14.3 },
    ],
  },
};

// 懸賞任務數據
export const longTermTasks: BountyTask[] = [
  {
    id: "longterm1",
    title: "Nike 2024夏季運動裝備推廣",
    description:
      "創作3個展示Nike最新夏季運動裝備的短視頻，突出產品特點和運動場景",
    reward: 500,
    progress: 0,
    progressTotal: 3,
    tag: "品牌合作",
    cta: "查看詳情",
    timeLeft: "48:00:00",
  },
  {
    id: "longterm2",
    title: "AI創作挑戰：未來科技生活",
    description:
      "使用AI工具創作5個展示未來科技生活場景的創意作品，包括智能家居、交通、醫療等",
    reward: 800,
    progress: 0,
    progressTotal: 5,
    tag: "AI創作",
    cta: "立即參與",
    timeLeft: "72:00:00",
  },
  {
    id: "longterm3",
    title: "星巴克夏日特飲推廣",
    description:
      "創作2個展示星巴克2024夏季限定飲品的創意內容，突出產品特色和飲用場景",
    reward: 300,
    progress: 0,
    progressTotal: 2,
    tag: "品牌合作",
    cta: "查看任務",
    timeLeft: "24:00:00",
  },
  {
    id: "longterm4",
    title: "元宇宙虛擬時裝週",
    description: "在元宇宙平台舉辦一場虛擬時裝秀，展示至少10套數字時裝設計",
    reward: 1000,
    progress: 0,
    progressTotal: 10,
    tag: "元宇宙",
    cta: "立即參與",
    timeLeft: "96:00:00",
  },
  {
    id: "longterm5",
    title: "環保主題創意挑戰",
    description:
      "創作3個關於環保和可持續發展的創意內容，呼籲更多人關注環境問題",
    reward: 400,
    progress: 0,
    progressTotal: 3,
    tag: "社會公益",
    cta: "查看詳情",
    timeLeft: "36:00:00",
  },
];

// 职业赏金任务数据
export const careerBountyTasks: CareerTask[] = [
  {
    id: "career1",
    title: "新星創作者",
    description: "達成 50 位訂閱者里程碑",
    reward: 100,
    progress: 50,
    progressTotal: 50,
    tag: "粉絲成長",
    cta: "查看詳情",
    timeLeft: "30d",
    icon: "youtube" as const,
  },
  {
    id: "career2",
    title: "潛力創作者",
    description: "累積 1,000 次影片觀看",
    reward: 200,
    progress: 0,
    progressTotal: 1000,
    tag: "觀看成長",
    cta: "查看詳情",
    timeLeft: "30d",
    icon: "youtube" as const,
  },
  {
    id: "career3",
    title: "互動達人",
    description: "獲得 100 個影片按讚",
    reward: 150,
    progress: 0,
    progressTotal: 100,
    tag: "互動成長",
    cta: "查看詳情",
    timeLeft: "30d",
    icon: "youtube" as const,
  },
  {
    id: "career4",
    title: "社群之星",
    description: "獲得 50 則影片留言",
    reward: 150,
    progress: 10,
    progressTotal: 50,
    tag: "社群成長",
    cta: "查看詳情",
    timeLeft: "30d",
    icon: "youtube" as const,
  },
];

// 活动概览数据
export const activityOverview: ActivityOverview = {
  completedTasks: 2,
  totalRpx: 50,
  nextGoal: "完成5个任务",
};
