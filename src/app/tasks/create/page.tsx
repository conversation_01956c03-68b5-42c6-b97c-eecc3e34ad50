"use client";

import * as React from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { TaskCategory, TaskType, TaskFormData } from "@/types/task";
import { X } from "lucide-react";

export default function CreateTaskPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [formData, setFormData] = React.useState<TaskFormData>({
    title: "",
    description: "",
    type: TaskType.SHORT_VIDEO,
    category: TaskCategory.REGULAR,
    reward: 1,
    experiencePoints: 10,
    deadline: new Date(),
    maxParticipants: 100,
    requirements: [],
  });
  const [newRequirement, setNewRequirement] = React.useState("");

  React.useEffect(() => {
    if (!user) {
      router.push("/auth");
    }
  }, [user, router]);

  const handleAddRequirement = () => {
    if (newRequirement.trim()) {
      setFormData({
        ...formData,
        requirements: [...formData.requirements, newRequirement.trim()],
      });
      setNewRequirement("");
    }
  };

  const handleRemoveRequirement = (index: number) => {
    const updatedRequirements = [...formData.requirements];
    updatedRequirements.splice(index, 1);
    setFormData({
      ...formData,
      requirements: updatedRequirements,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // TODO: Implement task creation logic
      console.log("Creating task:", formData);

      // Reset form
      setFormData({
        title: "",
        description: "",
        type: TaskType.SHORT_VIDEO,
        category: TaskCategory.REGULAR,
        reward: 1,
        experiencePoints: 10,
        deadline: new Date(),
        maxParticipants: 100,
        requirements: [],
      });

      // Redirect to tasks page
      router.push("/tasks");
    } catch (error) {
      console.error("Error creating task:", error);
    }
  };

  const calculateFees = () => {
    if (formData.category === TaskCategory.BOUNTY) {
      return formData.reward * 0.3; // 30% fee for bounty tasks
    }
    return 0; // No fee for regular tasks
  };

  const totalCost = () => {
    return formData.reward + calculateFees();
  };

  if (!user) {
    return null;
  }

  return (
    <main className="container mx-auto py-8">
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Create New Task</CardTitle>
          <CardDescription>
            Publish a task for creators to complete and earn rewards
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title">Task Title</Label>
              <Input
                id="title"
                placeholder="Enter a clear, concise title"
                value={formData.title}
                onChange={(e) =>
                  setFormData({ ...formData, title: e.target.value })
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe what creators need to do to complete this task"
                className="min-h-[120px]"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="taskType">Task Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) =>
                    setFormData({ ...formData, type: value as TaskType })
                  }
                  required
                >
                  <SelectTrigger id="taskType">
                    <SelectValue placeholder="Select task type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TaskType.SHORT_VIDEO}>
                      Short Video
                    </SelectItem>
                    <SelectItem value={TaskType.PROJECT_PROMOTION}>
                      Project Promotion
                    </SelectItem>
                    <SelectItem value={TaskType.COMMUNITY_INTERACTION}>
                      Community Interaction
                    </SelectItem>
                    <SelectItem value={TaskType.DEEP_CONTENT}>
                      Deep Content
                    </SelectItem>
                    <SelectItem value={TaskType.PROJECT_ANALYSIS}>
                      Project Analysis
                    </SelectItem>
                    <SelectItem value={TaskType.COMMUNITY_OPERATION}>
                      Community Operation
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="taskCategory">Task Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) =>
                    setFormData({
                      ...formData,
                      category: value as TaskCategory,
                    })
                  }
                  required
                >
                  <SelectTrigger id="taskCategory">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TaskCategory.REGULAR}>
                      Regular Task
                    </SelectItem>
                    <SelectItem value={TaskCategory.BOUNTY}>
                      Bounty Task
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="reward">Reward (USD)</Label>
                <Input
                  id="reward"
                  type="number"
                  min="1"
                  value={formData.reward}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      reward: parseFloat(e.target.value),
                    })
                  }
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="experiencePoints">Experience Points</Label>
                <Input
                  id="experiencePoints"
                  type="number"
                  min="1"
                  value={formData.experiencePoints}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      experiencePoints: parseInt(e.target.value),
                    })
                  }
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="deadline">Deadline</Label>
                <Input
                  id="deadline"
                  type="date"
                  value={formData.deadline.toISOString().split("T")[0]}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      deadline: new Date(e.target.value),
                    })
                  }
                  min={new Date().toISOString().split("T")[0]}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxParticipants">Maximum Participants</Label>
                <Input
                  id="maxParticipants"
                  type="number"
                  min="1"
                  max="10000"
                  value={formData.maxParticipants}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      maxParticipants: parseInt(e.target.value),
                    })
                  }
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Maximum 10,000 participants
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="requirements">Requirements</Label>
                <div className="flex mt-1 space-x-2">
                  <Input
                    id="requirements"
                    placeholder="Add a requirement"
                    value={newRequirement}
                    onChange={(e) => setNewRequirement(e.target.value)}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleAddRequirement}
                  >
                    Add
                  </Button>
                </div>
              </div>

              {formData.requirements.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Added Requirements:</p>
                  <ul className="space-y-2">
                    {formData.requirements.map((req, index) => (
                      <li
                        key={index}
                        className="flex items-center justify-between p-2 bg-muted rounded-md"
                      >
                        <span className="text-sm">{req}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveRequirement(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Platform Fee (30% for Bounty Tasks)</span>
                <span>{calculateFees()} USD</span>
              </div>
              <div className="flex justify-between font-semibold">
                <span>Total Cost</span>
                <span>{totalCost()} USD</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/tasks")}
            >
              Cancel
            </Button>
            <Button type="submit">Create Task</Button>
          </CardFooter>
        </form>
      </Card>
    </main>
  );
}
