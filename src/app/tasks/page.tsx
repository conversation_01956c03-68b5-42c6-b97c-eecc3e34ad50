"use client";

import React from "react";
import DashboardLayout from "@/components/DashboardLayout";
import TasksContent from "@/components/content/TasksContent";

export const dynamic = "force-dynamic";

export default function TasksPage() {
  return (
    <DashboardLayout
      enableDynamicContent={false}
      backgroundPattern="hexagon"
    >
      <TasksContent />
    </DashboardLayout>
  );
}
