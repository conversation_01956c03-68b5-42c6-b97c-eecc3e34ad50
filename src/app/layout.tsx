import React from "react";
import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ClerkProvider } from "@clerk/nextjs";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { QueryProvider } from "@/components/providers/query-provider";
import { LayoutProvider } from "@/contexts/LayoutContext";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "VG蜂巢",
  description: "創造你的規則，釋放創作價值 - 連接創作者與粉絲的Web3平台",
  metadataBase: new URL("https://creator-coin-hive.vercel.app"),
  keywords: [
    "Web3",
    "創作者經濟",
    "NFT",
    "區塊鏈",
    "社交平台",
    "創作者幣",
    "VG",
    "蜂巢",
  ],
  authors: [{ name: "VG蜂巢 Team" }],
  creator: "VG蜂巢",
  publisher: "VG蜂巢",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: "/site.webmanifest",
  icons: {
    icon: [
      { url: "/favicon.svg", type: "image/svg+xml" },
      { url: "/favicon-16x16.svg", sizes: "16x16", type: "image/svg+xml" },
      { url: "/favicon-32x32.svg", sizes: "32x32", type: "image/svg+xml" },
    ],
    apple: [
      { url: "/apple-touch-icon.svg", sizes: "180x180", type: "image/svg+xml" },
    ],
    other: [
      {
        rel: "mask-icon",
        url: "/logo.svg",
        color: "#00CEC9",
      },
    ],
  },
  appleWebApp: {
    title: "VG蜂巢",
    statusBarStyle: "default",
    capable: true,
  },
  openGraph: {
    type: "website",
    locale: "zh_TW",
    url: "https://vgee.org",
    title: "VG蜂巢",
    description: "創造你的規則，釋放創作價值 - 連接創作者與粉絲的Web3平台",
    siteName: "VG蜂巢",
    images: [
      {
        url: "/logo.svg",
        width: 500,
        height: 500,
        alt: "VG蜂巢 Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "VG蜂巢",
    description: "創造你的規則，釋放創作價值 - 連接創作者與粉絲的Web3平台",
    images: ["/logo.svg"],
    creator: "@VGHive",
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#00CEC9" },
    { media: "(prefers-color-scheme: dark)", color: "#1a1a2e" },
  ],
  colorScheme: "dark light",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body className={inter.className}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <QueryProvider>
              <LayoutProvider>
                <TooltipProvider>
                  <Toaster />
                  <Sonner />
                  {children}
                </TooltipProvider>
              </LayoutProvider>
            </QueryProvider>
          </ThemeProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
