"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import type { Profile } from "@/lib/schema";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/DashboardLayout";

interface LeaderboardEntry extends Profile {
  rank: number;
}

export default function LeaderboardPage() {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const router = useRouter();

  const fetchLeaderboard = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/leaderboard?sortBy=experience_points&limit=50`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch leaderboard");
      }

      const data: Profile[] = await response.json();

      // 添加排名
      const rankedData: LeaderboardEntry[] = data.map((profile, index) => ({
        ...profile,
        rank: index + 1,
      }));

      setLeaderboard(rankedData);
      setError("");
    } catch (err) {
      setError("載入榜單失敗，請稍後重試");
      console.error("Error fetching leaderboard:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaderboard();
  }, []);

  const getRankIcon = (rank: number) => {
    if (rank === 1) return "🥇";
    if (rank === 2) return "🥈";
    if (rank === 3) return "🥉";
    return `#${rank}`;
  };

  const getRankColor = (rank: number) => {
    if (rank === 1) return "from-[#00ff87] to-[#60efff]";
    if (rank === 2) return "from-[#ff2e63] to-[#ff6b9d]";
    if (rank === 3) return "from-[#ff9500] to-[#ffb347]";
    return "from-[#2a2a3e] to-[#1a1a2e]";
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getSortValue = (profile: Profile) => {
    return formatNumber(profile.experience_points);
  };

  return (
    <DashboardLayout
      showBountySidebar={false}
      enableDynamicContent={false}
      backgroundPattern="hexagon"
    >
      <div className="w-full flex flex-col">
        {/* 頁面標題 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white font-heading mb-2">
            🏆 創作者榜單
          </h1>
          <p className="text-gray-300 text-lg">
            展示平台上最優秀的創作者
          </p>
        </div>

        {/* 主要内容 */}
        <main className="flex-1 px-4 pb-8">


        {/* 榜单内容 */}
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl px-8 py-6 shadow-lg">
              <div className="text-white text-2xl font-bold animate-pulse flex items-center gap-4">
                <div className="w-3 h-10 rounded-full bg-gradient-to-b from-[#00ff87] to-[#60efff] animate-pulse" />
                <span className="bg-gradient-to-r from-[#00ff87] to-[#60efff] bg-clip-text text-transparent">
                  🚀 載入榜單中...
                </span>
              </div>
            </div>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center py-20">
            <div className="bg-[#1a1a2e]/95 backdrop-blur-sm rounded-xl p-8 border border-[#ff2e63]/50 text-center shadow-lg">
              <div className="text-[#ff2e63] text-xl font-bold mb-4">
                ⚠️ {error}
              </div>
              <Button
                onClick={() => fetchLeaderboard()}
                className="bg-gradient-to-r from-[#ff2e63] to-[#ff6b9d] hover:from-[#e91e54] hover:to-[#ff5a8a] text-white font-bold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                🔄 重新載入
              </Button>
            </div>
          </div>
        ) : (
          <div className="max-w-7xl mx-auto">
            {/* 前三名特殊展示 - 颁奖台样式 */}
            {leaderboard.length >= 3 && (
              <div className="mb-12">
                <h2 className="text-2xl font-bold text-white text-center mb-8 font-heading">
                  🏆 榜單前三甲
                </h2>
                {/* 頒獎台布局 - 底部對齊 */}
                <div className="flex items-end justify-center gap-6 md:gap-12 mb-8 px-4 max-w-5xl mx-auto">
                  {/* 第二名 - 左側，較低 */}
                  <div className="flex flex-col items-center flex-1 max-w-[280px]">
                    {leaderboard[1] && (
                      <div className="bg-gradient-to-br from-[#c0c0c0]/20 to-[#808080]/20 backdrop-blur-sm rounded-2xl p-6 md:p-8 border-2 border-[#c0c0c0]/30 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 cursor-pointer group w-full"
                           onClick={() => router.push(`/creator/${leaderboard[1].clerk_user_id}`)}>
                        <div className="text-center">
                          <div className="relative mb-4 md:mb-6">
                            <div className="w-20 h-20 md:w-24 md:h-24 mx-auto rounded-full overflow-hidden border-4 border-[#c0c0c0] shadow-lg">
                              {leaderboard[1].avatar_url ? (
                                <img src={leaderboard[1].avatar_url} alt={leaderboard[1].full_name || "用戶"} className="w-full h-full object-cover" />
                              ) : (
                                <div className="w-full h-full bg-gradient-to-br from-[#c0c0c0] to-[#808080] flex items-center justify-center text-white text-2xl md:text-3xl font-bold">
                                  {leaderboard[1].full_name?.[0] || "?"}
                                </div>
                              )}
                            </div>
                            <div className="absolute -top-2 -right-2 w-8 h-8 md:w-10 md:h-10 bg-gradient-to-r from-[#c0c0c0] to-[#808080] rounded-full flex items-center justify-center text-white font-black text-sm md:text-lg shadow-lg">
                              🥈
                            </div>
                          </div>
                          <h3 className="text-white font-bold text-lg md:text-xl mb-3 group-hover:text-[#c0c0c0] transition-colors line-clamp-2">
                            {leaderboard[1].full_name || "匿名用戶"}
                          </h3>
                          <div className="text-[#c0c0c0] font-bold text-xl md:text-2xl mb-2">
                            {getSortValue(leaderboard[1])}
                          </div>
                          <div className="text-gray-400 text-sm md:text-base">
                            經驗值
                          </div>
                        </div>
                      </div>
                    )}
                    {/* 第二名台階 */}
                    <div className="w-full h-10 md:h-16 bg-gradient-to-t from-[#c0c0c0]/30 to-[#c0c0c0]/10 rounded-t-lg border-t-2 border-[#c0c0c0]/50 flex items-center justify-center">
                      <span className="text-[#c0c0c0] font-bold text-xl md:text-2xl">2</span>
                    </div>
                  </div>

                  {/* 第一名 - 中間，最高 */}
                  <div className="flex flex-col items-center flex-1 max-w-[320px]">
                    {leaderboard[0] && (
                      <div className="bg-gradient-to-br from-[#ffd700]/20 to-[#ffb347]/20 backdrop-blur-sm rounded-2xl p-8 md:p-10 border-2 border-[#ffd700]/50 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-110 cursor-pointer group relative overflow-hidden w-full"
                           onClick={() => router.push(`/creator/${leaderboard[0].clerk_user_id}`)}>
                        <div className="absolute inset-0 bg-gradient-to-r from-[#ffd700]/10 to-[#ffb347]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                        <div className="text-center relative z-10">
                          <div className="relative mb-6 md:mb-8">
                            <div className="w-24 h-24 md:w-28 md:h-28 mx-auto rounded-full overflow-hidden border-4 border-[#ffd700] shadow-2xl">
                              {leaderboard[0].avatar_url ? (
                                <img src={leaderboard[0].avatar_url} alt={leaderboard[0].full_name || "用戶"} className="w-full h-full object-cover" />
                              ) : (
                                <div className="w-full h-full bg-gradient-to-br from-[#ffd700] to-[#ffb347] flex items-center justify-center text-white text-3xl md:text-4xl font-bold">
                                  {leaderboard[0].full_name?.[0] || "?"}
                                </div>
                              )}
                            </div>
                            <div className="absolute -top-3 -right-3 md:-top-4 md:-right-4 w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-[#ffd700] to-[#ffb347] rounded-full flex items-center justify-center text-white font-black text-lg md:text-xl shadow-xl animate-pulse">
                              🥇
                            </div>
                          </div>
                          <h3 className="text-white font-bold text-xl md:text-2xl mb-3 md:mb-4 group-hover:text-[#ffd700] transition-colors line-clamp-2">
                            {leaderboard[0].full_name || "匿名用戶"}
                          </h3>
                          <div className="text-[#ffd700] font-black text-2xl md:text-3xl mb-3">
                            {getSortValue(leaderboard[0])}
                          </div>
                          <div className="text-gray-300 text-sm md:text-base font-semibold">
                            👑 經驗值 冠軍
                          </div>
                        </div>
                      </div>
                    )}
                    {/* 第一名台階 - 最高 */}
                    <div className="w-full h-14 md:h-20 bg-gradient-to-t from-[#ffd700]/30 to-[#ffd700]/10 rounded-t-lg border-t-2 border-[#ffd700]/50 flex items-center justify-center">
                      <span className="text-[#ffd700] font-bold text-2xl md:text-3xl">1</span>
                    </div>
                  </div>

                  {/* 第三名 - 右側，較低 */}
                  <div className="flex flex-col items-center flex-1 max-w-[280px]">
                    {leaderboard[2] && (
                      <div className="bg-gradient-to-br from-[#cd7f32]/20 to-[#8b4513]/20 backdrop-blur-sm rounded-2xl p-6 md:p-8 border-2 border-[#cd7f32]/30 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 cursor-pointer group w-full"
                           onClick={() => router.push(`/creator/${leaderboard[2].clerk_user_id}`)}>
                        <div className="text-center">
                          <div className="relative mb-4 md:mb-6">
                            <div className="w-20 h-20 md:w-24 md:h-24 mx-auto rounded-full overflow-hidden border-4 border-[#cd7f32] shadow-lg">
                              {leaderboard[2].avatar_url ? (
                                <img src={leaderboard[2].avatar_url} alt={leaderboard[2].full_name || "用戶"} className="w-full h-full object-cover" />
                              ) : (
                                <div className="w-full h-full bg-gradient-to-br from-[#cd7f32] to-[#8b4513] flex items-center justify-center text-white text-2xl md:text-3xl font-bold">
                                  {leaderboard[2].full_name?.[0] || "?"}
                                </div>
                              )}
                            </div>
                            <div className="absolute -top-2 -right-2 w-8 h-8 md:w-10 md:h-10 bg-gradient-to-r from-[#cd7f32] to-[#8b4513] rounded-full flex items-center justify-center text-white font-black text-sm md:text-lg shadow-lg">
                              🥉
                            </div>
                          </div>
                          <h3 className="text-white font-bold text-lg md:text-xl mb-3 group-hover:text-[#cd7f32] transition-colors line-clamp-2">
                            {leaderboard[2].full_name || "匿名用戶"}
                          </h3>
                          <div className="text-[#cd7f32] font-bold text-xl md:text-2xl mb-2">
                            {getSortValue(leaderboard[2])}
                          </div>
                          <div className="text-gray-400 text-sm md:text-base">
                            經驗值
                          </div>
                        </div>
                      </div>
                    )}
                    {/* 第三名台階 */}
                    <div className="w-full h-10 md:h-16 bg-gradient-to-t from-[#cd7f32]/30 to-[#cd7f32]/10 rounded-t-lg border-t-2 border-[#cd7f32]/50 flex items-center justify-center">
                      <span className="text-[#cd7f32] font-bold text-xl md:text-2xl">3</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 完整榜單 */}
            <div className="space-y-3">
              <h2 className="text-xl font-bold text-white mb-6 font-heading flex items-center gap-3">
                <div className="w-1 h-6 bg-gradient-to-b from-[#00ff87] to-[#60efff] rounded-full" />
                📊 完整榜單
              </h2>

              {leaderboard.map((entry, index) => (
                <div
                  key={entry.clerk_user_id}
                  className={`
                    relative overflow-hidden rounded-xl transition-all duration-300 cursor-pointer group
                    ${entry.rank <= 3
                      ? 'bg-gradient-to-r from-[#1a1a2e]/95 to-[#2a2a3e]/95 border-2 border-[#00ff87]/30 shadow-lg hover:shadow-xl hover:border-[#00ff87]/50'
                      : 'bg-[#1a1a2e]/90 border border-[#2a2a3e] shadow-md hover:shadow-lg hover:border-[#00ff87]/20'
                    }
                    backdrop-blur-sm transform hover:scale-[1.01]
                  `}
                  onClick={() => router.push(`/creator/${entry.clerk_user_id}`)}
                >
                  {/* 背景装饰 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00ff87]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <div className="relative z-10 p-4 sm:p-6">
                    <div className="flex items-center gap-4 sm:gap-6">
                      {/* 排名 */}
                      <div className={`
                        flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-xl flex items-center justify-center font-black text-lg shadow-lg
                        ${entry.rank <= 3
                          ? `bg-gradient-to-r ${getRankColor(entry.rank)} border-2 border-white/20`
                          : 'bg-gradient-to-r from-[#2a2a3e] to-[#1a1a2e] border border-[#3a3a4e]'
                        }
                      `}>
                        <span className="text-white drop-shadow-lg">
                          {getRankIcon(entry.rank)}
                        </span>
                      </div>

                      {/* 头像 */}
                      <div className="flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-xl overflow-hidden border-2 border-[#2a2a3e] shadow-lg group-hover:border-[#00ff87]/30 transition-colors">
                        {entry.avatar_url ? (
                          <img
                            src={entry.avatar_url}
                            alt={entry.full_name || "用户"}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-[#2a2a3e] to-[#1a1a2e] flex items-center justify-center text-white text-xl font-bold">
                            {entry.full_name?.[0] || "?"}
                          </div>
                        )}
                      </div>

                      {/* 用户信息 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                          <h3 className="text-white font-bold text-lg sm:text-xl font-heading truncate group-hover:text-[#00ff87] transition-colors">
                            {entry.full_name || "匿名用戶"}
                          </h3>
                          <div className="flex items-center gap-2 flex-wrap">
                            <span className="px-2 py-1 bg-[#00ff87]/20 text-[#00ff87] rounded-full text-xs font-semibold border border-[#00ff87]/30">
                              Lv.{entry.level}
                            </span>
                            {entry.is_premium_member && (
                              <span className="px-2 py-1 bg-gradient-to-r from-[#ff2e63] to-[#ff6b9d] rounded-full text-white text-xs font-bold">
                                💎 VIP
                              </span>
                            )}
                          </div>
                        </div>

                        {entry.motto && (
                          <p className="text-gray-400 text-sm italic mb-2 line-clamp-1">
                            "{entry.motto}"
                          </p>
                        )}

                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>經驗值: {entry.experience_points?.toLocaleString() || 0}</span>
                          {entry.subscribers > 0 && (
                            <span>訂閱: {entry.subscribers.toLocaleString()}</span>
                          )}
                          {entry.views > 0 && (
                            <span>觀看: {entry.views.toLocaleString()}</span>
                          )}
                        </div>
                      </div>

                      {/* 统计数据 */}
                      <div className="flex-shrink-0 text-right">
                        <div className="text-white font-black text-xl sm:text-2xl mb-1 group-hover:text-[#00ff87] transition-colors">
                          {getSortValue(entry)}
                        </div>
                        <div className="text-gray-400 text-xs sm:text-sm font-semibold flex items-center justify-end gap-1">
                          <span>💎</span>
                          <span>經驗值</span>
                        </div>
                      </div>

                      {/* YouTube 信息 */}
                      {entry.youtube_title && (
                        <div className="hidden sm:block flex-shrink-0 bg-[#2a2a3e]/50 rounded-lg p-3 border border-[#ff2e63]/30 max-w-32">
                          <div className="text-[#ff2e63] text-xs font-semibold mb-1 text-center">
                            📺 YouTube
                          </div>
                          <div className="text-white text-sm font-bold truncate text-center">
                            {entry.youtube_title}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {leaderboard.length === 0 && (
              <div className="text-center py-20">
                <div className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-2xl p-12 shadow-lg max-w-md mx-auto">
                  <div className="text-6xl mb-4">🎯</div>
                  <div className="text-gray-400 text-xl font-semibold mb-2">
                    暫無榜單數據
                  </div>
                  <div className="text-gray-500 text-sm">
                    等待更多創作者加入平台！
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        </main>
      </div>
    </DashboardLayout>
  );
}
