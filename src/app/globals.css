@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Color Scheme - Inspired by Gaming UI */
:root {
  /* Primary Background Colors */
  --bg-primary: #1a2332;
  --bg-secondary: #243447;
  --bg-tertiary: #2d4059;
  --bg-card: #334155;
  --bg-card-hover: #3d5270;

  /* Accent Colors */
  --accent-teal: #4ecdc4;
  --accent-teal-light: #6ee7de;
  --accent-teal-dark: #3ba99f;
  --accent-orange: #f39c12;
  --accent-orange-light: #f5b041;
  --accent-orange-dark: #d68910;
  --accent-purple: #8e44ad;
  --accent-purple-light: #a569bd;
  --accent-purple-dark: #7d3c98;

  /* Status Colors */
  --success: #27ae60;
  --success-light: #58d68d;
  --warning: #f39c12;
  --warning-light: #f5b041;
  --error: #e74c3c;
  --error-light: #ec7063;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-disabled: #64748b;

  /* Border Colors */
  --border-primary: #475569;
  --border-secondary: #64748b;
  --border-accent: #4ecdc4;
}
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
    /* Deep navy blue gradient background inspired by gaming UI */
    background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%) !important;
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden; /* 防止水平滚动条 */
  }

  /* Ensure root elements don't override background */
  #__next, [data-nextjs-scroll-focus-boundary] {
    background: transparent !important;
  }

  /* 防止动画导致的滚动条问题 */
  html {
    overflow-x: hidden;
  }

  /* Alternative dark gradient backgrounds */
  .bg-navy-gradient {
    background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%);
  }

  .bg-deep-dark-gradient {
    background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #2a2f3e 100%);
  }

  .bg-midnight-gradient {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 50%, #21262d 100%);
  }

  /* Hexagonal honeycomb animations - 减小移动范围防止溢出 */
  @keyframes hexFloat {
    0%, 100% {
      transform: translateY(0px) translateX(0px);
    }
    25% {
      transform: translateY(-3px) translateX(2px);
    }
    50% {
      transform: translateY(-2px) translateX(-1px);
    }
    75% {
      transform: translateY(-5px) translateX(1px);
    }
  }

  @keyframes hexPulse {
    0%, 100% {
      opacity: 0.05;
      transform: scale(1);
    }
    50% {
      opacity: 0.15;
      transform: scale(1.02);
    }
  }

  @keyframes hexGlow {
    0%, 100% {
      filter: brightness(1) drop-shadow(0 0 5px rgba(0, 255, 135, 0.3));
    }
    50% {
      filter: brightness(1.2) drop-shadow(0 0 15px rgba(96, 239, 255, 0.4));
    }
  }

  /* Cyberpunk grid animations */
  @keyframes cyberpunkScan {
    0% {
      transform: translateY(-100%);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateY(100vh);
      opacity: 0;
    }
  }

  /* Utility classes for hexagonal patterns */
  .hex-float {
    animation: hexFloat 20s ease-in-out infinite;
  }

  .hex-pulse {
    animation: hexPulse 15s ease-in-out infinite;
  }

  .hex-glow {
    animation: hexGlow 8s ease-in-out infinite;
  }

  /* 自定义无感滚动条样式 */
  /* Webkit 浏览器 (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 0;
    margin: 4px 0;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.06);
    border-radius: 2px;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 20px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 135, 0.2);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb:active {
    background: rgba(0, 255, 135, 0.4);
  }

  /* 移除滚动条的箭头按钮 */
  ::-webkit-scrollbar-button {
    display: none;
    width: 0;
    height: 0;
  }

  /* 移除滚动条的角落 */
  ::-webkit-scrollbar-corner {
    background: transparent;
    display: none;
  }

  /* Firefox 滚动条样式 */
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.06) transparent;
  }

  /* 悬停时的滚动条样式 */
  *:hover {
    scrollbar-color: rgba(0, 255, 135, 0.2) transparent;
  }

  /* 活跃状态的滚动条样式 */
  *:active {
    scrollbar-color: rgba(0, 255, 135, 0.4) transparent;
  }

  /* 特殊的滚动容器样式 - 更加无感 */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.04) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    margin: 2px 0;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.04);
    border-radius: 1.5px;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 16px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 135, 0.15);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:active {
    background: rgba(0, 255, 135, 0.3);
  }

  .custom-scrollbar::-webkit-scrollbar-button {
    display: none;
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 超级无感滚动条 - 用于主要内容区域 */
  .scrollbar-minimal {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.02) transparent;
  }

  .scrollbar-minimal::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  .scrollbar-minimal::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-minimal::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 1px;
    border: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .scrollbar-minimal::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 135, 0.1);
  }

  .scrollbar-minimal::-webkit-scrollbar-button {
    display: none;
  }

  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-hidden {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hidden::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }
}