"use client";

import * as React from "react";
import { useAuth } from "@/contexts/AuthContext";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import DashboardHeader from "@/components/DashboardHeader";
import { motion } from "framer-motion";

const NAV_ITEMS = [
  // { label: "平台動態", href: "/news" },
  { label: "隱私政策", href: "/privacy" },
];

// SVG 卡通蜜蜂组件
const CartoonBee = ({ className = "", scale = 1 }) => (
  <svg
    className={className}
    width={60 * scale}
    height={60 * scale}
    viewBox="0 0 60 60"
    fill="none"
  >
    {/* 蜜蜂身体 */}
    <ellipse
      cx="30"
      cy="35"
      rx="12"
      ry="18"
      fill="#FFD700"
      stroke="#FF8C00"
      strokeWidth="2"
    />

    {/* 蜜蜂条纹 */}
    <ellipse cx="30" cy="28" rx="10" ry="3" fill="#2D2D2D" />
    <ellipse cx="30" cy="35" rx="10" ry="3" fill="#2D2D2D" />
    <ellipse cx="30" cy="42" rx="10" ry="3" fill="#2D2D2D" />

    {/* 蜜蜂头部 */}
    <circle
      cx="30"
      cy="15"
      r="8"
      fill="#FFD700"
      stroke="#FF8C00"
      strokeWidth="2"
    />

    {/* 眼睛 */}
    <circle cx="27" cy="13" r="2" fill="white" />
    <circle cx="33" cy="13" r="2" fill="white" />
    <circle cx="27" cy="13" r="1" fill="black" />
    <circle cx="33" cy="13" r="1" fill="black" />

    {/* 嘴巴 */}
    <path
      d="M28 17 Q30 19 32 17"
      stroke="#FF8C00"
      strokeWidth="1.5"
      fill="none"
    />

    {/* 触角 */}
    <line x1="26" y1="8" x2="24" y2="5" stroke="#2D2D2D" strokeWidth="2" />
    <line x1="34" y1="8" x2="36" y2="5" stroke="#2D2D2D" strokeWidth="2" />
    <circle cx="24" cy="5" r="1.5" fill="#FF8C00" />
    <circle cx="36" cy="5" r="1.5" fill="#FF8C00" />

    {/* 翅膀 */}
    <ellipse
      cx="20"
      cy="20"
      rx="8"
      ry="12"
      fill="rgba(255,255,255,0.7)"
      stroke="#60efff"
      strokeWidth="1"
    />
    <ellipse
      cx="40"
      cy="20"
      rx="8"
      ry="12"
      fill="rgba(255,255,255,0.7)"
      stroke="#60efff"
      strokeWidth="1"
    />

    {/* 翅膀装饰线 */}
    <path
      d="M16 15 Q20 20 16 25"
      stroke="#60efff"
      strokeWidth="1"
      fill="none"
    />
    <path
      d="M44 15 Q40 20 44 25"
      stroke="#60efff"
      strokeWidth="1"
      fill="none"
    />
  </svg>
);

// SVG 飞船组件
const CartoonSpaceship = ({ className = "", scale = 1 }) => (
  <svg
    className={className}
    width={80 * scale}
    height={60 * scale}
    viewBox="0 0 80 60"
    fill="none"
  >
    {/* 飞船主体 */}
    <ellipse
      cx="40"
      cy="30"
      rx="25"
      ry="15"
      fill="#00ff87"
      stroke="#60efff"
      strokeWidth="2"
    />

    {/* 飞船顶部舱门 */}
    <ellipse
      cx="40"
      cy="25"
      rx="12"
      ry="8"
      fill="rgba(96,239,255,0.3)"
      stroke="#60efff"
      strokeWidth="1"
    />

    {/* 飞船窗户 */}
    <circle
      cx="35"
      cy="25"
      r="3"
      fill="rgba(255,255,255,0.8)"
      stroke="#60efff"
      strokeWidth="1"
    />
    <circle
      cx="45"
      cy="25"
      r="3"
      fill="rgba(255,255,255,0.8)"
      stroke="#60efff"
      strokeWidth="1"
    />

    {/* 飞船推进器 */}
    <ellipse cx="15" cy="35" rx="6" ry="4" fill="#ff2e63" />
    <ellipse cx="65" cy="35" rx="6" ry="4" fill="#ff2e63" />

    {/* 推进器火焰 */}
    <path d="M9 33 Q5 35 9 37 Q7 35 9 33" fill="#FFD700" />
    <path d="M71 33 Q75 35 71 37 Q73 35 71 33" fill="#FFD700" />

    {/* 飞船装饰 */}
    <ellipse cx="40" cy="35" rx="15" ry="3" fill="rgba(96,239,255,0.2)" />
    <circle cx="30" cy="30" r="2" fill="#60efff" />
    <circle cx="50" cy="30" r="2" fill="#60efff" />
  </svg>
);

// SVG 闪烁星星组件
const CartoonStar = ({ className = "", scale = 1, color = "#FFD700" }) => (
  <svg
    className={className}
    width={30 * scale}
    height={30 * scale}
    viewBox="0 0 30 30"
    fill="none"
  >
    <path
      d="M15 2 L18 12 L28 12 L20 18 L23 28 L15 22 L7 28 L10 18 L2 12 L12 12 Z"
      fill={color}
      stroke="rgba(255,255,255,0.3)"
      strokeWidth="1"
    />
    <circle cx="15" cy="15" r="3" fill="rgba(255,255,255,0.5)" />
  </svg>
);

// SVG 可爱机器人组件
const CartoonRobot = ({ className = "", scale = 1 }) => (
  <svg
    className={className}
    width={50 * scale}
    height={70 * scale}
    viewBox="0 0 50 70"
    fill="none"
  >
    {/* 机器人头部 */}
    <rect
      x="15"
      y="5"
      width="20"
      height="20"
      rx="5"
      fill="#60efff"
      stroke="#00ff87"
      strokeWidth="2"
    />

    {/* 机器人眼睛 */}
    <circle cx="22" cy="13" r="2" fill="white" />
    <circle cx="28" cy="13" r="2" fill="white" />
    <circle cx="22" cy="13" r="1" fill="black" />
    <circle cx="28" cy="13" r="1" fill="black" />

    {/* 机器人嘴巴 */}
    <rect x="22" y="18" width="6" height="2" rx="1" fill="#00ff87" />

    {/* 机器人天线 */}
    <line x1="25" y1="5" x2="25" y2="2" stroke="#00ff87" strokeWidth="2" />
    <circle cx="25" cy="2" r="1.5" fill="#ff2e63" />

    {/* 机器人身体 */}
    <rect
      x="12"
      y="25"
      width="26"
      height="30"
      rx="5"
      fill="#00ff87"
      stroke="#60efff"
      strokeWidth="2"
    />

    {/* 机器人胸部按钮 */}
    <circle cx="20" cy="35" r="2" fill="#ff2e63" />
    <circle cx="30" cy="35" r="2" fill="#FFD700" />
    <circle cx="25" cy="45" r="3" fill="#60efff" />

    {/* 机器人手臂 */}
    <rect x="5" y="30" width="7" height="15" rx="3" fill="#60efff" />
    <rect x="38" y="30" width="7" height="15" rx="3" fill="#60efff" />

    {/* 机器人腿部 */}
    <rect x="16" y="55" width="6" height="12" rx="3" fill="#60efff" />
    <rect x="28" y="55" width="6" height="12" rx="3" fill="#60efff" />
  </svg>
);

// SVG 可爱云朵组件
const CartoonCloud = ({ className = "", scale = 1 }) => (
  <svg
    className={className}
    width={80 * scale}
    height={50 * scale}
    viewBox="0 0 80 50"
    fill="none"
  >
    <circle cx="20" cy="30" r="12" fill="rgba(255,255,255,0.8)" />
    <circle cx="35" cy="25" r="15" fill="rgba(255,255,255,0.8)" />
    <circle cx="50" cy="25" r="12" fill="rgba(255,255,255,0.8)" />
    <circle cx="60" cy="30" r="10" fill="rgba(255,255,255,0.8)" />
    <circle cx="40" cy="35" r="8" fill="rgba(255,255,255,0.8)" />

    {/* 云朵装饰星星 */}
    <circle cx="30" cy="20" r="1" fill="#FFD700" />
    <circle cx="45" cy="18" r="1" fill="#60efff" />
    <circle cx="55" cy="22" r="1" fill="#ff2e63" />
  </svg>
);

export default function Home() {
  const { user } = useAuth();

  return (
    <div className="min-h-screen w-full flex flex-col relative overflow-hidden">
      {/* 深藏青色科技背景 - 更深更暗 */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748]" />

        {/* 科技装饰性元素 - 更新为新配色 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-48 h-1 bg-gradient-to-r from-[#4ecdc4] to-transparent animate-pulse"></div>
          <div className="absolute top-32 right-20 w-1 h-48 bg-gradient-to-b from-[#f39c12] to-transparent animate-pulse"></div>
          <div className="absolute bottom-20 left-1/4 w-64 h-1 bg-gradient-to-r from-[#8e44ad] to-transparent animate-pulse"></div>
          <div className="absolute top-1/2 right-10 w-1 h-40 bg-gradient-to-b from-[#4ecdc4] to-transparent animate-pulse"></div>
          <div className="absolute bottom-1/3 right-1/3 w-32 h-1 bg-gradient-to-r from-[#f39c12] to-transparent animate-pulse"></div>
        </div>

        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,
              backgroundSize: "50px 50px",
            }}
          ></div>
        </div>

        {/* 大型光效 - 更新为新配色 */}
        <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-gradient-radial from-[#4ecdc4]/10 via-transparent to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-gradient-radial from-[#f39c12]/10 via-transparent to-transparent rounded-full blur-3xl"></div>
      </div>

      {/* 卡通装饰元素 */}
      <div className="absolute inset-0 z-5 pointer-events-none overflow-hidden">
        {/* 飞行的蜜蜂 */}
        <div
          className="absolute top-20 left-1/4 animate-bounce"
          style={{ animationDuration: "3s" }}
        >
          <CartoonBee className="opacity-80" scale={1.2} />
        </div>

        {/* 飞船 */}
        <div
          className="absolute top-1/3 right-10 animate-pulse"
          style={{ animationDuration: "4s" }}
        >
          <CartoonSpaceship className="opacity-70" scale={0.8} />
        </div>

        {/* 机器人 */}
        <div
          className="absolute bottom-32 left-10 animate-bounce"
          style={{ animationDuration: "2.5s" }}
        >
          <CartoonRobot className="opacity-75" scale={0.9} />
        </div>

        {/* 云朵装饰 */}
        <div
          className="absolute top-10 right-1/4 animate-pulse"
          style={{ animationDuration: "5s" }}
        >
          <CartoonCloud className="opacity-60" scale={0.7} />
        </div>

        <div
          className="absolute bottom-1/4 left-1/3 animate-pulse"
          style={{ animationDuration: "6s" }}
        >
          <CartoonCloud className="opacity-40" scale={0.5} />
        </div>

        {/* 闪烁星星 */}
        <div
          className="absolute top-1/4 left-10 animate-ping"
          style={{ animationDuration: "2s" }}
        >
          <CartoonStar className="opacity-80" scale={0.8} color="#00ff87" />
        </div>

        <div
          className="absolute top-1/2 right-1/3 animate-ping"
          style={{ animationDuration: "3s" }}
        >
          <CartoonStar className="opacity-70" scale={0.6} color="#60efff" />
        </div>

        <div
          className="absolute bottom-1/3 right-20 animate-ping"
          style={{ animationDuration: "2.5s" }}
        >
          <CartoonStar className="opacity-60" scale={0.7} color="#ff2e63" />
        </div>

        <div
          className="absolute top-3/4 left-1/2 animate-ping"
          style={{ animationDuration: "4s" }}
        >
          <CartoonStar className="opacity-50" scale={0.5} color="#FFD700" />
        </div>

        {/* 更多小蜜蜂 */}
        <div
          className="absolute bottom-1/2 right-1/4 animate-bounce"
          style={{ animationDuration: "3.5s", animationDelay: "1s" }}
        >
          <CartoonBee className="opacity-60" scale={0.7} />
        </div>

        {/* 小飞船 */}
        <div
          className="absolute top-2/3 left-1/5 animate-pulse"
          style={{ animationDuration: "3s", animationDelay: "0.5s" }}
        >
          <CartoonSpaceship className="opacity-50" scale={0.6} />
        </div>
      </div>

      {/* 极简标题栏 */}
      <header className="relative z-20 bg-[#1a1a2e]/90 backdrop-blur-sm border-b border-[#2a2a3e] py-4">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex items-center justify-start">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] flex items-center justify-center text-white text-sm font-bold shadow-lg">
                VG
              </div>
              <span className="text-white font-bold text-lg md:text-xl">
                VG蜂巢
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* 居中内容 */}
      <main className="relative z-10 flex-1 flex flex-col items-center justify-center text-center px-4 py-8 pt-24 md:pt-28 lg:pt-60">
        {/* 主标题区域 */}
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="mb-16 md:mb-20 relative"
        >
          {/* 标题装饰蜜蜂 */}
          <div
            className="absolute -left-16 top-1/2 transform -translate-y-1/2 animate-bounce hidden md:block"
            style={{ animationDuration: "2s" }}
          >
            <CartoonBee className="opacity-90" scale={0.8} />
          </div>
          <div
            className="absolute -right-16 top-1/2 transform -translate-y-1/2 animate-bounce hidden md:block"
            style={{ animationDuration: "2s", animationDelay: "1s" }}
          >
            <CartoonBee
              className="opacity-90 transform scale-x-[-1]"
              scale={0.8}
            />
          </div>

          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-1.5 h-12 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-black text-white drop-shadow-2xl font-heading leading-tight">
              VG蜂巢
            </h1>
            <div className="w-1.5 h-12 rounded-full bg-gradient-to-b from-[#f39c12] to-[#4ecdc4] animate-pulse" />
          </div>
          <div className="text-lg md:text-2xl lg:text-3xl font-bold text-[#4ecdc4] drop-shadow-lg font-heading">
            ✨ 創造你的規則，釋放創作價值 ✨
          </div>
        </motion.section>

        {/* CTA按钮区域 */}
        <motion.section
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
          className="mb-20 md:mb-24"
        >
          <div className="flex flex-col items-center gap-6 md:gap-8">
            {/* 主CTA按钮 */}
            <div className="relative">
              {/* 发光背景效果 */}
              <div className="absolute inset-0 bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] rounded-2xl blur-xl opacity-30 animate-pulse scale-110"></div>

              <Link href={user ? "/dashboard" : "/auth"}>
                <Button
                  size="lg"
                  className="relative px-12 md:px-16 py-7 md:py-8 rounded-2xl bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] hover:from-[#6ee7de] hover:to-[#f5b041] text-white text-xl md:text-2xl lg:text-3xl font-black shadow-2xl transform hover:scale-110 hover:shadow-[0_20px_40px_rgba(78,205,196,0.3)] transition-all duration-300 group"
                  style={{
                    textShadow: "2px 2px 0px rgba(0,0,0,0.3)",
                  }}
                >
                  <span className="flex items-center gap-3">
                    <span className="group-hover:animate-bounce">🌟</span>
                    在VG上開始！
                    <span className="group-hover:animate-bounce">🌟</span>
                  </span>

                  {/* 内部发光效果 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 rounded-2xl"></div>
                </Button>
              </Link>
            </div>

            {/* 副标题引导 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="text-center space-y-3"
            >
              <div className="flex items-center gap-3 text-[#00ff87] font-bold text-lg md:text-xl">
                <span className="animate-ping">⚡</span>
                <span>只需 1 分鐘即可加入創作者！</span>
                <span className="animate-ping">⚡</span>
              </div>
              <div className="flex items-center justify-center gap-3 text-gray-400 font-medium text-sm md:text-base">
                <span className="text-[#60efff]">✅</span>
                <span>免費體驗</span>
                <span className="text-[#60efff]">•</span>
                <span>立即開始</span>
                <span className="text-[#60efff]">•</span>
                <span>無需信用卡</span>
                <span className="text-[#60efff]">✅</span>
              </div>
            </motion.div>
          </div>
        </motion.section>

        {/* 价值主张区域 */}
        <motion.section
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
          className="mb-28 md:mb-32"
        >
          <div className="bg-[#1a1a2e]/95 backdrop-blur-sm rounded-2xl p-8 md:p-12 lg:p-16 border border-[#2a2a3e] shadow-2xl max-w-4xl lg:max-w-6xl relative">
            {/* 装饰星星 */}
            <div
              className="absolute -top-4 -left-4 animate-ping"
              style={{ animationDuration: "2s" }}
            >
              <CartoonStar scale={0.7} color="#00ff87" />
            </div>
            <div
              className="absolute -top-4 -right-4 animate-ping"
              style={{ animationDuration: "2s", animationDelay: "1s" }}
            >
              <CartoonStar scale={0.7} color="#60efff" />
            </div>

            <div className="text-base md:text-lg lg:text-xl text-white font-semibold leading-relaxed">
              <p className="text-[#00ff87] font-bold text-2xl md:text-3xl lg:text-4xl mb-12 text-center">
                ✨ 為何選擇 VG？
              </p>

              <div className="space-y-6 md:space-y-8">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  className="bg-gradient-to-r from-[#ff2e63]/10 to-transparent p-6 md:p-8 rounded-xl border-l-4 border-[#ff2e63] hover:from-[#ff2e63]/20 transition-all duration-300"
                >
                  <div className="flex items-center gap-4 md:gap-6">
                    <span className="text-3xl md:text-4xl">🚫</span>
                    <span className="text-lg md:text-xl lg:text-2xl">
                      在這裡，你不再依賴流量與廣告算法
                    </span>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="bg-gradient-to-l from-[#00ff87]/10 to-transparent p-6 md:p-8 rounded-xl border-r-4 border-[#00ff87] hover:from-[#00ff87]/20 transition-all duration-300"
                >
                  <div className="flex items-center gap-4 md:gap-6 justify-end">
                    <span className="text-lg md:text-xl lg:text-2xl">
                      展示工作賺取可持續收入
                    </span>
                    <span className="text-3xl md:text-4xl">💡</span>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                  className="bg-gradient-to-r from-[#60efff]/10 to-transparent p-6 md:p-8 rounded-xl border-l-4 border-[#60efff] hover:from-[#60efff]/20 transition-all duration-300"
                >
                  <div className="flex items-center gap-4 md:gap-6">
                    <span className="text-3xl md:text-4xl">🎨</span>
                    <span className="text-lg md:text-xl lg:text-2xl">
                      自由創作你真正熱愛的內容
                    </span>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  className="bg-gradient-to-l from-[#FFD700]/10 to-transparent p-6 md:p-8 rounded-xl border-r-4 border-[#FFD700] hover:from-[#FFD700]/20 transition-all duration-300"
                >
                  <div className="flex items-center gap-4 md:gap-6 justify-end">
                    <span className="text-lg md:text-xl lg:text-2xl">
                      以最純粹的形式建立持久的業務
                    </span>
                    <span className="text-3xl md:text-4xl">🌐</span>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.section>

        {/* 核心功能展示区域 */}
        <motion.section
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
          className="w-full max-w-6xl lg:max-w-7xl mb-24 md:mb-28"
        >
          {/* 突出显示的标题区域 */}
          <div className="relative mb-20">
            {/* 背景高亮效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00ff87]/5 to-transparent rounded-3xl blur-xl"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-[#60efff]/5 via-transparent to-[#ff2e63]/5 rounded-3xl"></div>

            <div className="relative bg-[#1a1a2e]/90 backdrop-blur-sm rounded-3xl p-8 md:p-12 lg:p-16 border border-[#00ff87]/30 shadow-2xl text-center">
              {/* 动画徽章 */}
              <div className="flex justify-center mb-8">
                <div className="relative">
                  <div className="bg-gradient-to-r from-[#00ff87] to-[#60efff] text-black px-8 py-4 rounded-full font-bold text-lg md:text-xl flex items-center gap-3 shadow-lg">
                    <span className="animate-pulse">💰</span>
                    <span>USD實時獎勵</span>
                    <span className="animate-pulse">💰</span>
                  </div>
                  {/* 闪烁效果 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-[#00ff87] to-[#60efff] rounded-full opacity-30 animate-ping scale-110"></div>
                </div>
              </div>

              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
                <span className="text-[#00ff87]">🚀</span> 全方位創作支持{" "}
                <span className="text-[#60efff]">🚀</span>
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 font-medium">
                從創作到變現，我們為你提供完整的平台生態
              </p>

              {/* 装饰性动画元素 */}
              <div
                className="absolute top-6 left-10 animate-bounce opacity-60"
                style={{ animationDuration: "2s" }}
              >
                <CartoonStar scale={0.6} color="#00ff87" />
              </div>
              <div
                className="absolute top-6 right-10 animate-bounce opacity-60"
                style={{ animationDuration: "2.5s", animationDelay: "0.5s" }}
              >
                <CartoonStar scale={0.6} color="#60efff" />
              </div>
              <div
                className="absolute bottom-6 left-1/4 animate-ping opacity-40"
                style={{ animationDuration: "3s" }}
              >
                <CartoonStar scale={0.5} color="#ff2e63" />
              </div>
              <div
                className="absolute bottom-6 right-1/4 animate-ping opacity-40"
                style={{ animationDuration: "2.8s", animationDelay: "1s" }}
              >
                <CartoonStar scale={0.5} color="#FFD700" />
              </div>
            </div>
          </div>

          {/* 功能卡片网格 - 响应式布局 */}
          <div className="space-y-8">
            {/* 第一排：3个主要功能 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 relative">
              {/* 卡片装饰元素 */}
              <div
                className="absolute -top-12 left-1/4 animate-bounce hidden lg:block"
                style={{ animationDuration: "3s" }}
              >
                <CartoonRobot scale={0.6} className="opacity-60" />
              </div>
              <div
                className="absolute -bottom-12 right-1/4 animate-pulse hidden lg:block"
                style={{ animationDuration: "4s" }}
              >
                <CartoonSpaceship scale={0.5} className="opacity-50" />
              </div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="bg-gradient-to-br from-[#1a1a2e]/95 to-[#2a2a3e]/95 backdrop-blur-sm rounded-2xl p-6 md:p-8 lg:p-10 border border-[#2a2a3e] shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:border-[#00ff87]/40 group"
              >
                <div className="text-4xl md:text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  🫅
                </div>
                <h3 className="text-white font-bold text-xl md:text-2xl lg:text-3xl mb-4 font-heading">
                  照自己的方式做
                </h3>
                <p className="text-gray-400 text-base md:text-lg leading-relaxed">
                  開啟影片創作者職業生涯，完全按照你的節奏和風格
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.9 }}
                className="bg-gradient-to-br from-[#1a1a2e]/95 to-[#2a2a3e]/95 backdrop-blur-sm rounded-2xl p-6 md:p-8 lg:p-10 border border-[#2a2a3e] shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:border-[#60efff]/40 group"
              >
                <div className="text-4xl md:text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  🧑‍🤝‍🧑
                </div>
                <h3 className="text-white font-bold text-xl md:text-2xl lg:text-3xl mb-4 font-heading">
                  建立真正的社區
                </h3>
                <p className="text-gray-400 text-base md:text-lg leading-relaxed">
                  把你的觀眾變成朋友，建立深度連結的創作者社群
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.0 }}
                className="bg-gradient-to-br from-[#1a1a2e]/95 to-[#2a2a3e]/95 backdrop-blur-sm rounded-2xl p-6 md:p-8 lg:p-10 border border-[#2a2a3e] shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:border-[#ff2e63]/40 group"
              >
                <div className="text-4xl md:text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  🐣
                </div>
                <h3 className="text-white font-bold text-xl md:text-2xl lg:text-3xl mb-4 font-heading">
                  擴大您的影響力
                </h3>
                <p className="text-gray-400 text-base md:text-lg leading-relaxed">
                  需要時提供協助，釋放成長力，擴展你的創作影響範圍
                </p>
              </motion.div>
            </div>

            {/* 第二排：2个支持功能 - 居中显示 */}
            <div className="flex justify-center">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 max-w-4xl w-full">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.1 }}
                  className="bg-gradient-to-br from-[#1a1a2e]/95 to-[#2a2a3e]/95 backdrop-blur-sm rounded-2xl p-6 md:p-8 lg:p-10 border border-[#2a2a3e] shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:border-[#FFD700]/40 group"
                >
                  <div className="text-4xl md:text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    🛡️
                  </div>
                  <h3 className="text-white font-bold text-xl md:text-2xl lg:text-3xl mb-4 font-heading">
                    獲得業務支持
                  </h3>
                  <p className="text-gray-400 text-base md:text-lg leading-relaxed">
                    安全的付款方式保障創作者收益，全方位業務支持
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                  className="bg-gradient-to-br from-[#1a1a2e]/95 to-[#2a2a3e]/95 backdrop-blur-sm rounded-2xl p-6 md:p-8 lg:p-10 border border-[#2a2a3e] shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:border-[#00ff87]/40 group"
                >
                  <div className="text-4xl md:text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    ⚡
                  </div>
                  <h3 className="text-white font-bold text-xl md:text-2xl lg:text-3xl mb-4 font-heading">
                    輕鬆創收
                  </h3>
                  <p className="text-gray-400 text-base md:text-lg leading-relaxed">
                    激發你的靈感，獲得多種收入來源，讓創作變現更簡單
                  </p>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.section>

        {/* 創作理念區塊 */}
        <motion.section
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
          className="mb-20"
        >
          <div className="bg-gradient-to-br from-[#1a1a2e]/95 to-[#2a2a3e]/95 backdrop-blur-sm rounded-2xl p-8 md:p-12 lg:p-16 border border-[#2a2a3e] shadow-2xl max-w-5xl lg:max-w-6xl relative">
            {/* 装饰元素 */}
            <div
              className="absolute -top-6 left-1/2 transform -translate-x-1/2 animate-bounce hidden md:block"
              style={{ animationDuration: "3s" }}
            >
              <CartoonStar scale={1.2} color="#FFD700" />
            </div>
            <div
              className="absolute -bottom-6 -left-6 animate-bounce hidden md:block"
              style={{ animationDuration: "2.5s" }}
            >
              <CartoonBee scale={0.8} className="opacity-70" />
            </div>
            <div
              className="absolute -bottom-6 -right-6 animate-pulse hidden md:block"
              style={{ animationDuration: "4s" }}
            >
              <CartoonSpaceship scale={0.7} className="opacity-60" />
            </div>

            <div className="text-center space-y-8 md:space-y-10">
              <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#60efff] mb-8">
                ⚙️ 創造你的世界
              </div>
              <div className="text-lg md:text-xl lg:text-2xl text-gray-300 leading-relaxed space-y-6 md:space-y-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                  className="flex items-center justify-center gap-4 md:gap-6"
                >
                  <span className="text-2xl md:text-3xl">🔍</span>
                  <span>對未知的渴望，是創造的起點</span>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.1 }}
                  className="flex items-center justify-center gap-4 md:gap-6"
                >
                  <span className="text-2xl md:text-3xl">💎</span>
                  <span>將熱情轉化為價值，讓創作成為你的長期資產</span>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                  className="text-[#00ff87] font-bold text-xl md:text-2xl lg:text-3xl mt-8 md:mt-10 p-6 md:p-8 bg-[#00ff87]/10 rounded-xl border border-[#00ff87]/30"
                >
                  <span className="text-3xl md:text-4xl mr-3 md:mr-4">⏱️</span>
                  主宰自己的時間軸——你的世界，由你定義
                </motion.div>
              </div>
            </div>
          </div>
        </motion.section>

        {/* 底部间距 */}
        <div className="h-12 md:h-20"></div>
      </main>

      {/* 页脚 */}
      <footer className="relative z-10 bg-[#1a1a2e]/80 backdrop-blur-sm border-t border-[#2a2a3e] py-6 md:py-8">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4 md:gap-6">
            {/* 左侧：版权信息 */}
            <div className="text-center md:text-left">
              <div className="flex items-center justify-center md:justify-start gap-2 mb-2">
                <div className="w-6 h-6 rounded-full bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] flex items-center justify-center text-white text-xs font-bold">
                  VG
                </div>
                <span className="text-white font-semibold text-sm md:text-base">
                  VG蜂巢
                </span>
              </div>
              <p className="text-gray-400 text-xs md:text-sm">
                © 2024 VG蜂巢. 保留所有權利.
              </p>
            </div>

            {/* 右侧：导航链接 */}
            <div className="flex items-center gap-6 md:gap-8">
              {NAV_ITEMS.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="text-gray-400 hover:text-[#4ecdc4] transition-colors duration-200 text-sm md:text-base font-medium hover:scale-105 transform"
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
