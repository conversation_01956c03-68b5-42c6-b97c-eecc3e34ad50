"use client";

import { SignIn } from "@clerk/nextjs";

export default function AuthForm() {
  return (
    <div className="flex justify-center w-full">
      <SignIn
        appearance={{
          elements: {
            formButtonPrimary:
              "bg-gradient-to-r from-coin-purple to-coin-purple-dark hover:from-coin-purple-dark hover:to-coin-purple transition-all duration-200",
            card: "bg-white shadow-xl",
            headerTitle: "text-2xl font-bold text-gray-900",
            headerSubtitle: "text-gray-600",
            socialButtonsBlockButton:
              "border-2 border-gray-200 hover:border-coin-purple transition-colors duration-200",
            socialButtonsBlockButtonText: "text-gray-700 font-medium",
            dividerLine: "bg-gray-200",
            dividerText: "text-gray-500",
            formFieldInput:
              "border-2 border-gray-200 focus:border-coin-purple transition-colors duration-200",
            formFieldLabel: "text-gray-700 font-medium",
            footerActionLink:
              "text-coin-purple hover:text-coin-purple-dark transition-colors duration-200",
          },
        }}
        fallbackRedirectUrl="/dashboard"
        forceRedirectUrl="/dashboard"
        signUpFallbackRedirectUrl="/dashboard"
        signUpUrl="/auth"
        signInUrl="/auth"
      />
    </div>
  );
}
