"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  Youtube,
  Users,
  Eye,
  Calendar,
  MessageSquare,
  Mail,
  ChevronRight,
  Star,
  Trophy,
  Sparkles,
  ArrowLeft,
  ExternalLink,
  Play,
  ThumbsUp,
  Clock,
} from "lucide-react";
import { motion } from "framer-motion";
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";
import type { Engine } from "tsparticles-engine";
import type { ISourceOptions } from "tsparticles-engine";
import type { Profile } from "@/lib/schema";
import ServicesDisplay from "@/components/services/ServicesDisplay";

// 粒子配置
const particlesConfig: ISourceOptions = {
  particles: {
    number: {
      value: 30,
      density: {
        enable: true,
        value_area: 800,
      },
    },
    color: {
      value: ["#00ff87", "#60efff", "#ff2e63"],
    },
    shape: {
      type: "circle",
    },
    opacity: {
      value: 0.3,
      animation: {
        enable: true,
        speed: 1,
      },
    },
    size: {
      value: 2,
      animation: {
        enable: true,
        speed: 2,
      },
    },
    links: {
      enable: true,
      distance: 120,
      color: "#00ff87",
      opacity: 0.1,
      width: 1,
    },
    move: {
      enable: true,
      speed: 1,
      direction: "none",
      random: true,
      straight: false,
      outModes: "out",
    },
  },
  interactivity: {
    events: {
      onHover: {
        enable: true,
        mode: "grab",
      },
    },
    modes: {
      grab: {
        distance: 100,
        links: {
          opacity: 0.5,
        },
      },
    },
  },
  detectRetina: true,
};

// 游戏风格按钮组件
const GameButton = ({
  children,
  onClick,
  variant = "primary",
  className = "",
  disabled = false,
}: {
  children: React.ReactNode;
  onClick: () => void;
  variant?: "primary" | "secondary";
  className?: string;
  disabled?: boolean;
}) => (
  <motion.button
    whileHover={!disabled ? { scale: 1.05, y: -2 } : {}}
    whileTap={!disabled ? { scale: 0.95 } : {}}
    onClick={onClick}
    disabled={disabled}
    className={`
      relative px-6 py-3 rounded-lg font-medium text-white
      ${
        variant === "primary"
          ? "bg-gradient-to-r from-[#00ff87] to-[#60efff] shadow-lg shadow-[#00ff87]/30"
          : "bg-[#1a1a2e]/80 border border-[#2a2a3e] backdrop-blur-sm"
      }
      ${disabled ? "opacity-50 cursor-not-allowed" : "overflow-hidden group"}
      ${className}
    `}
  >
    {!disabled && (
      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
    )}
    {children}
  </motion.button>
);

// 视频数据类型
interface VideoData {
  id: string;
  thumbnail: string;
  title: string;
  views: number;
  likes: number;
  duration: string;
  publishedAt: string;
  status: string;
  url: string;
}

export default function CreatorPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params?.userId as string;

  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showContactModal, setShowContactModal] = useState(false);
  const [videos, setVideos] = useState<VideoData[]>([]);
  const [videosLoading, setVideosLoading] = useState(false);

  const particlesInit = async (engine: Engine) => {
    await loadSlim(engine);
  };

  // 模拟视频数据
  const mockVideos: VideoData[] = [
    {
      id: "1",
      thumbnail: "https://img.youtube.com/vi/dxj5U2i-w28/maxresdefault.jpg",
      title: "Web3 创作者经济新机遇 - 区块链技术如何改变内容创作",
      views: 125400,
      likes: 3210,
      duration: "10:25",
      publishedAt: "2024-03-15",
      status: "已发布",
      url: "https://youtu.be/dxj5U2i-w28",
    },
    {
      id: "2",
      thumbnail: "https://img.youtube.com/vi/bsz2dVIRtrM/maxresdefault.jpg",
      title: "创作者直播：NFT 创作分享与问答",
      views: 89600,
      likes: 2543,
      duration: "1:15:30",
      publishedAt: "2024-03-10",
      status: "已发布",
      url: "https://www.youtube.com/live/bsz2dVIRtrM",
    },
    {
      id: "3",
      thumbnail: "https://img.youtube.com/vi/8qKtDG4QFVI/maxresdefault.jpg",
      title: "区块链技术入门指南 - 从零开始学习加密货币",
      views: 234800,
      likes: 5892,
      duration: "12:45",
      publishedAt: "2024-03-05",
      status: "已发布",
      url: "https://youtu.be/8qKtDG4QFVI",
    },
    {
      id: "4",
      thumbnail: "https://img.youtube.com/vi/example1/maxresdefault.jpg",
      title: "DeFi 投资策略分析 - 如何在去中心化金融中获利",
      views: 67200,
      likes: 1876,
      duration: "8:32",
      publishedAt: "2024-02-28",
      status: "已发布",
      url: "https://youtu.be/example1",
    },
    {
      id: "5",
      thumbnail: "https://img.youtube.com/vi/example2/maxresdefault.jpg",
      title: "元宇宙项目深度解析 - 未来虚拟世界的投资机会",
      views: 156700,
      likes: 4321,
      duration: "15:18",
      publishedAt: "2024-02-20",
      status: "已发布",
      url: "https://youtu.be/example2",
    },
    {
      id: "6",
      thumbnail: "https://img.youtube.com/vi/example3/maxresdefault.jpg",
      title: "智能合约开发教程 - Solidity 编程基础",
      views: 98300,
      likes: 2765,
      duration: "22:14",
      publishedAt: "2024-02-15",
      status: "已发布",
      url: "https://youtu.be/example3",
    },
  ];

  // 获取用户公开资料
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/creator/${userId}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError("创作者不存在");
          } else {
            setError("无法加载创作者信息");
          }
          return;
        }

        const profileData = await response.json();
        setProfile(profileData);
      } catch (err) {
        setError("无法加载创作者信息");
        console.error("Error fetching profile:", err);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchProfile();
    }
  }, [userId]);

  // 模拟获取视频数据
  useEffect(() => {
    const fetchVideos = async () => {
      if (!profile) return;

      setVideosLoading(true);
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setVideos(mockVideos);
      setVideosLoading(false);
    };

    fetchVideos();
  }, [profile]);

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return "1天前";
    if (diffDays < 30) return `${diffDays}天前`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`;
    return `${Math.floor(diffDays / 365)}年前`;
  };

  // 计算加入天数
  const getJoinDays = (joinDate: Date) => {
    if (!joinDate) return "N/A";

    if (joinDate instanceof Date) {
      console.log("joinDate", joinDate);
    } else {
      joinDate = new Date(joinDate);
      console.log("joinDate", joinDate);
    }

    const now = new Date();
    const diff = now.getTime() - joinDate.getTime();
    return Math.floor(diff / (1000 * 60 * 60 * 24));
  };

  // 模拟合作方式数据
  const collaborationData = {
    bySession: {
      enabled: true,
      price: 50,
      description: "单次合作，适合产品推广或内容植入",
    },
    byMonth: {
      enabled: true,
      price: 500,
      description: "包月合作，适合长期品牌合作",
    },
  };

  if (loading) {
    return (
      <div className="min-h-screen w-full bg-[#0a0a0f] flex items-center justify-center">
        <div className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl px-8 py-6 shadow-lg">
          <div className="text-white text-2xl font-bold animate-pulse flex items-center gap-3">
            <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#00ff87] to-[#60efff] animate-pulse" />
            🚀 加载创作者信息...
          </div>
        </div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="min-h-screen w-full bg-[#0a0a0f] flex items-center justify-center">
        <div className="bg-[#1a1a2e]/95 backdrop-blur-sm rounded-xl p-8 border border-[#ff2e63]/50 text-center shadow-lg max-w-md">
          <div className="text-[#ff2e63] text-xl font-bold mb-4">
            ⚠️ {error || "创作者不存在"}
          </div>
          <GameButton onClick={() => router.push("/")} variant="secondary">
            返回首页
          </GameButton>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-[#0a0a0f] relative overflow-hidden">
      {/* 深色科技背景 */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#0a0a0f]" />

        {/* 科技装饰性元素 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-20 w-48 h-1 bg-gradient-to-r from-[#00ff87] to-transparent animate-pulse"></div>
          <div className="absolute top-1/3 right-1/4 w-1 h-48 bg-gradient-to-b from-[#60efff] to-transparent animate-pulse"></div>
          <div className="absolute bottom-20 left-1/4 w-64 h-1 bg-gradient-to-r from-[#ff2e63] to-transparent animate-pulse"></div>
          <div className="absolute top-1/2 right-10 w-1 h-40 bg-gradient-to-b from-[#00ff87] to-transparent animate-pulse"></div>
        </div>

        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,
              backgroundSize: "50px 50px",
            }}
          ></div>
        </div>
      </div>

      {/* 粒子背景 */}
      <Particles
        id="tsparticles"
        init={particlesInit}
        options={particlesConfig}
        className="absolute inset-0 z-0"
      />

      {/* 顶部导航 */}
      <header className="relative z-10 w-full flex items-center justify-between px-8 py-4 bg-[#1a1a2e]/95 backdrop-blur-sm border-b border-[#2a2a3e]">
        <GameButton onClick={() => router.back()} variant="secondary">
          <div className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            返回
          </div>
        </GameButton>

        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-[#00ff87] to-[#60efff] rounded-lg flex items-center justify-center text-white font-bold text-xl shadow-lg">
            🎮
          </div>
          <span className="text-white font-bold text-xl tracking-wide font-heading">
            创作者主页
          </span>
        </div>

        <GameButton onClick={() => setShowContactModal(true)} variant="primary">
          <div className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            联系合作
          </div>
        </GameButton>
      </header>

      {/* 主要内容 */}
      <main className="relative z-10 container mx-auto px-4 py-8">
        {/* 创作者信息卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-4xl mx-auto mb-8"
        >
          <div className="bg-[#1a1a2e]/95 backdrop-blur-sm rounded-xl p-8 border border-[#2a2a3e] shadow-xl">
            <div className="flex flex-col md:flex-row gap-8">
              {/* 头像和基本信息 */}
              <div className="flex flex-col items-center">
                <div className="relative">
                  <div className="w-32 h-32 rounded-xl overflow-hidden border-4 border-[#00ff87] shadow-lg shadow-[#00ff87]/30">
                    <img
                      src={profile.avatar_url || "/default-avatar.png"}
                      alt={profile.full_name || "Creator"}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {profile.is_premium_member && (
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-[#ff2e63] to-[#ff6b9d] rounded-full flex items-center justify-center text-white font-bold text-sm">
                      💎
                    </div>
                  )}
                </div>

                <div className="text-center mt-4">
                  <h1 className="text-3xl font-black text-white mb-2 font-heading">
                    {profile.full_name}
                  </h1>
                  <div className="flex items-center gap-2 justify-center mb-2">
                    <span className="text-[#00ff87] font-semibold">
                      Lv.{profile.level}
                    </span>
                    <span className="text-gray-400">•</span>
                    <span className="text-gray-400">
                      #{profile.user_numeric_id?.toString().padStart(6, "0")}
                    </span>
                  </div>
                  <div className="text-gray-400 text-sm">
                    加入 {getJoinDays(profile.created_at)} 天
                  </div>
                </div>
              </div>

              {/* 详细信息 */}
              <div className="flex-1">
                {/* 个人简介 */}
                {profile.motto && (
                  <div className="mb-6">
                    <h3 className="text-white font-bold text-lg mb-2 flex items-center gap-2">
                      <Star className="w-5 h-5 text-[#00ff87]" />
                      个人简介
                    </h3>
                    <p className="text-gray-300 leading-relaxed italic">
                      "{profile.motto}"
                    </p>
                  </div>
                )}

                {/* YouTube 数据 */}
                <div className="mb-6">
                  <h3 className="text-white font-bold text-lg mb-4 flex items-center gap-2">
                    <Youtube className="w-5 h-5 text-[#ff0000]" />
                    YouTube 频道数据
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-[#ff0000]/10 rounded-lg p-4 border border-[#ff0000]/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="w-5 h-5 text-[#ff0000]" />
                        <span className="text-white/60 text-sm">订阅者</span>
                      </div>
                      <div className="text-2xl font-bold text-white">
                        {formatNumber(profile.subscribers)}
                      </div>
                    </div>

                    <div className="bg-[#60efff]/10 rounded-lg p-4 border border-[#60efff]/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Eye className="w-5 h-5 text-[#60efff]" />
                        <span className="text-white/60 text-sm">总播放量</span>
                      </div>
                      <div className="text-2xl font-bold text-white">
                        {formatNumber(profile.views)}
                      </div>
                    </div>

                    <div className="bg-[#00ff87]/10 rounded-lg p-4 border border-[#00ff87]/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Trophy className="w-5 h-5 text-[#00ff87]" />
                        <span className="text-white/60 text-sm">经验等级</span>
                      </div>
                      <div className="text-2xl font-bold text-white">
                        {profile.level}
                      </div>
                    </div>
                  </div>

                  {profile.youtube_title && (
                    <div className="mt-4 p-4 bg-[#2a2a3e]/50 rounded-lg border border-[#ff0000]/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-[#ff0000] text-sm font-semibold mb-1">
                            频道名称
                          </div>
                          <div className="text-white font-bold">
                            {profile.youtube_title}
                          </div>
                        </div>
                        <button
                          onClick={() =>
                            window.open(
                              `https://youtube.com/channel/${profile.youtube_id}`,
                              "_blank"
                            )
                          }
                          className="p-2 bg-[#ff0000]/20 rounded-lg hover:bg-[#ff0000]/30 transition-colors"
                        >
                          <ExternalLink className="w-5 h-5 text-[#ff0000]" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* 社交链接 */}
                {profile.social_links &&
                  Object.keys(profile.social_links).length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-white font-bold text-lg mb-4 flex items-center gap-2">
                        <MessageSquare className="w-5 h-5 text-[#60efff]" />
                        社交媒体
                      </h3>
                      <div className="flex gap-3">
                        {profile.social_links.twitter && (
                          <button
                            onClick={() =>
                              window.open(
                                `https://twitter.com/${profile.social_links?.twitter?.replace(
                                  "@",
                                  ""
                                )}`,
                                "_blank"
                              )
                            }
                            className="flex items-center gap-2 px-4 py-2 bg-[#1da1f2]/10 border border-[#1da1f2]/20 rounded-lg hover:bg-[#1da1f2]/20 transition-colors"
                          >
                            <div className="w-5 h-5 text-[#1da1f2]">𝕏</div>
                            <span className="text-white text-sm">
                              {profile.social_links.twitter}
                            </span>
                          </button>
                        )}
                        {profile.social_links.website && (
                          <button
                            onClick={() =>
                              window.open(
                                profile.social_links?.website,
                                "_blank"
                              )
                            }
                            className="flex items-center gap-2 px-4 py-2 bg-[#60efff]/10 border border-[#60efff]/20 rounded-lg hover:bg-[#60efff]/20 transition-colors"
                          >
                            <ExternalLink className="w-5 h-5 text-[#60efff]" />
                            <span className="text-white text-sm">官方网站</span>
                          </button>
                        )}
                      </div>
                    </div>
                  )}
              </div>
            </div>
          </div>
        </motion.div>

        {/* 服务与报价卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="max-w-4xl mx-auto mb-8"
        >
          <ServicesDisplay
            creatorId={userId}
            showContactButton={true}
            onContactClick={() => setShowContactModal(true)}
          />
        </motion.div>

        {/* 视频列表 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="max-w-4xl mx-auto mb-8"
        >
          <div className="bg-[#1a1a2e]/95 backdrop-blur-sm rounded-xl p-8 border border-[#2a2a3e] shadow-xl">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
              <Play className="w-6 h-6 text-[#ff0000]" />
              最新视频作品
            </h2>

            {videosLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="text-white text-lg animate-pulse flex items-center gap-3">
                  <div className="w-2 h-8 rounded-full bg-gradient-to-b from-[#ff0000] to-[#ff6b9d] animate-pulse" />
                  加载视频中...
                </div>
              </div>
            ) : videos.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {videos.map((video) => (
                  <motion.div
                    key={video.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className="group cursor-pointer"
                    onClick={() => window.open(video.url, "_blank")}
                  >
                    <div className="bg-[#2a2a3e]/50 rounded-xl overflow-hidden border border-[#2a2a3e] hover:border-[#ff0000]/30 transition-all duration-300 transform hover:scale-[1.02]">
                      {/* 视频缩略图 */}
                      <div className="relative aspect-video overflow-hidden">
                        <img
                          src={video.thumbnail}
                          alt={video.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        {/* 播放按钮覆盖层 */}
                        <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                          <div className="w-16 h-16 bg-[#ff0000]/80 rounded-full flex items-center justify-center">
                            <Play
                              className="w-8 h-8 text-white ml-1"
                              fill="white"
                            />
                          </div>
                        </div>
                        {/* 时长标签 */}
                        <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                          {video.duration}
                        </div>
                      </div>

                      {/* 视频信息 */}
                      <div className="p-4">
                        <h3 className="text-white font-semibold text-sm line-clamp-2 mb-3 group-hover:text-[#ff0000] transition-colors">
                          {video.title}
                        </h3>

                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                              <Eye className="w-3 h-3" />
                              <span>{formatNumber(video.views)}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <ThumbsUp className="w-3 h-3" />
                              <span>{formatNumber(video.likes)}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            <span>{formatDate(video.publishedAt)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 text-lg mb-2">暂无视频内容</div>
                <div className="text-gray-500 text-sm">
                  创作者还没有发布视频作品
                </div>
              </div>
            )}

            {/* 查看更多按钮 */}
            {videos.length > 0 && (
              <div className="text-center mt-8">
                <button
                  onClick={() =>
                    window.open(
                      `https://youtube.com/channel/${profile.youtube_id}`,
                      "_blank"
                    )
                  }
                  className="px-6 py-3 bg-[#ff0000]/10 border border-[#ff0000]/20 rounded-lg text-[#ff0000] font-semibold hover:bg-[#ff0000]/20 transition-all duration-300 flex items-center gap-2 mx-auto"
                >
                  <Youtube className="w-5 h-5" />
                  查看更多视频
                  <ExternalLink className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </motion.div>

        {/* 联系合作按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="max-w-4xl mx-auto text-center"
        >
          <GameButton
            onClick={() => setShowContactModal(true)}
            variant="primary"
            className="px-12 py-4 text-xl"
          >
            <div className="flex items-center gap-3">
              <MessageSquare className="w-6 h-6" />
              开始合作
              <ChevronRight className="w-5 h-5" />
            </div>
          </GameButton>

          <p className="text-gray-400 mt-4">点击联系创作者讨论合作详情</p>
        </motion.div>
      </main>

      {/* 联系合作弹窗 */}
      {showContactModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-[#1a1a2e] rounded-xl p-8 w-full max-w-md border border-[#00ff87]/30 relative shadow-2xl"
          >
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
              <MessageSquare className="w-6 h-6 text-[#00ff87]" />
              联系创作者
            </h3>

            <div className="space-y-4 mb-6">
              <p className="text-gray-300">
                选择合作方式，我们将为您建立联系：
              </p>

              <div className="space-y-3">
                {collaborationData.bySession.enabled && (
                  <button className="w-full p-4 bg-[#00ff87]/10 border border-[#00ff87]/20 rounded-lg hover:bg-[#00ff87]/20 transition-all text-left">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="text-white font-semibold">按次合作</div>
                        <div className="text-[#00ff87] text-sm">
                          ${collaborationData.bySession.price} USD/次
                        </div>
                      </div>
                      <ChevronRight className="w-5 h-5 text-[#00ff87]" />
                    </div>
                  </button>
                )}

                {collaborationData.byMonth.enabled && (
                  <button className="w-full p-4 bg-[#60efff]/10 border border-[#60efff]/20 rounded-lg hover:bg-[#60efff]/20 transition-all text-left">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="text-white font-semibold">包月合作</div>
                        <div className="text-[#60efff] text-sm">
                          ${collaborationData.byMonth.price} USD/月
                        </div>
                      </div>
                      <ChevronRight className="w-5 h-5 text-[#60efff]" />
                    </div>
                  </button>
                )}
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <GameButton
                onClick={() => setShowContactModal(false)}
                variant="secondary"
              >
                取消
              </GameButton>
              <GameButton
                onClick={() => {
                  // TODO: 实现联系功能
                  alert("联系功能待实现");
                  setShowContactModal(false);
                }}
                variant="primary"
              >
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  发起联系
                </div>
              </GameButton>
            </div>

            <button
              className="absolute top-4 right-4 text-white/60 hover:text-white text-2xl"
              onClick={() => setShowContactModal(false)}
            >
              ×
            </button>
          </motion.div>
        </div>
      )}
    </div>
  );
}
