import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { clerkClient } from "@clerk/nextjs/server";
import { ProfileService } from "@/services/profileService";
import type { NewProfile, ExternalAccounts } from "@/lib/schema";

function constructFullName(
  firstName?: string | null,
  lastName?: string | null
): string | undefined {
  if (!firstName && !lastName) return undefined;
  return [firstName, lastName].filter(Boolean).join(" ");
}

function processExternalAccounts(
  externalAccounts?: Array<{
    provider: string;
    emailAddress?: string | null;
    username?: string | null;
    externalId: string;
  }>
): ExternalAccounts {
  if (!externalAccounts || externalAccounts.length === 0) return {};

  const processed: ExternalAccounts = {};
  externalAccounts.forEach((account) => {
    processed[account.provider] = {
      id: account.externalId,
      email: account.emailAddress || undefined,
      username: account.username || undefined,
      verified: true,
    };
  });

  return processed;
}

export async function POST(request: NextRequest) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 检查用户是否已经存在于数据库中
    const existingProfile = await ProfileService.getProfile(clerkUserId);

    if (existingProfile) {
      return NextResponse.json({
        success: true,
        message: "Profile already exists",
        profile: existingProfile,
      });
    }

    // 获取 Clerk 用户详细信息
    const clerkUser = await (await clerkClient()).users.getUser(clerkUserId);

    if (!clerkUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    console.log("clerkUser", clerkUser);

    const primaryEmail =
      clerkUser.emailAddresses.find(
        (email) => email.verification?.status === "verified"
      ) || clerkUser.emailAddresses[0];

    if (!primaryEmail) {
      return NextResponse.json(
        { error: "No email found for user" },
        { status: 400 }
      );
    }

    const profileData: NewProfile = {
      clerk_user_id: clerkUser.id,
      email: primaryEmail.emailAddress,
      clerk_username: clerkUser.username || undefined,
      full_name: constructFullName(clerkUser.firstName, clerkUser.lastName),
      avatar_url: clerkUser.imageUrl || undefined,
      email_verified: primaryEmail.verification?.status === "verified" || false,
      external_accounts: processExternalAccounts(clerkUser.externalAccounts),
      clerk_created_at: new Date(clerkUser.createdAt),
      clerk_updated_at: new Date(clerkUser.updatedAt),
    };

    // 创建新用户资料
    const numericId = await ProfileService.generateUniqueNumericId();
    profileData.user_numeric_id = numericId;

    const newProfile = await ProfileService.createProfile(profileData);
    console.log(`Created profile for user: ${clerkUser.id}`);

    return NextResponse.json({
      success: true,
      message: "Profile created successfully",
      profile: newProfile,
    });
  } catch (error) {
    console.error("Profile sync error:", error);
    return NextResponse.json(
      { error: "Failed to sync profile" },
      { status: 500 }
    );
  }
}
