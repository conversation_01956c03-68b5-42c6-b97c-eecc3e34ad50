import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { ProfileService } from "@/services/profileService";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    const { userId } = await params;

    // Verify the user is requesting their own profile or is an admin
    if (!clerkUserId || clerkUserId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const profile = await ProfileService.getProfile(userId);

    if (!profile) {
      return NextResponse.json({ error: "Profile not found" }, { status: 404 });
    }

    return NextResponse.json(profile);
  } catch (error) {
    console.error("Error fetching profile:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    const { userId } = await params;

    // Verify the user is updating their own profile
    if (!clerkUserId || clerkUserId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const updates = await request.json();

    // Remove fields that shouldn't be updated directly
    const allowedUpdates = {
      full_name: updates.full_name,
      avatar_url: updates.avatar_url,
      motto: updates.motto,
      wallet_address: updates.wallet_address,
      youtube_id: updates.youtube_id,
      youtube_title: updates.youtube_title,
      subscribers: updates.subscribers,
      views: updates.views,
      experience_points: updates.experience_points,
      level: updates.level,
      last_login_at: updates.last_login_at,
    };

    // Filter out undefined values but keep null values (needed for disconnection)
    const filteredUpdates = Object.fromEntries(
      Object.entries(allowedUpdates).filter(([_, value]) => value !== undefined)
    );

    const updatedProfile = await ProfileService.updateProfile(
      userId,
      filteredUpdates
    );

    if (!updatedProfile) {
      return NextResponse.json({ error: "Profile not found" }, { status: 404 });
    }

    return NextResponse.json(updatedProfile);
  } catch (error) {
    console.error("Error updating profile:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
