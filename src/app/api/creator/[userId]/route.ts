import { NextRequest, NextResponse } from "next/server";
import { ProfileService } from "@/services/profileService";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    // 获取用户公开资料
    const profile = await ProfileService.getProfile(userId);

    if (!profile) {
      return NextResponse.json({ error: "Creator not found" }, { status: 404 });
    }

    // 只返回公开信息，过滤掉敏感数据
    const publicProfile = {
      clerk_user_id: profile.clerk_user_id,
      full_name: profile.full_name,
      avatar_url: profile.avatar_url,
      motto: profile.motto,
      role: profile.role,
      experience_points: profile.experience_points,
      level: profile.level,
      is_premium_member: profile.is_premium_member,
      youtube_id: profile.youtube_id,
      youtube_title: profile.youtube_title,
      subscribers: profile.subscribers,
      views: profile.views,
      created_at: profile.created_at,
      user_numeric_id: profile.user_numeric_id,
      social_links: profile.social_links,
      // 不返回敏感信息如 email, wallet_address 等
    };

    return NextResponse.json(publicProfile);
  } catch (error) {
    console.error("Error fetching creator profile:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
