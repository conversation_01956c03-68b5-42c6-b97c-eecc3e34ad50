// 用户工具交互 API 路由

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getUserTools, toggleToolFavorite, recordToolUsage } from '@/lib/database/tools';
import type { 
  GetUserToolsRequest,
  GetUserToolsResponse,
  ToggleFavoriteRequest,
  RecordUsageRequest,
  ApiResponse,
  UserTool
} from '@/types/tools';

// 验证切换收藏请求数据
function validateToggleFavoriteRequest(data: any): data is ToggleFavoriteRequest {
  return data && typeof data === 'object' && 
         typeof data.tool_id === 'string' && 
         data.tool_id.trim().length > 0;
}

// 验证记录使用请求数据
function validateRecordUsageRequest(data: any): data is RecordUsageRequest {
  return data && typeof data === 'object' && 
         typeof data.tool_id === 'string' && 
         data.tool_id.trim().length > 0;
}

// GET /api/user/tools - 获取用户工具列表
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const getUserToolsRequest: GetUserToolsRequest = {
      favorites_only: searchParams.get('favorites_only') === 'true',
      category: searchParams.get('category') as any || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined,
    };
    
    // 获取用户工具列表
    const result = await getUserTools(userId, getUserToolsRequest);
    
    return NextResponse.json(
      {
        success: true,
        data: result
      } as ApiResponse<GetUserToolsResponse>,
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error in GET /api/user/tools:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取用户工具列表失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}

// POST /api/user/tools - 处理用户工具操作（收藏、使用记录）
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }
    
    // 解析请求体
    const body = await request.json();
    const { action } = body;
    
    if (!action || typeof action !== 'string') {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '缺少操作类型参数'
          }
        } as ApiResponse<never>,
        { status: 400 }
      );
    }
    
    switch (action) {
      case 'toggle_favorite':
        // 切换收藏状态
        if (!validateToggleFavoriteRequest(body)) {
          return NextResponse.json(
            {
              success: false,
              error: {
                code: 'VALIDATION_ERROR',
                message: '请求数据格式错误'
              }
            } as ApiResponse<never>,
            { status: 400 }
          );
        }
        
        const favoriteResult = await toggleToolFavorite(userId, body.tool_id);
        
        return NextResponse.json(
          {
            success: true,
            data: favoriteResult
          } as ApiResponse<UserTool>,
          { status: 200 }
        );
        
      case 'record_usage':
        // 记录工具使用
        if (!validateRecordUsageRequest(body)) {
          return NextResponse.json(
            {
              success: false,
              error: {
                code: 'VALIDATION_ERROR',
                message: '请求数据格式错误'
              }
            } as ApiResponse<never>,
            { status: 400 }
          );
        }
        
        const usageResult = await recordToolUsage(userId, body.tool_id);
        
        return NextResponse.json(
          {
            success: true,
            data: usageResult
          } as ApiResponse<UserTool>,
          { status: 200 }
        );
        
      default:
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'INVALID_ACTION',
              message: '无效的操作类型'
            }
          } as ApiResponse<never>,
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('Error in POST /api/user/tools:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '操作失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
