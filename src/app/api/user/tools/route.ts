// 简化的用户工具 API 路由 - MVP版本

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getUserSelectedTools, toggleToolSelection } from '@/lib/database/tools';
import type {
  GetUserToolsResponse,
  ToggleToolSelectionRequest,
  ApiResponse
} from '@/types/tools';

// 验证切换选择请求数据
function validateToggleSelectionRequest(data: any): data is ToggleToolSelectionRequest {
  return data && typeof data === 'object' &&
         typeof data.tool_id === 'string' &&
         data.tool_id.trim().length > 0;
}

// GET /api/user/tools - 获取用户选择的工具列表
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }

    // 获取用户选择的工具列表
    const result = await getUserSelectedTools(userId);

    return NextResponse.json(
      {
        success: true,
        data: result
      } as ApiResponse<GetUserToolsResponse>,
      { status: 200 }
    );

  } catch (error) {
    console.error('Error in GET /api/user/tools:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取用户工具列表失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}

// POST /api/user/tools - 切换工具选择状态
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    if (!validateToggleSelectionRequest(body)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求数据格式错误'
          }
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 切换工具选择状态
    const isSelected = await toggleToolSelection(userId, body.tool_id);

    return NextResponse.json(
      {
        success: true,
        data: { tool_id: body.tool_id, is_selected: isSelected }
      } as ApiResponse<{ tool_id: string; is_selected: boolean }>,
      { status: 200 }
    );

  } catch (error) {
    console.error('Error in POST /api/user/tools:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '操作失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
