import { NextResponse } from "next/server";
import { db, testConnection } from "@/lib/db";

export async function GET() {
  try {
    // 测试数据库连接
    const startTime = Date.now();
    await testConnection();
    const connectionTime = Date.now() - startTime;

    // 执行一个简单查询测试
    const queryStartTime = Date.now();
    await db`SELECT NOW() as current_time`;
    const queryTime = Date.now() - queryStartTime;

    return NextResponse.json({
      status: "healthy",
      database: {
        connected: true,
        connectionTime: `${connectionTime}ms`,
        queryTime: `${queryTime}ms`,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Health check failed:", error);

    const errorObj = error as { code?: string; message?: string };

    return NextResponse.json(
      {
        status: "unhealthy",
        database: {
          connected: false,
          error: errorObj.message || "Unknown database error",
          code: errorObj.code || "UNKNOWN",
        },
        timestamp: new Date().toISOString(),
      },
      { status: 503 }
    );
  }
}
