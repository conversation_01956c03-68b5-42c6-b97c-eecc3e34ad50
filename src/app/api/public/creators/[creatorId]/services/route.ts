// 获取创作者公开服务 API

import { NextRequest, NextResponse } from "next/server";
import { getPublicCreatorServices } from "@/lib/database/services";
import type {
  ServiceType,
  ApiResponse,
  PublicServicesResponse,
} from "@/types/services";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ creatorId: string }> }
) {
  try {
    const { creatorId } = await params;

    // 验证 creatorId
    if (!creatorId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "创作者ID不能为空",
          },
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") as ServiceType | null;

    // 验证类型参数
    if (type && !["embedded", "custom"].includes(type)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "无效的服务类型",
          },
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 获取公开服务数据
    const result = await getPublicCreatorServices(creatorId, type || undefined);

    return NextResponse.json(
      {
        success: true,
        data: result,
      } as ApiResponse<PublicServicesResponse>,
      {
        status: 200,
        headers: {
          // 设置缓存headers
          "Cache-Control": "public, max-age=600", // 缓存10分钟
        },
      }
    );
  } catch (error) {
    console.error(
      "Error in GET /api/public/creators/[creatorId]/services:",
      error
    );

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "创作者不存在",
          },
        } as ApiResponse<never>,
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "服务器内部错误",
        },
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
