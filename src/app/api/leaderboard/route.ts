import { NextRequest, NextResponse } from "next/server";
import { ProfileService } from "@/services/profileService";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sortBy = searchParams.get("sortBy") || "experience_points";
    const limit = parseInt(searchParams.get("limit") || "50");

    // 验证排序字段
    const allowedSortFields = [
      "experience_points",
      "subscribers",
      "views",
      "level",
    ];
    if (!allowedSortFields.includes(sortBy)) {
      return NextResponse.json(
        { error: "Invalid sort field" },
        { status: 400 }
      );
    }

    const leaderboard = await ProfileService.getLeaderboard(sortBy, limit);

    return NextResponse.json(leaderboard);
  } catch (error) {
    console.error("Error fetching leaderboard:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
