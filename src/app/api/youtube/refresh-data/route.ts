import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { ProfileService } from "@/services/profileService";

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user profile to retrieve YouTube channel ID
    const profile = await ProfileService.getProfile(userId);
    if (!profile?.youtube_id) {
      return NextResponse.json(
        { error: "No YouTube channel connected" },
        { status: 400 }
      );
    }

    const channelId = profile.youtube_id;

    // Fetch latest channel data using YouTube Data API v3
    const apiKey = process.env.YOUTUBE_API_KEY;
    if (!apiKey) {
      console.error("YouTube API key not configured");
      return NextResponse.json(
        { error: "YouTube API not available" },
        { status: 500 }
      );
    }

    const channelResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&id=${channelId}&key=${apiKey}`
    );

    if (!channelResponse.ok) {
      console.error("Failed to fetch channel data:", channelResponse.statusText);
      return NextResponse.json(
        { error: "Failed to fetch channel data" },
        { status: 500 }
      );
    }

    const channelData = await channelResponse.json();

    if (!channelData.items || channelData.items.length === 0) {
      return NextResponse.json(
        { error: "Channel not found" },
        { status: 404 }
      );
    }

    const channel = channelData.items[0];

    // Update profile with latest data
    const updatedProfile = await ProfileService.updateProfile(userId, {
      youtube_title: channel.snippet.title,
      subscribers: parseInt(channel.statistics?.subscriberCount || '0'),
      views: parseInt(channel.statistics?.viewCount || '0'),
      avatar_url: channel.snippet.thumbnails?.default?.url || profile.avatar_url,
    });

    if (!updatedProfile) {
      return NextResponse.json(
        { error: "Failed to update profile" },
        { status: 500 }
      );
    }

    // Return the updated channel data
    return NextResponse.json({
      success: true,
      channelData: {
        channelId: channel.id,
        title: channel.snippet.title,
        subscribers: parseInt(channel.statistics?.subscriberCount || '0'),
        views: parseInt(channel.statistics?.viewCount || '0'),
        videos: parseInt(channel.statistics?.videoCount || '0'),
        thumbnailUrl: channel.snippet.thumbnails?.default?.url,
      },
      lastUpdated: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error refreshing YouTube data:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 