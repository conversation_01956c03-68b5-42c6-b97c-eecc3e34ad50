import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

interface YouTubePlaylistItem {
  snippet: {
    resourceId: {
      videoId: string;
    };
    title: string;
    description: string;
    publishedAt: string;
    thumbnails: {
      default?: { url: string };
      medium?: { url: string };
      high?: { url: string };
    };
  };
}

interface YouTubeVideoStats {
  id: string;
  statistics: {
    viewCount: string;
    likeCount: string;
  };
  contentDetails: {
    duration: string;
  };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const playlistId = searchParams.get("playlistId");
    const maxResults = searchParams.get("maxResults") || "50";
    const accessToken = request.headers
      .get("authorization")
      ?.replace("Bearer ", "");

    if (!playlistId) {
      return NextResponse.json(
        { error: "Playlist ID is required" },
        { status: 400 }
      );
    }

    // Prepare headers - include Authorization only if access token is provided
    const headers: Record<string, string> = {};
    if (accessToken) {
      headers.Authorization = `Bearer ${accessToken}`;
    }

    // Get videos from the uploads playlist (works with or without access token for public playlists)
    const videosResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/playlistItems?part=snippet&playlistId=${playlistId}&maxResults=${maxResults}&key=${process.env.YOUTUBE_API_KEY}`,
      {
        headers,
      }
    );

    if (!videosResponse.ok) {
      const errorData = await videosResponse.json();
      return NextResponse.json(
        { error: "Failed to fetch videos", details: errorData },
        { status: videosResponse.status }
      );
    }

    const videosData = await videosResponse.json();

    if (!videosData.items || videosData.items.length === 0) {
      return NextResponse.json({ videos: [] });
    }

    // Get video statistics for each video
    const videoIds = (videosData.items as YouTubePlaylistItem[])
      .map((item) => item.snippet.resourceId.videoId)
      .join(",");

    const statsResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?part=statistics,contentDetails&id=${videoIds}&key=${process.env.YOUTUBE_API_KEY}`,
      {
        headers,
      }
    );

    if (!statsResponse.ok) {
      const errorData = await statsResponse.json();
      return NextResponse.json(
        { error: "Failed to fetch video statistics", details: errorData },
        { status: statsResponse.status }
      );
    }

    const statsData = await statsResponse.json();

    // Format duration helper
    const formatDuration = (duration: string): string => {
      const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
      if (!match) return "0:00";

      const hours = parseInt(match[1] || "0");
      const minutes = parseInt(match[2] || "0");
      const seconds = parseInt(match[3] || "0");

      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds
          .toString()
          .padStart(2, "0")}`;
      }
      return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    };

    // Combine video data with statistics
    const formattedVideos = (videosData.items as YouTubePlaylistItem[]).map(
      (item) => {
        const videoId = item.snippet.resourceId.videoId;
        const stats = (statsData.items as YouTubeVideoStats[]).find(
          (stat) => stat.id === videoId
        );

        const duration = stats?.contentDetails?.duration || "PT0S";
        const formattedDuration = formatDuration(duration);

        return {
          id: videoId,
          thumbnail:
            item.snippet.thumbnails.medium?.url ||
            item.snippet.thumbnails.default?.url ||
            "",
          title: item.snippet.title,
          views: parseInt(stats?.statistics?.viewCount || "0"),
          likes: parseInt(stats?.statistics?.likeCount || "0"),
          duration: formattedDuration,
          publishedAt: item.snippet.publishedAt,
          status: "已发布",
          url: `https://www.youtube.com/watch?v=${videoId}`,
          description: item.snippet.description,
        };
      }
    );

    return NextResponse.json({ videos: formattedVideos });
  } catch (error) {
    console.error("YouTube videos API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}