import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const channelId = searchParams.get("channelId");
    const username = searchParams.get("username");
    const handle = searchParams.get("handle");

    if (!channelId && !username && !handle) {
      return NextResponse.json(
        { error: "Channel ID, username, or handle is required" },
        { status: 400 }
      );
    }

    // Build the API URL based on the provided parameter
    let apiUrl =
      "https://www.googleapis.com/youtube/v3/channels?part=contentDetails,snippet,statistics";

    if (channelId) {
      apiUrl += `&id=${channelId}`;
    } else if (username) {
      apiUrl += `&forUsername=${username}`;
    } else if (handle) {
      // For handles (e.g., @username), we need to use the handle format
      const cleanHandle = handle.startsWith("@") ? handle.slice(1) : handle;
      apiUrl += `&forHandle=${cleanHandle}`;
    }

    apiUrl += `&key=${process.env.YOUTUBE_API_KEY}`;

    // Fetch channel data (public API, no auth required)
    const channelResponse = await fetch(apiUrl);

    if (!channelResponse.ok) {
      const errorData = await channelResponse.json();
      return NextResponse.json(
        { error: "Failed to fetch channel data", details: errorData },
        { status: channelResponse.status }
      );
    }

    const channelData = await channelResponse.json();

    if (!channelData.items || channelData.items.length === 0) {
      return NextResponse.json({ error: "Channel not found" }, { status: 404 });
    }

    const channel = channelData.items[0];
    const uploadsPlaylistId = channel.contentDetails?.relatedPlaylists?.uploads;

    // Note: Some statistics might be hidden for privacy
    const subscriberCount = channel.statistics.hiddenSubscriberCount
      ? "Hidden"
      : channel.statistics.subscriberCount;

    return NextResponse.json({
      channelId: channel.id,
      title: channel.snippet.title,
      description: channel.snippet.description,
      customUrl: channel.snippet.customUrl,
      thumbnailUrl: channel.snippet.thumbnails.default?.url,
      bannerUrl: channel.snippet.thumbnails.high?.url,
      subscribers:
        subscriberCount === "Hidden"
          ? "Hidden"
          : parseInt(subscriberCount || "0"),
      subscribersHidden: channel.statistics.hiddenSubscriberCount || false,
      views: parseInt(channel.statistics.viewCount || "0"),
      videos: parseInt(channel.statistics.videoCount || "0"),
      publishedAt: channel.snippet.publishedAt,
      country: channel.snippet.country,
      uploadsPlaylistId,
      isPublic: true,
    });
  } catch (error) {
    console.error("YouTube public API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
