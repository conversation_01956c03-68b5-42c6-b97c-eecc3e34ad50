import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { ProfileService } from "@/services/profileService";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get("code");
    const state = searchParams.get("state");
    const error = searchParams.get("error");

    // Handle OAuth errors
    if (error) {
      console.error("OAuth error:", error);
      const redirectUrl = new URL("/profile/connections", request.nextUrl.origin);
      redirectUrl.searchParams.set("error", error);
      return NextResponse.redirect(redirectUrl);
    }

    if (!code || !state) {
      const redirectUrl = new URL("/profile/connections", request.nextUrl.origin);
      redirectUrl.searchParams.set("error", "Missing authorization code or state");
      return NextResponse.redirect(redirectUrl);
    }

    // Extract user ID from state (format: user_id_timestamp_random)
    const userId = state.split('_')[1];
    // Verify user is authenticated
    const { userId: authUserId } = await auth();

    if (!authUserId || !authUserId.includes(userId)) {
      const redirectUrl = new URL("/sign-in", request.nextUrl.origin);
      return NextResponse.redirect(redirectUrl);
    }

    // Exchange code for temporary access token (one-time use)
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID!,
        client_secret: process.env.YOUTUBE_CLIENT_SECRET!,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${request.nextUrl.origin}/api/youtube/verify-callback`,
      }),
    });

    if (!tokenResponse.ok) {
      const redirectUrl = new URL("/profile/connections", request.nextUrl.origin);
      redirectUrl.searchParams.set("error", "Failed to verify YouTube channel");
      return NextResponse.redirect(redirectUrl);
    }

    const tokenData = await tokenResponse.json();

    // Use token once to get channel info
    const channelResponse = await fetch(
      'https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&mine=true',
      {
        headers: { 'Authorization': `Bearer ${tokenData.access_token}` },
      }
    );

    if (!channelResponse.ok) {
      const redirectUrl = new URL("/profile/connections", request.nextUrl.origin);
      redirectUrl.searchParams.set("error", "Failed to fetch YouTube channel data");
      return NextResponse.redirect(redirectUrl);
    }

    const channelData = await channelResponse.json();

    if (!channelData.items || channelData.items.length === 0) {
      const redirectUrl = new URL("/profile/connections", request.nextUrl.origin);
      redirectUrl.searchParams.set("error", "No YouTube channel found for this account");
      return NextResponse.redirect(redirectUrl);
    }

    const channel = channelData.items[0];

    // Store channel ID and basic info in user profile (permanently)
    try {
      const updatedProfile = await ProfileService.updateProfile(authUserId, {
        youtube_id: channel.id,
        youtube_title: channel.snippet.title,
        subscribers: parseInt(channel.statistics?.subscriberCount || '0'),
        views: parseInt(channel.statistics?.viewCount || '0'),
        avatar_url: channel.snippet.thumbnails?.default?.url || undefined,
      });

      if (!updatedProfile) {
        console.warn("Failed to update profile with YouTube data");
        const redirectUrl = new URL("/profile/connections", request.nextUrl.origin);
        redirectUrl.searchParams.set("error", "Failed to save channel information");
        return NextResponse.redirect(redirectUrl);
      }
    } catch (profileError) {
      console.error("Error updating profile:", profileError);
      const redirectUrl = new URL("/profile/connections", request.nextUrl.origin);
      redirectUrl.searchParams.set("error", "Failed to save channel information");
      return NextResponse.redirect(redirectUrl);
    }

    // Success - redirect back to profile page
    const redirectUrl = new URL("/profile", request.nextUrl.origin);
    return NextResponse.redirect(redirectUrl);

  } catch (error) {
    console.error("Error in YouTube verification callback:", error);
    const redirectUrl = new URL("/profile/connections", request.nextUrl.origin);
    redirectUrl.searchParams.set("error", "Internal server error");
    return NextResponse.redirect(redirectUrl);
  }
} 