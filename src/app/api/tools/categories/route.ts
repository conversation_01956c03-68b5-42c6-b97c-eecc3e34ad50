// 工具分类 API 路由

import { NextRequest, NextResponse } from 'next/server';
import { getToolStats } from '@/lib/database/tools';
import { TOOL_CATEGORIES, PLATFORMS } from '@/types/tools';
import type { 
  ApiResponse,
  CategoryInfo,
  PlatformInfo,
  ToolStats
} from '@/types/tools';

// GET /api/tools/categories - 获取工具分类和平台信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeStats = searchParams.get('include_stats') === 'true';
    
    let stats: ToolStats | undefined;
    
    if (includeStats) {
      try {
        stats = await getToolStats();
      } catch (error) {
        console.warn('Failed to get tool stats:', error);
        // 继续返回分类信息，即使统计失败
      }
    }
    
    const response = {
      categories: TOOL_CATEGORIES,
      platforms: PLATFORMS,
      ...(stats && { stats })
    };
    
    return NextResponse.json(
      {
        success: true,
        data: response
      } as ApiResponse<{
        categories: CategoryInfo[];
        platforms: PlatformInfo[];
        stats?: ToolStats;
      }>,
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error in GET /api/tools/categories:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取分类信息失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
