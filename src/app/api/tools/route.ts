// 简化的工具 API 路由 - MVP版本

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getTools } from '@/lib/database/tools';
import type {
  GetToolsRequest,
  ApiResponse,
  GetToolsResponse
} from '@/types/tools';

// GET /api/tools - 获取工具列表（简化版）
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    const { searchParams } = new URL(request.url);

    // 解析查询参数（只支持分类筛选）
    const getToolsRequest: GetToolsRequest = {
      category: searchParams.get('category') as any || undefined,
    };

    // 获取工具列表
    const result = await getTools(getToolsRequest, userId || undefined);

    return NextResponse.json(
      {
        success: true,
        data: result
      } as ApiResponse<GetToolsResponse>,
      { status: 200 }
    );

  } catch (error) {
    console.error('Error in GET /api/tools:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取工具列表失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
