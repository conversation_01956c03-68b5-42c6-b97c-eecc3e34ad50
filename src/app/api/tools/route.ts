// 工具管理 API 路由

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getTools, createTool } from '@/lib/database/tools';
import type { 
  GetToolsRequest,
  CreateToolRequest,
  ApiResponse,
  Tool,
  GetToolsResponse
} from '@/types/tools';

// 验证创建工具请求数据
function validateCreateToolRequest(data: any): data is CreateToolRequest {
  if (!data || typeof data !== 'object') {
    return false;
  }
  
  const { name, category, platform } = data;
  
  // 验证必填字段
  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    return false;
  }
  
  if (!category || typeof category !== 'string') {
    return false;
  }
  
  if (!Array.isArray(platform) || platform.length === 0) {
    return false;
  }
  
  // 验证分类是否有效
  const validCategories = ['video', 'image', 'audio', 'ai', 'design', 'productivity', 'analytics', 'other'];
  if (!validCategories.includes(category)) {
    return false;
  }
  
  // 验证平台是否有效
  const validPlatforms = ['Web', 'Windows', 'Mac', 'iOS', 'Android'];
  if (!platform.every((p: any) => validPlatforms.includes(p))) {
    return false;
  }
  
  return true;
}

// GET /api/tools - 获取工具列表
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const getToolsRequest: GetToolsRequest = {
      category: searchParams.get('category') as any || undefined,
      platform: searchParams.get('platform') as any || undefined,
      is_free: searchParams.get('is_free') ? searchParams.get('is_free') === 'true' : undefined,
      search: searchParams.get('search') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined,
    };
    
    // 获取工具列表
    const result = await getTools(getToolsRequest, userId || undefined);
    
    return NextResponse.json(
      {
        success: true,
        data: result
      } as ApiResponse<GetToolsResponse>,
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error in GET /api/tools:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取工具列表失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}

// POST /api/tools - 创建新工具（仅管理员）
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }
    
    // TODO: 添加管理员权限检查
    // 这里应该检查用户是否为管理员
    // const userProfile = await getUserProfile(userId);
    // if (userProfile.role !== 'admin') {
    //   return NextResponse.json({...}, { status: 403 });
    // }
    
    // 解析请求体
    const body = await request.json();
    
    // 验证请求数据
    if (!validateCreateToolRequest(body)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求数据格式错误或参数无效'
          }
        } as ApiResponse<never>,
        { status: 400 }
      );
    }
    
    // 创建工具
    const newTool = await createTool({
      name: body.name,
      description: body.description,
      icon_url: body.icon_url,
      website_url: body.website_url,
      category: body.category,
      platform: body.platform,
      is_free: body.is_free ?? true
    });
    
    return NextResponse.json(
      {
        success: true,
        data: newTool
      } as ApiResponse<Tool>,
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Error in POST /api/tools:', error);
    
    // 检查是否是重复名称错误
    if (error instanceof Error && error.message.includes('已存在')) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'DUPLICATE_TOOL',
            message: '工具名称已存在'
          }
        } as ApiResponse<never>,
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '创建工具失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
