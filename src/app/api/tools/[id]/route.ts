// 单个工具管理 API 路由

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getToolById, updateTool, deleteTool } from '@/lib/database/tools';
import type { 
  UpdateToolRequest,
  ApiResponse,
  Tool,
  ToolWithUserData
} from '@/types/tools';

// 验证更新工具请求数据
function validateUpdateToolRequest(data: any): data is UpdateToolRequest {
  if (!data || typeof data !== 'object') {
    return false;
  }
  
  // 至少要有一个字段需要更新
  const hasValidField = Object.keys(data).some(key => {
    const value = data[key];
    switch (key) {
      case 'name':
        return typeof value === 'string' && value.trim().length > 0;
      case 'description':
      case 'icon_url':
      case 'website_url':
        return typeof value === 'string';
      case 'category':
        return typeof value === 'string' && 
               ['video', 'image', 'audio', 'ai', 'design', 'productivity', 'analytics', 'other'].includes(value);
      case 'platform':
        return Array.isArray(value) && value.length > 0 &&
               value.every((p: any) => ['Web', 'Windows', 'Mac', 'iOS', 'Android'].includes(p));
      case 'is_free':
        return typeof value === 'boolean';
      default:
        return false;
    }
  });
  
  return hasValidField;
}

// GET /api/tools/[id] - 获取单个工具详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    const toolId = params.id;
    
    // 获取工具详情
    const tool = await getToolById(toolId, userId || undefined);
    
    if (!tool) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TOOL_NOT_FOUND',
            message: '工具不存在'
          }
        } as ApiResponse<never>,
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      {
        success: true,
        data: tool
      } as ApiResponse<ToolWithUserData>,
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error in GET /api/tools/[id]:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取工具详情失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}

// PUT /api/tools/[id] - 更新工具信息（仅管理员）
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }
    
    // TODO: 添加管理员权限检查
    // const userProfile = await getUserProfile(userId);
    // if (userProfile.role !== 'admin') {
    //   return NextResponse.json({...}, { status: 403 });
    // }
    
    const toolId = params.id;
    
    // 解析请求体
    const body = await request.json();
    
    // 验证请求数据
    if (!validateUpdateToolRequest(body)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求数据格式错误或参数无效'
          }
        } as ApiResponse<never>,
        { status: 400 }
      );
    }
    
    // 更新工具
    const updatedTool = await updateTool(toolId, body);
    
    if (!updatedTool) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TOOL_NOT_FOUND',
            message: '工具不存在'
          }
        } as ApiResponse<never>,
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      {
        success: true,
        data: updatedTool
      } as ApiResponse<Tool>,
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error in PUT /api/tools/[id]:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '更新工具失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}

// DELETE /api/tools/[id] - 删除工具（仅管理员）
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }
    
    // TODO: 添加管理员权限检查
    // const userProfile = await getUserProfile(userId);
    // if (userProfile.role !== 'admin') {
    //   return NextResponse.json({...}, { status: 403 });
    // }
    
    const toolId = params.id;
    
    // 删除工具
    const deleted = await deleteTool(toolId);
    
    if (!deleted) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TOOL_NOT_FOUND',
            message: '工具不存在'
          }
        } as ApiResponse<never>,
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      {
        success: true,
        data: { deleted: true }
      } as ApiResponse<{ deleted: boolean }>,
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error in DELETE /api/tools/[id]:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '删除工具失败'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
