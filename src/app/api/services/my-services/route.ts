// 获取创作者服务列表 API

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getCreatorServices } from '@/lib/database/services';
import type { ServiceType, ServiceStatus, ApiResponse, MyServicesResponse } from '@/types/services';

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as ServiceType | null;
    const status = searchParams.get('status') as ServiceStatus | null;

    // 验证参数
    if (type && !['embedded', 'custom'].includes(type)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '无效的服务类型'
          }
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    if (status && !['active', 'inactive'].includes(status)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '无效的服务状态'
          }
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 获取服务数据
    const services = await getCreatorServices(
      userId,
      type || undefined,
      status || undefined
    );

    return NextResponse.json(
      {
        success: true,
        data: services
      } as ApiResponse<MyServicesResponse>,
      { status: 200 }
    );

  } catch (error) {
    console.error('Error in GET /api/services/my-services:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
} 