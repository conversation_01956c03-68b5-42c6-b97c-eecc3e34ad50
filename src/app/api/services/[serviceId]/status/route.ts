// 更新服务状态 API

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { updateServiceStatus } from "@/lib/database/services";
import type {
  UpdateServiceStatusRequest,
  ApiResponse,
  ServiceStatusUpdateResponse,
  ServiceStatus,
} from "@/types/services";

// 验证状态更新请求数据
function validateStatusUpdateRequest(
  data: any
): data is UpdateServiceStatusRequest {
  if (!data || typeof data !== "object") return false;

  const { status } = data;

  // 验证状态值
  if (!status || !["active", "inactive"].includes(status)) {
    return false;
  }

  return true;
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ serviceId: string }> }
) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "UNAUTHORIZED",
            message: "用户未认证",
          },
        } as ApiResponse<never>,
        { status: 401 }
      );
    }

    const { serviceId } = await params;

    // 验证 serviceId
    if (!serviceId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "服务ID不能为空",
          },
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    if (!validateStatusUpdateRequest(body)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "状态值无效，必须为 active 或 inactive",
          },
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 更新服务状态
    const result = await updateServiceStatus(serviceId, userId, body.status);

    return NextResponse.json(
      {
        success: true,
        data: result,
      } as ApiResponse<ServiceStatusUpdateResponse>,
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in PATCH /api/services/[serviceId]/status:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "服务不存在或无权限访问",
          },
        } as ApiResponse<never>,
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "服务器内部错误",
        },
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
