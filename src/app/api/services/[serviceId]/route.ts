// 更新和删除服务 API

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { updateService, deleteService } from "@/lib/database/services";
import type {
  UpdateServiceRequest,
  ApiResponse,
  Service,
} from "@/types/services";

// 验证更新请求数据
function validateUpdateServiceRequest(data: any): data is UpdateServiceRequest {
  if (!data || typeof data !== "object") return false;

  const { title, description, priceMin, priceMax } = data;

  // 至少要有一个字段需要更新
  if (
    title === undefined &&
    description === undefined &&
    priceMin === undefined &&
    priceMax === undefined
  ) {
    return false;
  }

  // 验证字符串长度（如果提供）
  if (
    title !== undefined &&
    (typeof title !== "string" || title.length < 1 || title.length > 100)
  ) {
    return false;
  }

  if (
    description !== undefined &&
    (typeof description !== "string" ||
      description.length < 1 ||
      description.length > 500)
  ) {
    return false;
  }

  // 验证价格（如果提供）
  if (
    priceMin !== undefined &&
    (typeof priceMin !== "number" || priceMin < 0)
  ) {
    return false;
  }

  if (
    priceMax !== undefined &&
    (typeof priceMax !== "number" || priceMax < 0)
  ) {
    return false;
  }

  // 如果同时提供了 priceMin 和 priceMax，验证它们的关系
  if (priceMin !== undefined && priceMax !== undefined && priceMax < priceMin) {
    return false;
  }

  return true;
}

// 更新服务
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ serviceId: string }> }
) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "UNAUTHORIZED",
            message: "用户未认证",
          },
        } as ApiResponse<never>,
        { status: 401 }
      );
    }

    const { serviceId } = await params;

    // 验证 serviceId
    if (!serviceId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "服务ID不能为空",
          },
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    if (!validateUpdateServiceRequest(body)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "请求数据格式错误或参数无效",
          },
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 更新服务
    const updatedService = await updateService(serviceId, userId, body);

    return NextResponse.json(
      {
        success: true,
        data: updatedService,
      } as ApiResponse<Service>,
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in PUT /api/services/[serviceId]:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "服务不存在或无权限访问",
          },
        } as ApiResponse<never>,
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "服务器内部错误",
        },
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}

// 删除服务
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ serviceId: string }> }
) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "UNAUTHORIZED",
            message: "用户未认证",
          },
        } as ApiResponse<never>,
        { status: 401 }
      );
    }

    const { serviceId } = await params;

    // 验证 serviceId
    if (!serviceId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "服务ID不能为空",
          },
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 删除服务
    await deleteService(serviceId, userId);

    return NextResponse.json(
      {
        success: true,
        message: "服务删除成功",
      } as ApiResponse<{ message: string }>,
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in DELETE /api/services/[serviceId]:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "服务不存在或无权限访问",
          },
        } as ApiResponse<never>,
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "服务器内部错误",
        },
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
