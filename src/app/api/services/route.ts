// 创建服务 API

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createService } from '@/lib/database/services';
import type { 
  CreateServiceRequest, 
  ApiResponse, 
  Service,
  ServiceType 
} from '@/types/services';

// 验证请求体数据
function validateCreateServiceRequest(data: any): data is CreateServiceRequest {
  console.log('[validateCreateServiceRequest] 开始验证请求数据:', JSON.stringify(data, null, 2));
  
  // 验证数据基本格式
  if (!data || typeof data !== 'object') {
    console.log('[validateCreateServiceRequest] 验证失败: 数据不是有效对象');
    return false;
  }
  
  const { type, title, description, priceMin, priceMax } = data;
  console.log('[validateCreateServiceRequest] 提取字段:', { type, title, description, priceMin, priceMax });
  
  // 验证必填字段存在性
  if (!type) {
    console.log('[validateCreateServiceRequest] 验证失败: type 字段缺失');
    return false;
  }
  if (!title) {
    console.log('[validateCreateServiceRequest] 验证失败: title 字段缺失');
    return false;
  }
  if (!description) {
    console.log('[validateCreateServiceRequest] 验证失败: description 字段缺失');
    return false;
  }
  if (priceMin === undefined) {
    console.log('[validateCreateServiceRequest] 验证失败: priceMin 字段缺失');
    return false;
  }
  if (priceMax === undefined) {
    console.log('[validateCreateServiceRequest] 验证失败: priceMax 字段缺失');
    return false;
  }
  
  // 验证服务类型
  if (!['embedded', 'custom'].includes(type)) {
    console.log('[validateCreateServiceRequest] 验证失败: type 值无效，期望 embedded 或 custom，实际:', type);
    return false;
  }
  
  // 验证标题
  if (typeof title !== 'string') {
    console.log('[validateCreateServiceRequest] 验证失败: title 不是字符串类型，实际类型:', typeof title);
    return false;
  }
  if (title.length < 1) {
    console.log('[validateCreateServiceRequest] 验证失败: title 长度不能为空');
    return false;
  }
  if (title.length > 100) {
    console.log('[validateCreateServiceRequest] 验证失败: title 长度超过100字符，实际长度:', title.length);
    return false;
  }
  
  // 验证描述
  if (typeof description !== 'string') {
    console.log('[validateCreateServiceRequest] 验证失败: description 不是字符串类型，实际类型:', typeof description);
    return false;
  }
  if (description.length < 1) {
    console.log('[validateCreateServiceRequest] 验证失败: description 长度不能为空');
    return false;
  }
  if (description.length > 500) {
    console.log('[validateCreateServiceRequest] 验证失败: description 长度超过500字符，实际长度:', description.length);
    return false;
  }
  
  // 验证最低价格
  if (typeof priceMin !== 'number') {
    console.log('[validateCreateServiceRequest] 验证失败: priceMin 不是数字类型，实际类型:', typeof priceMin, '值:', priceMin);
    return false;
  }
  if (priceMin < 0) {
    console.log('[validateCreateServiceRequest] 验证失败: priceMin 不能为负数，实际值:', priceMin);
    return false;
  }
  
  // 验证最高价格
  if (typeof priceMax !== 'number') {
    console.log('[validateCreateServiceRequest] 验证失败: priceMax 不是数字类型，实际类型:', typeof priceMax, '值:', priceMax);
    return false;
  }
  if (priceMax < 0) {
    console.log('[validateCreateServiceRequest] 验证失败: priceMax 不能为负数，实际值:', priceMax);
    return false;
  }
  
  // 验证价格范围逻辑
  if (priceMax < priceMin) {
    console.log('[validateCreateServiceRequest] 验证失败: priceMax 不能小于 priceMin，priceMin:', priceMin, 'priceMax:', priceMax);
    return false;
  }
  
  console.log('[validateCreateServiceRequest] 验证成功: 所有字段都符合要求');
  return true;
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '用户未认证'
          }
        } as ApiResponse<never>,
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    
    // 验证请求数据
    if (!validateCreateServiceRequest(body)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求数据格式错误或参数无效'
          }
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    // 创建服务
    const newService = await createService(userId, body);

    return NextResponse.json(
      {
        success: true,
        data: newService
      } as ApiResponse<Service>,
      { status: 201 }
    );

  } catch (error) {
    console.error('Error in POST /api/services:', error);
    
    // 检查是否是重复标题错误
    if (error instanceof Error && error.message.includes('duplicate')) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'DUPLICATE_SERVICE',
            message: '服务标题已存在'
          }
        } as ApiResponse<never>,
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误'
        }
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
} 