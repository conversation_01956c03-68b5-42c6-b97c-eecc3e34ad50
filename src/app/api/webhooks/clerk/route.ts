import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { Webhook } from "svix";
import { ProfileService } from "@/services/profileService";
import type { NewProfile, ExternalAccounts } from "@/lib/schema";

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

interface ClerkUser {
  id: string;
  email_addresses: Array<{
    email_address: string;
    verification?: {
      status: string;
    };
  }>;
  username?: string;
  first_name?: string;
  last_name?: string;
  image_url?: string;
  created_at: number;
  updated_at: number;
  external_accounts?: Array<{
    provider: string;
    email_address?: string;
    username?: string;
    external_id: string;
  }>;
}

interface ClerkWebhookEvent {
  type: string;
  data: ClerkUser;
}

function constructFullName(
  firstName?: string,
  lastName?: string
): string | undefined {
  if (!firstName && !lastName) return undefined;
  return [firstName, lastName].filter(Boolean).join(" ");
}

function processExternalAccounts(
  externalAccounts?: ClerkUser["external_accounts"]
): ExternalAccounts {
  if (!externalAccounts || externalAccounts.length === 0) return {};

  const processed: ExternalAccounts = {};
  externalAccounts.forEach((account) => {
    processed[account.provider] = {
      id: account.external_id,
      email: account.email_address,
      username: account.username,
      verified: true, // Assume verified if from Clerk
    };
  });

  return processed;
}

export async function POST(request: NextRequest) {
  if (!webhookSecret) {
    return NextResponse.json(
      { error: "CLERK_WEBHOOK_SECRET is not set" },
      { status: 500 }
    );
  }

  try {
    const headerPayload = await headers();
    const svixId = headerPayload.get("svix-id");
    const svixTimestamp = headerPayload.get("svix-timestamp");
    const svixSignature = headerPayload.get("svix-signature");

    if (!svixId || !svixTimestamp || !svixSignature) {
      return NextResponse.json(
        { error: "Missing svix headers" },
        { status: 400 }
      );
    }

    const payload = await request.text();
    const wh = new Webhook(webhookSecret);

    let event: ClerkWebhookEvent;
    try {
      event = wh.verify(payload, {
        "svix-id": svixId,
        "svix-timestamp": svixTimestamp,
        "svix-signature": svixSignature,
      }) as ClerkWebhookEvent;
    } catch (err) {
      console.error("Error verifying webhook:", err);
      return NextResponse.json(
        { error: "Invalid webhook signature" },
        { status: 400 }
      );
    }

    const { type, data: user } = event;

    switch (type) {
      case "user.created":
      case "user.updated": {
        const primaryEmail =
          user.email_addresses.find(
            (email) => email.verification?.status === "verified"
          ) || user.email_addresses[0];

        if (!primaryEmail) {
          console.error("No email found for user:", user.id);
          return NextResponse.json(
            { error: "No email found" },
            { status: 400 }
          );
        }

        const profileData: NewProfile = {
          clerk_user_id: user.id,
          email: primaryEmail.email_address,
          clerk_username: user.username || undefined,
          full_name: constructFullName(user.first_name, user.last_name),
          avatar_url: user.image_url || undefined,
          email_verified:
            primaryEmail.verification?.status === "verified" || false,
          external_accounts: processExternalAccounts(user.external_accounts),
          clerk_created_at: new Date(user.created_at),
          clerk_updated_at: new Date(user.updated_at),
        };

        if (type === "user.created") {
          // Generate a unique numeric ID for new users
          const numericId = await ProfileService.generateUniqueNumericId();
          profileData.user_numeric_id = numericId;

          await ProfileService.createProfile(profileData);
          console.log(`Created profile for user: ${user.id}`);
        } else {
          // Update existing profile
          await ProfileService.updateProfile(user.id, {
            email: profileData.email,
            clerk_username: profileData.clerk_username,
            full_name: profileData.full_name,
            avatar_url: profileData.avatar_url,
            email_verified: profileData.email_verified,
            external_accounts: profileData.external_accounts,
            clerk_updated_at: profileData.clerk_updated_at,
          });
          console.log(`Updated profile for user: ${user.id}`);
        }

        // Update last login time
        await ProfileService.updateLastLogin(user.id);
        break;
      }

      case "user.deleted": {
        await ProfileService.deleteProfile(user.id);
        console.log(`Deleted profile for user: ${user.id}`);
        break;
      }

      default:
        console.log(`Unhandled webhook event type: ${type}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Webhook error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
