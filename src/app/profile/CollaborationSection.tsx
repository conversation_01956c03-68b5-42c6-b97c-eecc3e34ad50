import React, { useState } from "react";
import { Sparkles } from "lucide-react";

interface CollaborationConfig {
  bySession: {
    enabled: boolean;
    price: number;
    description: string;
  };
  byMonth: {
    enabled: boolean;
    price: number;
    description: string;
  };
}

interface CollaborationSectionProps {
  collaboration: CollaborationConfig;
  onCollaborationUpdate: (newCollaboration: CollaborationConfig) => void;
  editable?: boolean;
}

const CollaborationSection: React.FC<CollaborationSectionProps> = ({
  collaboration,
  onCollaborationUpdate,
  editable = true,
}) => {
  const [showEditCollab, setShowEditCollab] = useState(false);
  const [collabDraft, setCollabDraft] = useState<CollaborationConfig>({
    bySession: { ...collaboration.bySession },
    byMonth: { ...collaboration.byMonth },
  });

  const handleSave = () => {
    onCollaborationUpdate({ ...collabDraft });
    setShowEditCollab(false);
  };

  const handleCancel = () => {
    setCollabDraft({
      bySession: { ...collaboration.bySession },
      byMonth: { ...collaboration.byMonth },
    });
    setShowEditCollab(false);
  };

  return (
    <>
      {/* 合作方式与收费标准卡片 */}
      <div className="relative mt-6 mb-8 bg-gradient-to-br from-[#4ecdc4]/10 to-[#f39c12]/10 border border-[#4ecdc4]/20 rounded-xl p-5 shadow group">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-[#4ecdc4]" />
            <span className="text-lg font-bold text-white">
              合作方式与收费标准
            </span>
          </div>
          {editable && (
            <button
              className="px-3 py-1 bg-[#4ecdc4]/20 text-[#4ecdc4] rounded-lg text-sm font-medium hover:bg-[#4ecdc4]/30 transition-all"
              onClick={() => setShowEditCollab(true)}
            >
              编辑
            </button>
          )}
        </div>
        <div className="space-y-2 text-white/90 text-sm">
          {collaboration.bySession.enabled && (
            <div>
              <span className="font-semibold">按次合作：</span>
              <span className="text-[#00cec9] font-bold">
                {collaboration.bySession.price} USD/次
              </span>
              {collaboration.bySession.description && (
                <span className="ml-2 text-white/60">
                  {collaboration.bySession.description}
                </span>
              )}
            </div>
          )}
          {collaboration.byMonth.enabled && (
            <div>
              <span className="font-semibold">按月合作：</span>
              <span className="text-[#00cec9] font-bold">
                {collaboration.byMonth.price} USD/月
              </span>
              {collaboration.byMonth.description && (
                <span className="ml-2 text-white/60">
                  {collaboration.byMonth.description}
                </span>
              )}
            </div>
          )}
          {!collaboration.bySession.enabled &&
            !collaboration.byMonth.enabled && (
              <div className="text-white/60">
                未设置合作方式，点击编辑进行设置
              </div>
            )}
        </div>
      </div>

      {/* 编辑合作方式弹窗 */}
      {showEditCollab && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
          <div className="bg-[#1a202c] rounded-xl p-8 w-full max-w-md border border-[#4ecdc4]/30 relative">
            <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-[#4ecdc4]" />{" "}
              编辑合作方式与收费标准
            </h3>
            <div className="space-y-4">
              {/* 按次合作设置 */}
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={collabDraft.bySession.enabled}
                  onChange={(e) =>
                    setCollabDraft((d) => ({
                      ...d,
                      bySession: {
                        ...d.bySession,
                        enabled: e.target.checked,
                      },
                    }))
                  }
                  id="bySession"
                  className="accent-[#00cec9]"
                />
                <label htmlFor="bySession" className="text-white">
                  按次合作
                </label>
                <input
                  type="number"
                  min={0}
                  className="ml-2 w-20 px-2 py-1 rounded bg-[#0f1419]/60 text-white border border-[#4ecdc4]/20 focus:outline-none focus:border-[#4ecdc4]/40"
                  value={collabDraft.bySession.price}
                  onChange={(e) =>
                    setCollabDraft((d) => ({
                      ...d,
                      bySession: {
                        ...d.bySession,
                        price: Number(e.target.value),
                      },
                    }))
                  }
                  disabled={!collabDraft.bySession.enabled}
                />
                <span className="ml-1 text-white/60">USD/次</span>
              </div>
              <input
                type="text"
                className="w-full px-2 py-1 rounded bg-[#0f1419]/60 text-white border border-[#4ecdc4]/20 focus:outline-none focus:border-[#4ecdc4]/40"
                placeholder="按次合作说明（可选）"
                value={collabDraft.bySession.description}
                onChange={(e) =>
                  setCollabDraft((d) => ({
                    ...d,
                    bySession: {
                      ...d.bySession,
                      description: e.target.value,
                    },
                  }))
                }
                disabled={!collabDraft.bySession.enabled}
              />

              {/* 按月合作设置 */}
              <div className="flex items-center gap-2 mt-2">
                <input
                  type="checkbox"
                  checked={collabDraft.byMonth.enabled}
                  onChange={(e) =>
                    setCollabDraft((d) => ({
                      ...d,
                      byMonth: {
                        ...d.byMonth,
                        enabled: e.target.checked,
                      },
                    }))
                  }
                  id="byMonth"
                  className="accent-[#00cec9]"
                />
                <label htmlFor="byMonth" className="text-white">
                  按月合作
                </label>
                <input
                  type="number"
                  min={0}
                  className="ml-2 w-20 px-2 py-1 rounded bg-[#0f1419]/60 text-white border border-[#4ecdc4]/20 focus:outline-none focus:border-[#4ecdc4]/40"
                  value={collabDraft.byMonth.price}
                  onChange={(e) =>
                    setCollabDraft((d) => ({
                      ...d,
                      byMonth: {
                        ...d.byMonth,
                        price: Number(e.target.value),
                      },
                    }))
                  }
                  disabled={!collabDraft.byMonth.enabled}
                />
                <span className="ml-1 text-white/60">USD/月</span>
              </div>
              <input
                type="text"
                className="w-full px-2 py-1 rounded bg-[#0f1419]/60 text-white border border-[#4ecdc4]/20 focus:outline-none focus:border-[#4ecdc4]/40"
                placeholder="按月合作说明（可选）"
                value={collabDraft.byMonth.description}
                onChange={(e) =>
                  setCollabDraft((d) => ({
                    ...d,
                    byMonth: {
                      ...d.byMonth,
                      description: e.target.value,
                    },
                  }))
                }
                disabled={!collabDraft.byMonth.enabled}
              />
            </div>

            {/* 按钮区域 */}
            <div className="flex justify-end gap-3 mt-6">
              <button
                className="px-4 py-2 rounded bg-[#0f1419]/60 text-white hover:bg-[#1a202c]/60 border border-[#2d3748]/50"
                onClick={handleCancel}
              >
                取消
              </button>
              <button
                className="px-4 py-2 rounded bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] text-white font-bold shadow hover:scale-105 transition-all"
                onClick={handleSave}
              >
                保存
              </button>
            </div>

            {/* 关闭按钮 */}
            <button
              className="absolute top-2 right-2 text-white/60 hover:text-white"
              onClick={handleCancel}
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default CollaborationSection;
