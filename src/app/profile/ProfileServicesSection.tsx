import React, { useState, useEffect } from "react";
import { Sparkles, ChevronRight, ChevronDown, ChevronUp, Filter } from "lucide-react";
import { servicesApi } from "@/services/servicesApi";
import ServicesManagementModal from "@/components/services/ServicesManagementModal";
import type { MyServicesResponse, Service } from "@/types/services";

interface ProfileServicesSectionProps {
  creatorId: string;
  className?: string;
}

const ProfileServicesSection: React.FC<ProfileServicesSectionProps> = ({
  creatorId,
  className = "",
}) => {
  const [services, setServices] = useState<MyServicesResponse>({
    embedded: [],
    custom: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showManagementModal, setShowManagementModal] = useState(false);
  const [showAll, setShowAll] = useState(false);
  const [filter, setFilter] = useState<'all' | 'embedded' | 'custom'>('all');

  // 載入服務數據
  const loadServices = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await servicesApi.getMyServices({ status: "active" }); // 只顯示激活的服務
      setServices(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "載入服務失敗");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (creatorId) {
      loadServices();
    }
  }, [creatorId]);

  // 獲取服務類型顯示文字
  const getServiceTypeLabel = (type: Service['type']) => {
    return type === 'embedded' ? '植入推廣' : '定製推廣';
  };

  // 獲取服務類型顏色
  const getServiceTypeColor = (type: Service['type']) => {
    return type === 'embedded'
      ? 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      : 'bg-purple-500/20 text-purple-300 border-purple-500/30';
  };

  // 合併所有激活的服務
  const allActiveServices = [...services.embedded, ...services.custom];

  // 根據篩選器過濾服務
  const filteredServices = allActiveServices.filter(service => {
    if (filter === 'all') return true;
    return service.type === filter;
  });

  const totalServices = filteredServices.length;
  const maxDisplayCount = 8;
  const displayedServices = showAll ? filteredServices : filteredServices.slice(0, maxDisplayCount);
  const hasMore = filteredServices.length > maxDisplayCount;

  const handleManageServices = () => {
    setShowManagementModal(true);
  };

  const handleCloseModal = () => {
    setShowManagementModal(false);
    // 重新載入服務數據以反映可能的更改
    loadServices();
  };

  const getFilterLabel = (filterType: typeof filter) => {
    switch (filterType) {
      case 'all': return '全部';
      case 'embedded': return '植入推廣';
      case 'custom': return '定製推廣';
      default: return '全部';
    }
  };

  return (
    <div className={`relative mt-6 mb-8 bg-gradient-to-br from-[#4ecdc4]/10 to-[#f39c12]/10 border border-[#4ecdc4]/20 rounded-xl p-6 shadow group ${className}`}>
      {/* 標題和管理按鈕 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-[#4ecdc4]" />
          <span className="text-lg font-bold text-white">我的服務</span>
        </div>
        <button
          onClick={handleManageServices}
          className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] text-white rounded-lg text-sm font-medium hover:scale-105 transition-all shadow-lg shadow-[#4ecdc4]/20"
        >
          管理服務
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>

      <div className="border-t border-[#00cec9]/20 pt-4">
        {loading ? (
          <div className="text-center py-8 text-white/60">載入中...</div>
        ) : error ? (
          <div className="text-center py-8 text-red-400">載入失敗: {error}</div>
        ) : allActiveServices.length === 0 ? (
          <div className="text-center py-8 text-white/60">
            還沒有創建任何服務，點擊「管理服務」開始創建
          </div>
        ) : (
          <>
            {/* 篩選器 */}
            <div className="flex items-center gap-2 mb-4">
              <div className="flex items-center gap-1 text-white/60 text-sm">
                <Filter className="w-4 h-4" />
                <span>篩選:</span>
              </div>
              <div className="flex gap-1">
                {(['all', 'embedded', 'custom'] as const).map((filterType) => (
                  <button
                    key={filterType}
                    onClick={() => {
                      setFilter(filterType);
                      setShowAll(false); // 切換篩選器時重置展開狀態
                    }}
                    className={`px-3 py-1 rounded-lg text-xs font-medium transition-all ${
                      filter === filterType
                        ? 'bg-[#00cec9] text-white'
                        : 'bg-white/10 text-white/60 hover:bg-white/20'
                    }`}
                  >
                    {getFilterLabel(filterType)}
                  </button>
                ))}
              </div>
              {totalServices > 0 && (
                <div className="ml-auto text-white/40 text-xs">
                  共 {totalServices} 項
                </div>
              )}
            </div>

            {totalServices === 0 ? (
              <div className="text-center py-8 text-white/60">
                沒有找到符合條件的服務
              </div>
            ) : (
              <>
                {/* 服務列表 */}
                <div className="space-y-3">
                  {displayedServices.map((service) => (
                    <div
                      key={service.id}
                      className="flex items-center justify-between py-3 px-4 bg-[#0f1419]/60 rounded-lg border border-[#2d3748]/30 hover:bg-[#1a202c]/60 transition-all group/item"
                    >
                      <div className="flex items-center gap-3 flex-1">
                        <div className="w-2 h-2 rounded-full bg-[#4ecdc4]" />
                        <div className="flex-1">
                          <h4 className="text-white font-medium truncate max-w-[200px]">
                            {service.title}
                          </h4>
                          <p className="text-white/60 text-sm line-clamp-1">
                            {service.description}
                          </p>
                        </div>
                      </div>

                      {/* 服務類型標籤 */}
                      <div className={`px-3 py-1 rounded-lg border text-xs font-medium ${getServiceTypeColor(service.type)}`}>
                        {getServiceTypeLabel(service.type)}
                      </div>
                    </div>
                  ))}
                </div>

                {/* 展開/折疊按鈕 */}
                {hasMore && (
                  <div className="mt-4 pt-3 border-t border-white/10 flex items-center justify-center">
                    <button
                      onClick={() => setShowAll(!showAll)}
                      className="flex items-center gap-2 px-4 py-2 bg-[#0f1419]/60 text-white/70 rounded-lg text-sm font-medium hover:bg-[#1a202c]/60 hover:text-white transition-all border border-[#2d3748]/30"
                    >
                      {showAll ? (
                        <>
                          <ChevronUp className="w-4 h-4" />
                          收起
                        </>
                      ) : (
                        <>
                          <ChevronDown className="w-4 h-4" />
                          顯示更多 ({filteredServices.length - maxDisplayCount} 項)
                        </>
                      )}
                    </button>
                  </div>
                )}

                {/* 服務總數顯示 */}
                {showAll && (
                  <div className="mt-4 pt-3 border-t border-white/10">
                    <p className="text-white/60 text-sm text-center">
                      ... (共 {totalServices} 項服務)
                    </p>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>

      {/* 服務管理彈窗 */}
      <ServicesManagementModal
        isOpen={showManagementModal}
        onClose={handleCloseModal}
        creatorId={creatorId}
      />
    </div>
  );
};

export default ProfileServicesSection; 