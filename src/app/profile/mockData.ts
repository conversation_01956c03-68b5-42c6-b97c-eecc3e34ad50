import type { ISourceOptions } from "tsparticles-engine";

// Update mock user data with game stats
export const mockUserData = {
  username: "放牧猫",
  uniqueId: "#22JGVGRJ",
  isPremium: true,
  joinDate: "03/2025",
  avatar: "/lovable-uploads/f04b73ef-11c1-4465-968e-07be3d440339.png",
  level: 86,
  nextLevel: 87,
  currentXp: 28138,
  nextLevelXp: 93000,
  gameStats: {
    health: 6500,
    power: 13534,
    collectorLevel: 13,
  },
  motto: "回歸初心",
  achievements: {
    completed: 13,
    total: 20,
    latest: "月度最佳创作者",
  },
  tasks: {
    completed: 156,
    inProgress: 3,
  },
  connections: [
    {
      platform: "MetaMask",
      status: "已绑定",
      address: "0x1a2b...3c4d",
      icon: "Wallet",
      required: true,
    },
    {
      platform: "YouTube",
      status: "已认证",
      handle: "@ecnal",
      icon: "Youtube",
      required: true,
    },
    {
      platform: "Twitter",
      status: "已绑定",
      handle: "@ecnal",
      icon: "Twitter",
      required: true,
    },
  ],
  assets: {
    balance: 1234.56,
    change24h: 123.45,
    monthlyIncome: 789.12,
    totalIncome: 3456.78,
  },
  recentActivity: [
    { date: "2025/04/29", type: "短视频创作", reward: 50, status: "已完成" },
    { date: "2025/04/28", type: "社区互动", reward: 5, status: "已完成" },
    { date: "2025/04/27", type: "项目推广", reward: 30, status: "审核中" },
  ],
  creatorPrivileges: [
    "可参与悬赏任务",
    "专属客服支持",
    "数据分析工具",
    "平台优先推荐",
  ],
  taskCompletionRates: [
    { type: "视频任务", rate: 95 },
    { type: "社区任务", rate: 88 },
    { type: "推广任务", rate: 92 },
  ],
  collaboration: {
    bySession: {
      enabled: true,
      price: 5,
      description: "每次合作，适合单次内容推广",
    },
    byMonth: {
      enabled: true,
      price: 10,
      description: "包月合作，适合长期内容合作",
    },
  },
};

// Add mock video data
export const mockVideos = [
  {
    id: 1,
    thumbnail: "https://img.youtube.com/vi/dxj5U2i-w28/maxresdefault.jpg",
    title: "Web3 创作者经济新机遇",
    views: 1234,
    likes: 321,
    duration: "10:25",
    publishedAt: "2024-03-15",
    status: "已发布",
    url: "https://youtu.be/dxj5U2i-w28",
  },
  {
    id: 2,
    thumbnail: "https://img.youtube.com/vi/bsz2dVIRtrM/maxresdefault.jpg",
    title: "创作者直播：NFT 创作分享",
    views: 2156,
    likes: 543,
    duration: "15:30",
    publishedAt: "2024-03-10",
    status: "已发布",
    url: "https://www.youtube.com/live/bsz2dVIRtrM",
  },
  {
    id: 3,
    thumbnail: "https://img.youtube.com/vi/8qKtDG4QFVI/maxresdefault.jpg",
    title: "区块链技术入门指南",
    views: 3678,
    likes: 892,
    duration: "12:45",
    publishedAt: "2024-03-05",
    status: "已发布",
    url: "https://youtu.be/8qKtDG4QFVI",
  },
];

// Update achievements data to only include completed ones
export const mockAchievements = [
  {
    id: "1",
    icon: "⭐",
    name: "月度最佳创作者",
    rarity: "legendary" as const,
    obtainedAt: "2024-03-15",
  },
  {
    id: "2",
    icon: "🎥",
    name: "视频创作达人",
    rarity: "rare" as const,
    obtainedAt: "2024-03-10",
  },
  {
    id: "3",
    icon: "💎",
    name: "内容质量先锋",
    rarity: "epic" as const,
    obtainedAt: "2024-03-05",
  },
  {
    id: "4",
    icon: "🏆",
    name: "社区影响力",
    rarity: "rare" as const,
    obtainedAt: "2024-03-01",
  },
];

// Add particle configuration
export const particlesConfig: ISourceOptions = {
  particles: {
    number: {
      value: 50,
      density: {
        enable: true,
        value_area: 800,
      },
    },
    color: {
      value: ["#4ecdc4", "#f39c12", "#8e44ad"],
    },
    shape: {
      type: "circle",
    },
    opacity: {
      value: 0.5,
      animation: {
        enable: true,
        speed: 1,
      },
    },
    size: {
      value: 3,
      animation: {
        enable: true,
        speed: 2,
      },
    },
    links: {
      enable: true,
      distance: 150,
      color: "#4ecdc4",
      opacity: 0.2,
      width: 1,
    },
    move: {
      enable: true,
      speed: 2,
      direction: "none",
      random: true,
      straight: false,
      outModes: "out",
    },
  },
  interactivity: {
    events: {
      onHover: {
        enable: true,
        mode: "grab",
      },
      onClick: {
        enable: true,
        mode: "push",
      },
    },
    modes: {
      grab: {
        distance: 140,
        links: {
          opacity: 1,
        },
      },
      push: {
        quantity: 4,
      },
    },
  },
  detectRetina: true,
};

// YouTube API configuration
export const YOUTUBE_CLIENT_ID =
  "************-qis88gp4gdtf4qrm7qs7h893s6aj95nd.apps.googleusercontent.com"; // Replace with your actual client ID

export const YOUTUBE_REDIRECT_URI = `${
  typeof window !== "undefined" ? window.location.origin : ""
}/oauth/callback`;

export const YOUTUBE_SCOPE = "https://www.googleapis.com/auth/youtube.readonly";

// Use authorization code flow instead of implicit flow
export const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${YOUTUBE_CLIENT_ID}&redirect_uri=${encodeURIComponent(
  YOUTUBE_REDIRECT_URI
)}&response_type=code&scope=${encodeURIComponent(
  YOUTUBE_SCOPE
)}&access_type=offline`;
