import React from "react";
import { motion } from "framer-motion";
import { ChevronRight } from "lucide-react";

// Utility functions
export const getRarityColor = (rarity: string) => {
  switch (rarity) {
    case "传说":
      return "from-[#FFD700] to-[#FFA500]";
    case "史诗":
      return "from-[#FF1493] to-[#9400D3]";
    case "稀有":
      return "from-[#00CED1] to-[#4169E1]";
    default:
      return "from-[#32CD32] to-[#008000]";
  }
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case "已绑定":
    case "已认证":
    case "已完成":
      return "#00ff87";
    case "未绑定":
      return "#ff2e63";
    case "审核中":
      return "#ffd700";
    default:
      return "#60efff";
  }
};

// Add game-style button component
export const GameButton = ({
  children,
  onClick,
  variant = "primary",
  className = "",
}: {
  children: React.ReactNode;
  onClick: () => void;
  variant?: "primary" | "secondary";
  className?: string;
}) => (
  <motion.button
    whileHover={{ scale: 1.05, y: -2 }}
    whileTap={{ scale: 0.95 }}
    onClick={onClick}
    className={`
      relative px-6 py-3 rounded-lg font-medium text-white
      ${
        variant === "primary"
          ? "bg-gradient-to-r from-[#00CEC9] to-[#00B894] shadow-lg shadow-[#00CEC9]/30"
          : "bg-white/10 border border-white/20 backdrop-blur-sm"
      }
      overflow-hidden group
      ${className}
    `}
  >
    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
    {children}
  </motion.button>
);

// Game-style stat card component with animation
export const StatCard = ({
  label,
  value,
  icon,
}: {
  label: string;
  value: number;
  icon: React.ReactNode;
}) => (
  <motion.div
    initial={{ scale: 0.9, opacity: 0 }}
    animate={{ scale: 1, opacity: 1 }}
    transition={{ duration: 0.3 }}
    className="relative bg-[#1a1a2e]/60 p-4 rounded-xl border border-[#2a2a3e] hover:border-[#00cec9]/30 transition-all group"
  >
    <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-[#00cec9]/30 rounded-tl-xl" />
    <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-[#00cec9]/30 rounded-tr-xl" />
    <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-[#00cec9]/30 rounded-bl-xl" />
    <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-[#00cec9]/30 rounded-br-xl" />

    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="p-2 rounded-lg bg-[#00cec9]/20 text-[#00cec9] group-hover:bg-[#00cec9]/30 transition-colors">
          {icon}
        </div>
        <div>
          <p className="text-white/60 text-sm">{label}</p>
          <p className="text-white font-bold text-lg">
            {value.toLocaleString()}
          </p>
        </div>
      </div>
      <ChevronRight className="w-5 h-5 text-white/40 group-hover:text-[#00cec9] transition-colors" />
    </div>
  </motion.div>
);
