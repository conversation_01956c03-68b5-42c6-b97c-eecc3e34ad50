import React from "react";
import { motion } from "framer-motion";
import { Youtube } from "lucide-react";

interface YouTubeData {
  channelId: string;
  title: string;
  subscribers: number;
  views: number;
  videos: number;
  thumbnailUrl: string;
}

interface YouTubeStatsCardProps {
  youtubeData: YouTubeData | null;
  delay?: number;
}

const YouTubeStatsCard: React.FC<YouTubeStatsCardProps> = ({
  youtubeData,
  delay = 0.1,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
      className="relative bg-[#ff0000]/15 rounded-xl p-4 backdrop-blur-sm group hover:bg-[#ff0000]/20 transition-all border border-[#ff0000]/30 shadow-lg shadow-[#ff0000]/10"
    >
      <div className="flex items-center gap-3 mb-4">
        <div className="w-12 h-12 rounded-xl bg-[#ff0000]/25 flex items-center justify-center">
          <Youtube className="w-7 h-7 text-[#ff0000]" />
        </div>
        <div className="flex-1">
          <div className="text-white/60 text-sm">YouTube 数据</div>
          <div className="text-2xl font-bold text-white drop-shadow-[0_2px_2px_rgba(0,0,0,0.5)]">
            {youtubeData?.subscribers !== undefined
              ? youtubeData.subscribers.toLocaleString()
              : "未连接"}{" "}
            粉丝
          </div>
        </div>
      </div>

      <div className="bg-[#0f1419]/60 rounded-lg p-3 border border-[#2d3748]/30">
        <div className="text-white/60 text-xs mb-1">总播放量</div>
        <div className="text-xl font-bold text-white">
          {youtubeData?.views !== undefined
            ? youtubeData.views.toLocaleString()
            : "未连接"}{" "}
          次
        </div>
      </div>

      {!youtubeData && (
        <div className="mt-3 text-center">
          <span className="text-sm text-white/60">
            请通过下方账号绑定连接 YouTube 频道
          </span>
        </div>
      )}
    </motion.div>
  );
};

export default YouTubeStatsCard;
