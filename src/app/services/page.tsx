"use client";

import React from "react";
import DashboardLayout from "@/components/DashboardLayout";
import ServicesContent from "@/components/content/ServicesContent";

export const dynamic = "force-dynamic";

export default function ServicesPage() {
  return (
    <DashboardLayout
      enableDynamicContent={false}
      backgroundPattern="hexagon"
    >
      <ServicesContent />
    </DashboardLayout>
  );
}
