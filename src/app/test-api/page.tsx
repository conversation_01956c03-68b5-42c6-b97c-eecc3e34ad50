'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { servicesApi } from '@/services/servicesApi';
import type { Service } from '@/types/services';

export default function TestApiPage() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  
  // 表单数据
  const [formData, setFormData] = useState({
    type: 'embedded' as 'embedded' | 'custom',
    title: '测试视频植入广告',
    description: '这是一个测试服务，用于验证API功能',
    priceMin: 1000,
    priceMax: 5000
  });

  const addLog = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
    setLogs(prev => [...prev, logMessage]);
  };

  const clearLogs = () => setLogs([]);

  // 测试函数
  const testCreateService = async () => {
    setLoading(true);
    addLog('开始创建服务...');
    
    try {
      const result = await servicesApi.createService(formData);
      addLog(`创建服务成功，ID: ${result.id}`, 'success');
      await loadMyServices(); // 刷新列表
    } catch (error) {
      addLog(`创建服务失败: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadMyServices = async () => {
    setLoading(true);
    addLog('获取我的服务列表...');
    
    try {
      const result = await servicesApi.getMyServices();
      // 将分类的服务合并为单个数组用于展示
      const allServices = [...result.embedded, ...result.custom];
      setServices(allServices);
      addLog(`获取服务列表成功，共 ${allServices.length} 个服务`, 'success');
    } catch (error) {
      addLog(`获取服务列表失败: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const updateService = async (serviceId: string) => {
    setLoading(true);
    addLog(`更新服务 ${serviceId}...`);
    
    try {
      const updateData = {
        title: formData.title + ' (已更新)',
        priceMax: formData.priceMax + 1000
      };
      
      addLog(`更新数据: ${JSON.stringify(updateData)}`);
      await servicesApi.updateService(serviceId, updateData);
      addLog(`更新服务成功`, 'success');
      await loadMyServices(); // 刷新列表
    } catch (error) {
      addLog(`更新服务失败: ${error.message}`, 'error');
      // 添加更详细的错误信息
      if (error.message.includes('DOCTYPE')) {
        addLog('错误提示：返回的是HTML页面而不是JSON，可能是认证失败或路由错误', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const toggleServiceStatus = async (serviceId: string) => {
    setLoading(true);
    addLog(`切换服务状态 ${serviceId}...`);
    
    try {
      const service = services.find(s => s.id === serviceId);
      if (!service) {
        throw new Error('服务不存在');
      }
      
      addLog(`当前状态: ${service.status}，即将切换为: ${service.status === 'active' ? 'inactive' : 'active'}`);
      const result = await servicesApi.toggleServiceStatus(serviceId, service.status);
      addLog(`切换服务状态成功，新状态: ${result.status}`, 'success');
      await loadMyServices(); // 刷新列表
    } catch (error) {
      addLog(`切换服务状态失败: ${error.message}`, 'error');
      // 添加更详细的错误信息
      if (error.message.includes('DOCTYPE')) {
        addLog('错误提示：返回的是HTML页面而不是JSON，可能是认证失败或路由错误', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const deleteService = async (serviceId: string) => {
    if (!confirm('确定要删除这个服务吗？')) return;
    
    setLoading(true);
    addLog(`删除服务 ${serviceId}...`);
    
    try {
      await servicesApi.deleteService(serviceId);
      addLog(`删除服务成功`, 'success');
      await loadMyServices(); // 刷新列表
    } catch (error) {
      addLog(`删除服务失败: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const testAuth = async () => {
    setLoading(true);
    addLog('测试认证状态...');
    
    try {
      const response = await fetch('/api/services/my-services', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      addLog(`认证测试响应状态: ${response.status}`);
      addLog(`响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
      
      const responseText = await response.text();
      addLog(`响应内容前100字符: ${responseText.substring(0, 100)}`);
      
      if (response.status === 401) {
        addLog('认证失败 - 用户未登录', 'error');
      } else if (response.ok) {
        addLog('认证成功', 'success');
      } else {
        addLog(`认证异常 - HTTP ${response.status}`, 'error');
      }
    } catch (error) {
      addLog(`认证测试失败: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const runAllTests = async () => {
    addLog('开始运行所有测试...', 'info');
    
    // 0. 先测试认证
    await testAuth();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 1. 创建服务
    await testCreateService();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. 获取服务列表
    await loadMyServices();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 如果有服务，测试更新和状态切换
    if (services.length > 0) {
      const firstService = services[0];
      await updateService(firstService.id);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      await toggleServiceStatus(firstService.id);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    addLog('所有测试完成！', 'success');
  };

  return (
    <div className="container mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Services API 测试页面</h1>
        <p className="text-muted-foreground mt-2">手动测试服务相关API接口</p>
      </div>

      {/* 创建服务表单 */}
      <Card>
        <CardHeader>
          <CardTitle>创建新服务</CardTitle>
          <CardDescription>填写表单测试服务创建功能</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">服务类型</label>
              <Select 
                value={formData.type} 
                onValueChange={(value: 'embedded' | 'custom') => 
                  setFormData(prev => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="embedded">植入推广</SelectItem>
                  <SelectItem value="custom">定制推广</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium">服务标题</label>
              <Input
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="输入服务标题"
              />
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">服务描述</label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="输入服务描述"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">最低价格</label>
              <Input
                type="number"
                value={formData.priceMin}
                onChange={(e) => setFormData(prev => ({ ...prev, priceMin: parseInt(e.target.value) || 0 }))}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">最高价格</label>
              <Input
                type="number"
                value={formData.priceMax}
                onChange={(e) => setFormData(prev => ({ ...prev, priceMax: parseInt(e.target.value) || 0 }))}
              />
            </div>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button onClick={testAuth} variant="outline" disabled={loading}>
              测试认证
            </Button>
            <Button onClick={testCreateService} disabled={loading}>
              创建服务
            </Button>
            <Button onClick={loadMyServices} variant="outline" disabled={loading}>
              获取服务列表
            </Button>
            <Button onClick={runAllTests} variant="secondary" disabled={loading}>
              运行所有测试
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 服务列表 */}
      <Card>
        <CardHeader>
          <CardTitle>我的服务列表 ({services.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {services.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">暂无服务</p>
          ) : (
            <div className="space-y-4">
              {services.map(service => (
                <div key={service.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-medium">{service.title}</h3>
                      <p className="text-sm text-muted-foreground">{service.description}</p>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant={service.type === 'embedded' ? 'default' : 'secondary'}>
                        {service.type === 'embedded' ? '植入推广' : '定制推广'}
                      </Badge>
                      <Badge variant={service.status === 'active' ? 'default' : 'secondary'}>
                        {service.status === 'active' ? '活跃' : '停用'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground">
                      价格: ¥{service.priceMin} - ¥{service.priceMax}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateService(service.id)}
                        disabled={loading}
                      >
                        更新
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => toggleServiceStatus(service.id)}
                        disabled={loading}
                      >
                        切换状态
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => deleteService(service.id)}
                        disabled={loading}
                      >
                        删除
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 测试日志 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>测试日志</CardTitle>
          <Button onClick={clearLogs} variant="outline" size="sm">
            清空日志
          </Button>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <p className="text-gray-500">暂无日志...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 