"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { Youtube, CheckCircle2, AlertCircle } from "lucide-react";

interface ChannelData {
  channelId: string;
  title: string;
  subscribers: number;
  views: number;
  videos: number;
  thumbnailUrl: string;
  description: string;
  customUrl: string;
  publishedAt: string;
}

const OAuthCallback: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading"
  );
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [channelData, setChannelData] = useState<ChannelData | null>(null);

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
        // Check if we're in the browser
        if (typeof window === "undefined") return;

        // Extract the authorization code from URL query parameters
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        const error = urlParams.get("error");

        if (error) {
          setStatus("error");
          setErrorMessage(`Authorization denied: ${error}`);
          return;
        }

        if (!code) {
          setStatus("error");
          setErrorMessage("No authorization code found in the callback URL");
          return;
        }

        if (!user) {
          setStatus("error");
          setErrorMessage(
            "You must be logged in to connect your YouTube account"
          );
          return;
        }

        // Clean up URL immediately
        if (typeof window !== "undefined") {
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );
        }

        // Call our secure API route to handle the OAuth process
        const response = await fetch("/api/youtube/oauth-callback", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            code,
            redirect_uri: `${window.location.origin}/oauth/callback`,
            userId: user.id,
          }),
        });

        const data = await response.json();

        if (!response.ok) {
          setStatus("error");
          setErrorMessage(data.error || "Failed to process OAuth callback");
          return;
        }

        if (data.success && data.channelData) {
          setChannelData(data.channelData);
          setStatus("success");

          // Redirect to profile page after a short delay
          setTimeout(() => {
            router.push("/profile");
          }, 2000);
        } else {
          setStatus("error");
          setErrorMessage("Failed to connect YouTube account");
        }
      } catch (error) {
        console.error("Error processing OAuth callback:", error);
        setStatus("error");
        setErrorMessage(
          error instanceof Error ? error.message : "An unknown error occurred"
        );
      }
    };

    processOAuthCallback();
  }, [router, user]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#121212] to-[#1e1e1e] flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md bg-[#2a2a2a] rounded-xl shadow-xl overflow-hidden"
      >
        <div className="p-8 flex flex-col items-center text-center">
          {status === "loading" && (
            <>
              <div className="w-16 h-16 rounded-full bg-[#4a4a4a] flex items-center justify-center mb-4">
                <Youtube className="w-8 h-8 text-white animate-pulse" />
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">处理中...</h1>
              <p className="text-white/70">正在连接您的 YouTube 账号</p>
              <div className="mt-4 w-full bg-white/10 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-[#ff0000] to-[#ff4444] h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 2, ease: "easeInOut" }}
                />
              </div>
            </>
          )}

          {status === "success" && (
            <>
              <div className="w-16 h-16 rounded-full bg-[#22c55e]/20 flex items-center justify-center mb-4">
                <CheckCircle2 className="w-8 h-8 text-[#22c55e]" />
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">连接成功！</h1>
              <p className="text-white/70 mb-4">您的 YouTube 账号已成功连接</p>

              {channelData && (
                <div className="bg-white/5 rounded-lg p-4 mb-4 w-full">
                  <div className="flex items-center gap-3">
                    {channelData.thumbnailUrl && (
                      <img
                        src={channelData.thumbnailUrl}
                        alt={channelData.title}
                        className="w-12 h-12 rounded-full"
                      />
                    )}
                    <div className="text-left">
                      <div className="text-white font-medium">
                        {channelData.title}
                      </div>
                      <div className="text-white/60 text-sm">
                        {channelData.subscribers.toLocaleString()} 订阅者
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <p className="text-white/50 text-sm">正在返回个人资料页面...</p>
            </>
          )}

          {status === "error" && (
            <>
              <div className="w-16 h-16 rounded-full bg-[#ff2e63]/20 flex items-center justify-center mb-4">
                <AlertCircle className="w-8 h-8 text-[#ff2e63]" />
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">连接失败</h1>
              <p className="text-white/70 mb-6 text-sm">
                {errorMessage || "处理您的 YouTube 账号连接请求时出错"}
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => router.push("/profile")}
                  className="px-6 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
                >
                  返回个人资料
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="px-6 py-2 bg-[#ff2e63]/20 hover:bg-[#ff2e63]/30 text-[#ff2e63] rounded-lg transition-colors"
                >
                  重试
                </button>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default OAuthCallback;
