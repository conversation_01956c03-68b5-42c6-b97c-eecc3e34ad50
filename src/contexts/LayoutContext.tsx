"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface LayoutState {
  showHomeSidebar: boolean;
  showMobileNav: boolean;
  currentContent: string;
  isNavigating: boolean;
  backgroundPattern: "grid" | "hexagon" | "cyberpunk";
  sidebarCollapsed: boolean;
}

interface LayoutContextType {
  layoutState: LayoutState;
  updateLayoutState: (updates: Partial<LayoutState>) => void;
  toggleHomeSidebar: () => void;
  toggleMobileNav: () => void;
  toggleSidebarCollapsed: () => void;
  setCurrentContent: (content: string) => void;
  setNavigating: (navigating: boolean) => void;
  setBackgroundPattern: (pattern: "grid" | "hexagon" | "cyberpunk") => void;
}

const defaultLayoutState: LayoutState = {
  showHomeSidebar: true,
  showMobileNav: false,
  currentContent: "dashboard",
  isNavigating: false,
  backgroundPattern: "hexagon",
  sidebarCollapsed: false,
};

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

interface LayoutProviderProps {
  children: ReactNode;
}

export const LayoutProvider: React.FC<LayoutProviderProps> = ({ children }) => {
  const [layoutState, setLayoutState] = useState<LayoutState>(defaultLayoutState);

  // Load layout state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem("vgee-layout-state");
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        setLayoutState(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error("Failed to parse saved layout state:", error);
      }
    }
  }, []);

  // Save layout state to localStorage when it changes
  useEffect(() => {
    const stateToSave = {
      showHomeSidebar: layoutState.showHomeSidebar,
      backgroundPattern: layoutState.backgroundPattern,
      sidebarCollapsed: layoutState.sidebarCollapsed,
    };
    localStorage.setItem("vgee-layout-state", JSON.stringify(stateToSave));
  }, [layoutState.showHomeSidebar, layoutState.backgroundPattern, layoutState.sidebarCollapsed]);

  const updateLayoutState = (updates: Partial<LayoutState>) => {
    setLayoutState(prev => ({ ...prev, ...updates }));
  };

  const toggleHomeSidebar = () => {
    setLayoutState(prev => ({ ...prev, showHomeSidebar: !prev.showHomeSidebar }));
  };

  const toggleMobileNav = () => {
    setLayoutState(prev => ({ ...prev, showMobileNav: !prev.showMobileNav }));
  };

  const toggleSidebarCollapsed = () => {
    setLayoutState(prev => ({ ...prev, sidebarCollapsed: !prev.sidebarCollapsed }));
  };

  const setCurrentContent = (content: string) => {
    setLayoutState(prev => ({ ...prev, currentContent: content }));
  };

  const setNavigating = (navigating: boolean) => {
    setLayoutState(prev => ({ ...prev, isNavigating: navigating }));
  };

  const setBackgroundPattern = (pattern: "grid" | "hexagon" | "cyberpunk") => {
    setLayoutState(prev => ({ ...prev, backgroundPattern: pattern }));
  };

  const value: LayoutContextType = {
    layoutState,
    updateLayoutState,
    toggleHomeSidebar,
    toggleMobileNav,
    toggleSidebarCollapsed,
    setCurrentContent,
    setNavigating,
    setBackgroundPattern,
  };

  return (
    <LayoutContext.Provider value={value}>
      {children}
    </LayoutContext.Provider>
  );
};

export const useLayout = (): LayoutContextType => {
  const context = useContext(LayoutContext);
  if (!context) {
    throw new Error("useLayout must be used within a LayoutProvider");
  }
  return context;
};
