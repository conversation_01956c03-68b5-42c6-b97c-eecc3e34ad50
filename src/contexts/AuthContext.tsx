"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { createContext, useContext } from "react";
import type { ReactNode } from "react";

// Re-export Clerk<PERSON>rovider as AuthProvider for backward compatibility
export function AuthProvider({ children }: { children: ReactNode }) {
  return <ClerkProvider>{children}</ClerkProvider>;
}

// Create a context for any additional auth-related functionality
interface AuthContextType {
  // This context is mainly for providing Clerk functionality
  // The actual auth functionality is handled by the useAuth hook
  isProviderReady: boolean;
}

const AuthContext = createContext<AuthContextType>({
  isProviderReady: true,
});

export function useAuthContext() {
  const context = useContext(AuthContext);
  return context;
}

// Export the main useAuth hook for convenience
export { useAuth } from "@/hooks/useAuth";
