// Avatar upload service
export class AvatarService {
  private static instance: AvatarService;

  public static getInstance(): AvatarService {
    if (!AvatarService.instance) {
      AvatarService.instance = new AvatarService();
    }
    return AvatarService.instance;
  }

  /**
   * Upload avatar file to storage
   * @param file - The image file to upload
   * @param userId - The user ID for the avatar
   * @returns Promise<string> - The URL of the uploaded avatar
   */
  async uploadAvatar(file: File, userId: string): Promise<string> {
    try {
      // Validate file
      if (!file.type.startsWith("image/")) {
        throw new Error("请选择图片文件");
      }

      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        throw new Error("图片大小不能超过5MB");
      }

      // Create FormData for upload
      const formData = new FormData();
      formData.append("avatar", file);
      formData.append("userId", userId);

      // For now, we'll use a simple blob URL
      // In a real implementation, you would upload to your storage service
      // (e.g., AWS S3, Cloudinary, Supabase Storage, etc.)
      const avatarUrl = URL.createObjectURL(file);

      // Simulate upload delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // In a real implementation, you might do something like:
      // const response = await fetch('/api/upload-avatar', {
      //   method: 'POST',
      //   body: formData,
      // });
      //
      // if (!response.ok) {
      //   throw new Error('Upload failed');
      // }
      //
      // const { url } = await response.json();
      // return url;

      return avatarUrl;
    } catch (error) {
      console.error("Error uploading avatar:", error);
      throw error;
    }
  }

  /**
   * Delete avatar from storage
   * @param avatarUrl - The URL of the avatar to delete
   */
  async deleteAvatar(avatarUrl: string): Promise<void> {
    try {
      // In a real implementation, you would delete from your storage service
      // For blob URLs, we can revoke them
      if (avatarUrl.startsWith("blob:")) {
        URL.revokeObjectURL(avatarUrl);
      }

      // Example API call:
      // await fetch('/api/delete-avatar', {
      //   method: 'DELETE',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({ url: avatarUrl }),
      // });
    } catch (error) {
      console.error("Error deleting avatar:", error);
      throw error;
    }
  }

  /**
   * Resize image to fit avatar requirements
   * @param file - The image file to resize
   * @param maxSize - Maximum size in pixels (width/height)
   * @returns Promise<File> - The resized image file
   */
  async resizeImage(file: File, maxSize: number = 400): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;

        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }

        // Set canvas size and draw image
        canvas.width = width;
        canvas.height = height;

        if (ctx) {
          ctx.drawImage(img, 0, 0, width, height);

          canvas.toBlob(
            (blob) => {
              if (blob) {
                const resizedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: Date.now(),
                });
                resolve(resizedFile);
              } else {
                reject(new Error("Failed to resize image"));
              }
            },
            file.type,
            0.9
          );
        } else {
          reject(new Error("Canvas context not available"));
        }
      };

      img.onerror = () => reject(new Error("Failed to load image"));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Generate a thumbnail preview of the image
   * @param file - The image file
   * @returns Promise<string> - Base64 data URL of the thumbnail
   */
  async generateThumbnail(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");

          // Create thumbnail (150x150)
          canvas.width = 150;
          canvas.height = 150;

          if (ctx) {
            // Draw image centered and cropped
            const size = Math.min(img.width, img.height);
            const x = (img.width - size) / 2;
            const y = (img.height - size) / 2;

            ctx.drawImage(img, x, y, size, size, 0, 0, 150, 150);
            resolve(canvas.toDataURL());
          } else {
            reject(new Error("Canvas context not available"));
          }
        };
        img.src = e.target?.result as string;
      };
      reader.onerror = () => reject(new Error("Failed to read file"));
      reader.readAsDataURL(file);
    });
  }
}
