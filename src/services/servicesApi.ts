// 服务与报价模块前端API服务

import type {
  Service,
  CreateServiceRequest,
  UpdateServiceRequest,
  UpdateServiceStatusRequest,
  MyServicesResponse,
  PublicServicesResponse,
  ServiceStatusUpdateResponse,
  ApiResponse,
  MyServicesQueryParams,
  PublicServicesQueryParams,
  ServiceStatus
} from '@/types/services';

class ServicesApiService {
  private baseUrl = '/api';

  // 处理API响应
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as ApiResponse<T>;
    if (!data.success) {
      throw new Error(data.error?.message || 'API request failed');
    }

    return data.data!;
  }

  // 构建查询字符串
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });
    
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }

  // ============= 服务管理接口 =============

  /**
   * 获取我的服务列表
   */
  async getMyServices(params?: MyServicesQueryParams): Promise<MyServicesResponse> {
    const queryString = params ? this.buildQueryString(params) : '';
    const response = await fetch(`${this.baseUrl}/services/my-services${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return this.handleResponse<MyServicesResponse>(response);
  }

  /**
   * 创建新服务
   */
  async createService(data: CreateServiceRequest): Promise<Service> {
    const response = await fetch(`${this.baseUrl}/services`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    return this.handleResponse<Service>(response);
  }

  /**
   * 更新服务
   */
  async updateService(serviceId: string, data: UpdateServiceRequest): Promise<Service> {
    const response = await fetch(`${this.baseUrl}/services/${serviceId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    return this.handleResponse<Service>(response);
  }

  /**
   * 更新服务状态
   */
  async updateServiceStatus(
    serviceId: string, 
    status: ServiceStatus
  ): Promise<ServiceStatusUpdateResponse> {
    const response = await fetch(`${this.baseUrl}/services/${serviceId}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    });

    return this.handleResponse<ServiceStatusUpdateResponse>(response);
  }

  /**
   * 删除服务
   */
  async deleteService(serviceId: string): Promise<{ message: string }> {
    const response = await fetch(`${this.baseUrl}/services/${serviceId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return this.handleResponse<{ message: string }>(response);
  }

  // ============= 公开展示接口 =============

  /**
   * 获取创作者公开服务
   */
  async getPublicServices(
    creatorId: string,
    params?: PublicServicesQueryParams
  ): Promise<PublicServicesResponse> {
    const queryString = params ? this.buildQueryString(params) : '';
    const response = await fetch(
      `${this.baseUrl}/public/creators/${creatorId}/services${queryString}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    return this.handleResponse<PublicServicesResponse>(response);
  }

  // ============= 便捷方法 =============

  /**
   * 切换服务状态（上架/下架）
   */
  async toggleServiceStatus(serviceId: string, currentStatus: ServiceStatus): Promise<ServiceStatusUpdateResponse> {
    const newStatus: ServiceStatus = currentStatus === 'active' ? 'inactive' : 'active';
    return this.updateServiceStatus(serviceId, newStatus);
  }

  /**
   * 获取指定类型的我的服务
   */
  async getMyServicesByType(type: 'embedded' | 'custom'): Promise<Service[]> {
    const response = await this.getMyServices({ type });
    return response[type];
  }

  /**
   * 获取指定状态的我的服务
   */
  async getMyServicesByStatus(status: ServiceStatus): Promise<MyServicesResponse> {
    return this.getMyServices({ status });
  }

  /**
   * 批量删除服务
   */
  async deleteMultipleServices(serviceIds: string[]): Promise<void> {
    const deletePromises = serviceIds.map(id => this.deleteService(id));
    await Promise.all(deletePromises);
  }
}

// 导出单例实例
export const servicesApi = new ServicesApiService();

// 也导出类以供需要时创建新实例
export { ServicesApiService };
export default servicesApi; 