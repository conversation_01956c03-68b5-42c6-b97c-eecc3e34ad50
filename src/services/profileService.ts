import { db, testConnection } from "@/lib/db";
import type { Profile, NewProfile } from "@/lib/schema";

// 重试配置
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1秒

// 延迟函数
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// 重试装饰器
async function withRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries = MAX_RETRIES
): Promise<T> {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: unknown) {
      lastError = error;

      // 检查是否是连接相关的错误
      const errorObj = error as { code?: string; message?: string };
      const isConnectionError =
        errorObj?.code === "ECONNRESET" ||
        errorObj?.code === "ECONNREFUSED" ||
        errorObj?.code === "ETIMEDOUT" ||
        errorObj?.message?.includes("connection") ||
        errorObj?.message?.includes("timeout");

      if (isConnectionError && attempt < maxRetries) {
        console.warn(
          `${operationName} failed (attempt ${attempt}/${maxRetries}), retrying in ${RETRY_DELAY}ms...`,
          {
            error: errorObj.message,
            code: errorObj.code,
          }
        );

        // 测试连接是否恢复
        try {
          await testConnection();
        } catch (testError) {
          console.warn("Connection test failed, continuing with retry...");
        }

        await sleep(RETRY_DELAY * attempt); // 指数退避
        continue;
      }

      // 如果不是连接错误或已达到最大重试次数，则抛出错误
      break;
    }
  }

  throw lastError;
}

export class ProfileService {
  /**
   * Get a user profile by Clerk user ID
   */
  static async getProfile(clerkUserId: string): Promise<Profile | null> {
    return withRetry(async () => {
      const result = await db`
        SELECT * FROM profiles 
        WHERE clerk_user_id = ${clerkUserId}
      `;
      return (result[0] as Profile) || null;
    }, `getProfile(${clerkUserId})`);
  }

  /**
   * Create a new profile
   */
  static async createProfile(profileData: NewProfile): Promise<Profile> {
    return withRetry(async () => {
      const {
        clerk_user_id,
        email,
        clerk_username = null,
        email_verified = false,
        external_accounts = {},
        clerk_created_at = null,
        clerk_updated_at = null,
        full_name = null,
        avatar_url = null,
        motto = null,
        role = "creator",
        experience_points = 0,
        level = 1,
        is_premium_member = false,
        user_numeric_id = null,
        youtube_id = null,
        youtube_title = null,
        subscribers = 0,
        views = 0,
        wallet_address = null,
        profile_completion_percentage = 0,
        last_login_at = null,
        preferred_language = "en",
        notification_preferences = { email: true, push: true, in_app: true },
        social_links = {},
      } = profileData;

      const result = await db`
        INSERT INTO profiles (
          clerk_user_id, email, clerk_username, email_verified, external_accounts,
          clerk_created_at, clerk_updated_at, full_name, avatar_url, motto,
          role, experience_points, level, is_premium_member, user_numeric_id,
          youtube_id, youtube_title, subscribers, views, wallet_address,
          profile_completion_percentage, last_login_at, preferred_language,
          notification_preferences, social_links
        ) VALUES (
          ${clerk_user_id}, ${email}, ${clerk_username}, ${email_verified}, ${JSON.stringify(
        external_accounts
      )},
          ${clerk_created_at}, ${clerk_updated_at}, ${full_name}, ${avatar_url}, ${motto},
          ${role}, ${experience_points}, ${level}, ${is_premium_member}, ${user_numeric_id},
          ${youtube_id}, ${youtube_title}, ${subscribers}, ${views}, ${wallet_address},
          ${profile_completion_percentage}, ${last_login_at}, ${preferred_language},
          ${JSON.stringify(notification_preferences)}, ${JSON.stringify(
        social_links
      )}
        )
        RETURNING *
      `;
      return result[0] as Profile;
    }, `createProfile(${profileData.clerk_user_id})`);
  }

  /**
   * Update a user profile
   */
  static async updateProfile(
    clerkUserId: string,
    updates: Partial<Omit<Profile, "clerk_user_id" | "created_at">>
  ): Promise<Profile | null> {
    return withRetry(async () => {
      if (Object.keys(updates).length === 0) {
        return await this.getProfile(clerkUserId);
      }

      // Build the SET clause dynamically to handle null values properly
      const setClauses: string[] = [];
      const values: (string | number | boolean | null | Date)[] = [];

      // Helper function to add a field to update
      const addUpdate = (
        field: string,
        value: string | number | boolean | null | Date
      ) => {
        setClauses.push(`${field} = $${values.length + 1}`);
        values.push(value);
      };

      // Add updates for each field if it exists in the updates object
      if ("email" in updates) addUpdate("email", updates.email || null);
      if ("clerk_username" in updates)
        addUpdate("clerk_username", updates.clerk_username || null);
      if ("full_name" in updates) addUpdate("full_name", updates.full_name || null);
      if ("avatar_url" in updates) addUpdate("avatar_url", updates.avatar_url || null);
      if ("motto" in updates) addUpdate("motto", updates.motto || null);
      if ("youtube_id" in updates) addUpdate("youtube_id", updates.youtube_id || null);
      if ("youtube_title" in updates)
        addUpdate("youtube_title", updates.youtube_title || null);
      if ("subscribers" in updates)
        addUpdate("subscribers", updates.subscribers || 0);
      if ("views" in updates) addUpdate("views", updates.views || 0);
      if ("wallet_address" in updates)
        addUpdate("wallet_address", updates.wallet_address || null);
      if ("email_verified" in updates)
        addUpdate("email_verified", updates.email_verified || false);
      if ("external_accounts" in updates) {
        addUpdate(
          "external_accounts",
          updates.external_accounts
            ? JSON.stringify(updates.external_accounts)
            : null
        );
      }
      if ("clerk_updated_at" in updates)
        addUpdate("clerk_updated_at", updates.clerk_updated_at || null);
      if ("experience_points" in updates)
        addUpdate("experience_points", updates.experience_points || 0);
      if ("level" in updates)
        addUpdate("level", updates.level || 1);
      if ("last_login_at" in updates)
        addUpdate("last_login_at", updates.last_login_at || null);

      // Always update the updated_at timestamp - use a special case for NOW()
      setClauses.push("updated_at = NOW()");

      // Build the final query
      const query = `
        UPDATE profiles 
        SET ${setClauses.join(", ")}
        WHERE clerk_user_id = $${values.length + 1}
        RETURNING *
      `;

      // Add clerkUserId to the values array
      values.push(clerkUserId);

      const result = await db.unsafe(query, values);
      return (result[0] as unknown as Profile) || null;
    }, `updateProfile(${clerkUserId})`);
  }

  /**
   * Delete a user profile
   */
  static async deleteProfile(clerkUserId: string): Promise<void> {
    return withRetry(async () => {
      await db`
        DELETE FROM profiles 
        WHERE clerk_user_id = ${clerkUserId}
      `;
    }, `deleteProfile(${clerkUserId})`);
  }

  /**
   * Get all profiles (for admin purposes)
   */
  static async getAllProfiles(): Promise<Profile[]> {
    return withRetry(async () => {
      const result = await db`
        SELECT * FROM profiles 
        ORDER BY created_at DESC
      `;
      return result as unknown as Profile[];
    }, "getAllProfiles");
  }

  /**
   * Generate a unique numeric ID for the user
   */
  static async generateUniqueNumericId(): Promise<number> {
    const maxAttempts = 10;

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      // Generate a random 6-digit number
      const numericId = Math.floor(100000 + Math.random() * 900000);

      try {
        // Check if this ID is already taken
        const existingProfile = await withRetry(async () => {
          return await db`
            SELECT clerk_user_id FROM profiles 
            WHERE user_numeric_id = ${numericId}
          `;
        }, `checkNumericId(${numericId})`);

        if (existingProfile.length === 0) {
          return numericId;
        }
      } catch (error) {
        console.error("Error checking numeric ID uniqueness:", error);
      }
    }

    throw new Error(
      "Unable to generate unique numeric ID after maximum attempts"
    );
  }

  /**
   * Format numeric ID for display (e.g., #123456)
   */
  static formatUserNumericId(numericId: number): string {
    return `#${numericId.toString().padStart(6, "0")}`;
  }

  /**
   * Find profile by numeric ID
   */
  static async getProfileByNumericId(
    numericId: number
  ): Promise<Profile | null> {
    try {
      const result = await db`
        SELECT * FROM profiles 
        WHERE user_numeric_id = ${numericId}
      `;
      return (result[0] as Profile) || null;
    } catch (error) {
      console.error("Error getting profile by numeric ID:", error);
      throw error;
    }
  }

  /**
   * Find profile by email
   */
  static async getProfileByEmail(email: string): Promise<Profile | null> {
    try {
      const result = await db`
        SELECT * FROM profiles 
        WHERE email = ${email}
      `;
      return (result[0] as Profile) || null;
    } catch (error) {
      console.error("Error getting profile by email:", error);
      throw error;
    }
  }

  /**
   * Find profile by username
   */
  static async getProfileByUsername(username: string): Promise<Profile | null> {
    try {
      const result = await db`
        SELECT * FROM profiles 
        WHERE clerk_username = ${username}
      `;
      return (result[0] as Profile) || null;
    } catch (error) {
      console.error("Error getting profile by username:", error);
      throw error;
    }
  }

  /**
   * Update user's last login time
   */
  static async updateLastLogin(clerkUserId: string): Promise<void> {
    try {
      await db`
        UPDATE profiles 
        SET last_login_at = NOW()
        WHERE clerk_user_id = ${clerkUserId}
      `;
    } catch (error) {
      console.error("Error updating last login:", error);
      throw error;
    }
  }

  /**
   * Get leaderboard data sorted by specified field
   */
  static async getLeaderboard(
    sortBy: string = "experience_points",
    limit: number = 50
  ): Promise<Profile[]> {
    return withRetry(async () => {
      // 验证排序字段以防止SQL注入
      const allowedSortFields = [
        "experience_points",
        "subscribers",
        "views",
        "level",
      ];
      if (!allowedSortFields.includes(sortBy)) {
        throw new Error(`Invalid sort field: ${sortBy}`);
      }

      // 只返回有完整信息的用户（有full_name的）
      const result = await db.unsafe(
        `
        SELECT * FROM profiles 
        WHERE full_name IS NOT NULL 
        AND full_name != ''
        ORDER BY ${sortBy} DESC, created_at ASC
        LIMIT $1
      `,
        [limit]
      );

      return result as unknown as Profile[];
    }, `getLeaderboard(${sortBy}, ${limit})`);
  }
}
