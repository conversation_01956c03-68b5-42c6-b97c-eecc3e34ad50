/**
 * Simplified YouTube Authentication Service
 * 
 * This service uses OAuth only once to:
 * 1. Verify channel ownership
 * 2. Get the channel ID
 * 3. Store channel ID in user profile
 * 
 * All subsequent data fetching uses public APIs with the stored channel ID.
 */

interface ChannelVerificationResult {
  success: boolean;
  channelId?: string;
  channelTitle?: string;
  error?: string;
}

interface PublicChannelData {
  channelId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  customUrl?: string;
  subscribers: number | 'Hidden';
  subscribersHidden: boolean;
  views: number;
  videos: number;
  publishedAt: string;
  country?: string;
}

export class SimpleYouTubeAuth {
  private static instance: SimpleYouTubeAuth;

  private constructor() {}

  public static getInstance(): SimpleYouTubeAuth {
    if (!SimpleYouTubeAuth.instance) {
      SimpleYouTubeAuth.instance = new SimpleYouTubeAuth();
    }
    return SimpleYouTubeAuth.instance;
  }

  /**
   * Step 1: Initiate OAuth flow (one-time verification)
   */
  public async initiateVerification(userId: string): Promise<string> {
    const clientId = process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID;
    const redirectUri = `${window.location.origin}/api/youtube/verify-callback`;
    const scope = 'https://www.googleapis.com/auth/youtube.readonly';
    
    // Simple state for CSRF protection (no need for complex PKCE)
    const state = `${userId}_${Date.now()}_${Math.random().toString(36).substring(7)}`;
    
    // Store state temporarily
    sessionStorage.setItem('youtube_verify_state', state);
    
    const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?${new URLSearchParams({
      client_id: clientId!,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope,
      access_type: 'offline', // We'll use the token once and discard
      prompt: 'consent',
      state,
    })}`;

    return authUrl;
  }

  /**
   * Step 2: Handle OAuth callback and extract channel ID (one-time)
   */
  public async verifyChannelOwnership(
    code: string, 
    state: string
  ): Promise<ChannelVerificationResult> {
    try {
      // Verify state
      const storedState = sessionStorage.getItem('youtube_verify_state');
      if (!storedState || storedState !== state) {
        return { success: false, error: 'Invalid state parameter' };
      }
      
      // Clean up state
      sessionStorage.removeItem('youtube_verify_state');

      // Exchange code for temporary access token
      const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          client_id: process.env.NEXT_PUBLIC_YOUTUBE_CLIENT_ID!,
          client_secret: process.env.YOUTUBE_CLIENT_SECRET!,
          code,
          grant_type: 'authorization_code',
          redirect_uri: `${window.location.origin}/api/youtube/verify-callback`,
        }),
      });

      if (!tokenResponse.ok) {
        return { success: false, error: 'Failed to exchange code for token' };
      }

      const tokenData = await tokenResponse.json();

      // Use token once to get channel info
      const channelResponse = await fetch(
        'https://www.googleapis.com/youtube/v3/channels?part=snippet&mine=true',
        {
          headers: { 'Authorization': `Bearer ${tokenData.access_token}` },
        }
      );

      if (!channelResponse.ok) {
        return { success: false, error: 'Failed to fetch channel data' };
      }

      const channelData = await channelResponse.json();

      if (!channelData.items || channelData.items.length === 0) {
        return { success: false, error: 'No YouTube channel found' };
      }

      const channel = channelData.items[0];

      // We got what we needed - channel ID and basic info
      // Token will expire and we don't care
      return {
        success: true,
        channelId: channel.id,
        channelTitle: channel.snippet.title,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Verification failed',
      };
    }
  }

  /**
   * Step 3: Fetch current channel data using public API (ongoing)
   */
  public async getPublicChannelData(channelId: string): Promise<PublicChannelData | null> {
    try {
      const apiKey = process.env.YOUTUBE_API_KEY;
      const url = `https://www.googleapis.com/youtube/v3/channels?${new URLSearchParams({
        part: 'snippet,statistics',
        id: channelId,
        key: apiKey!,
      })}`;

      const response = await fetch(url);

      if (!response.ok) {
        console.error('Failed to fetch public channel data:', response.statusText);
        return null;
      }

      const data = await response.json();

      if (!data.items || data.items.length === 0) {
        console.error('Channel not found or not public');
        return null;
      }

      const channel = data.items[0];
      const snippet = channel.snippet;
      const statistics = channel.statistics;

      return {
        channelId: channel.id,
        title: snippet.title,
        description: snippet.description || '',
        thumbnailUrl: snippet.thumbnails?.default?.url || '',
        customUrl: snippet.customUrl,
        subscribers: statistics.hiddenSubscriberCount 
          ? 'Hidden' 
          : parseInt(statistics.subscriberCount || '0'),
        subscribersHidden: statistics.hiddenSubscriberCount || false,
        views: parseInt(statistics.viewCount || '0'),
        videos: parseInt(statistics.videoCount || '0'),
        publishedAt: snippet.publishedAt,
        country: snippet.country,
      };

    } catch (error) {
      console.error('Error fetching public channel data:', error);
      return null;
    }
  }

  /**
   * Utility: Check if channel is accessible via public API
   */
  public async isChannelPublic(channelId: string): Promise<boolean> {
    const data = await this.getPublicChannelData(channelId);
    return data !== null;
  }
}

export const simpleYouTubeAuth = SimpleYouTubeAuth.getInstance(); 