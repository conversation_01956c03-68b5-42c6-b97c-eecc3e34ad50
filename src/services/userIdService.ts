import { ProfileService } from "@/services/profileService";

export class UserIdService {
  private static instance: UserIdService;

  private constructor() {}

  static getInstance(): UserIdService {
    if (!UserIdService.instance) {
      UserIdService.instance = new UserIdService();
    }
    return UserIdService.instance;
  }

  /**
   * Generate a unique numeric user ID with collision checking
   * @param maxAttempts Maximum number of attempts to generate unique ID
   * @returns Promise<number> Unique 6-digit numeric ID
   */
  async generateUniqueNumericId(maxAttempts: number = 50): Promise<number> {
    return await ProfileService.generateUniqueNumericId();
  }

  /**
   * Format numeric ID for display
   * @param numericId The numeric ID to format
   * @returns Formatted ID string (e.g., "#123456")
   */
  formatUserNumericId(numericId: number | null): string {
    if (!numericId) return "#000000";
    return ProfileService.formatUserNumericId(numericId);
  }

  /**
   * Backfill numeric IDs for existing users without IDs
   * This method can be called to migrate existing users
   */
  async backfillExistingUsers(): Promise<{ success: number; failed: number }> {
    let successCount = 0;
    let failedCount = 0;

    try {
      // Find all profiles without numeric IDs
      const allProfiles = await ProfileService.getAllProfiles();
      const profilesWithoutIds = allProfiles.filter(
        (profile) => !profile.user_numeric_id
      );

      if (profilesWithoutIds.length === 0) {
        console.log("No profiles found without numeric IDs");
        return { success: 0, failed: 0 };
      }

      console.log(
        `Found ${profilesWithoutIds.length} profiles without numeric IDs`
      );

      // Generate and assign IDs for each profile
      for (const profile of profilesWithoutIds) {
        try {
          const numericId = await ProfileService.generateUniqueNumericId();

          await ProfileService.updateProfile(profile.clerk_user_id, {
            user_numeric_id: numericId,
          });

          successCount++;
          console.log(
            `Assigned ID ${this.formatUserNumericId(numericId)} to user ${
              profile.clerk_user_id
            }`
          );
        } catch (error) {
          console.error(
            `Failed to assign ID to user ${profile.clerk_user_id}:`,
            error
          );
          failedCount++;
        }
      }

      return { success: successCount, failed: failedCount };
    } catch (error) {
      console.error("Error during backfill operation:", error);
      throw error;
    }
  }
}
