export enum TaskType {
  SHORT_VIDEO = "SHORT_VIDEO",
  PROJECT_PROMOTION = "PROJECT_PROMOTION",
  COMMUNITY_INTERACTION = "COMMUNITY_INTERACTION",
  DEEP_CONTENT = "DEEP_CONTENT",
  PROJECT_ANALYSIS = "PROJECT_ANALYSIS",
  COMMUNITY_OPERATION = "COMMUNITY_OPERATION",
}

export enum TaskCategory {
  REGULAR = "REGULAR",
  BOUNTY = "BOUNTY",
}

export enum TaskStatus {
  OPEN = "OPEN",
  IN_PROGRESS = "IN_PROGRESS",
  SUBMITTED = "SUBMITTED",
  UNDER_REVIEW = "UNDER_REVIEW",
  COMPLETED = "COMPLETED",
  REJECTED = "REJECTED",
}

export interface Task {
  id: string;
  title: string;
  description: string;
  type: TaskType;
  category: TaskCategory;
  status: TaskStatus;
  reward: number;
  experiencePoints: number;
  deadline: Date;
  maxParticipants: number;
  remainingSlots?: number;
  requirements: string[];
  projectName: string;
  projectLogo: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskFormData {
  title: string;
  description: string;
  type: TaskType;
  category: TaskCategory;
  reward: number;
  experiencePoints: number;
  deadline: Date;
  maxParticipants: number;
  requirements: string[];
} 