// TypeScript types for the tools management system

// Tool categories enum
export type ToolCategory = 
  | 'video' 
  | 'image' 
  | 'audio' 
  | 'ai' 
  | 'design' 
  | 'productivity' 
  | 'analytics' 
  | 'other';

// Supported platforms enum
export type Platform = 
  | 'Web' 
  | 'Windows' 
  | 'Mac' 
  | 'iOS' 
  | 'Android';

// Tool interface representing the tools table
export interface Tool {
  id: string;
  name: string;
  description?: string | null;
  icon_url?: string | null;
  website_url?: string | null;
  category: ToolCategory;
  platform: Platform[];
  is_free: boolean;
  created_at: Date;
  updated_at: Date;
}

// New tool data for creation (without auto-generated fields)
export interface NewTool {
  name: string;
  description?: string;
  icon_url?: string;
  website_url?: string;
  category: ToolCategory;
  platform: Platform[];
  is_free?: boolean;
}

// Simplified user tool interaction interface
export interface UserTool {
  id: string;
  user_id: string;
  tool_id: string;
  is_selected: boolean;
  created_at: Date;
  updated_at: Date;
}

// New user tool data for creation
export interface NewUserTool {
  user_id: string;
  tool_id: string;
  is_selected?: boolean;
}

// Tool with user selection data (simplified)
export interface ToolWithUserData extends Tool {
  is_selected?: boolean;
}

// Simplified API request/response types for MVP
export interface GetToolsRequest {
  category?: ToolCategory;
}

export interface GetToolsResponse {
  tools: ToolWithUserData[];
}

export interface ToggleToolSelectionRequest {
  tool_id: string;
}

export interface GetUserToolsResponse {
  tools: ToolWithUserData[];
}

// API response wrapper types (consistent with existing project patterns)
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
}

// Tool statistics for analytics
export interface ToolStats {
  total_tools: number;
  tools_by_category: Record<ToolCategory, number>;
  tools_by_platform: Record<Platform, number>;
  free_tools_count: number;
  paid_tools_count: number;
}

// User tool statistics
export interface UserToolStats {
  total_favorites: number;
  total_tools_used: number;
  most_used_category: ToolCategory | null;
  favorite_tools: ToolWithUserData[];
  recently_used_tools: ToolWithUserData[];
}

// Filter options for frontend components
export interface ToolFilters {
  category?: ToolCategory | 'all';
  platform?: Platform | 'all';
  is_free?: boolean | 'all';
  search?: string;
}

// Sort options
export type ToolSortBy = 
  | 'name' 
  | 'created_at' 
  | 'use_count' 
  | 'last_used_at';

export type SortOrder = 'asc' | 'desc';

export interface ToolSortOptions {
  sort_by: ToolSortBy;
  order: SortOrder;
}

// Category display information
export interface CategoryInfo {
  key: ToolCategory;
  label: string;
  description: string;
  icon: string;
}

// Platform display information
export interface PlatformInfo {
  key: Platform;
  label: string;
  icon: string;
}

// Constants for frontend use
export const TOOL_CATEGORIES: CategoryInfo[] = [
  { key: 'video', label: '视频剪辑', description: '视频编辑和后期制作工具', icon: 'Video' },
  { key: 'image', label: '图片处理', description: '图像编辑和处理工具', icon: 'Image' },
  { key: 'audio', label: '音频制作', description: '音频编辑和制作工具', icon: 'Music' },
  { key: 'ai', label: 'AI助手', description: '人工智能辅助创作工具', icon: 'Bot' },
  { key: 'design', label: '设计工具', description: '界面和平面设计工具', icon: 'Palette' },
  { key: 'productivity', label: '效率工具', description: '提高工作效率的工具', icon: 'Zap' },
  { key: 'analytics', label: '数据分析', description: '数据统计和分析工具', icon: 'BarChart3' },
  { key: 'other', label: '其他工具', description: '其他类型的创作工具', icon: 'Package' },
];

export const PLATFORMS: PlatformInfo[] = [
  { key: 'Web', label: '网页版', icon: 'Globe' },
  { key: 'Windows', label: 'Windows', icon: 'Monitor' },
  { key: 'Mac', label: 'macOS', icon: 'Monitor' },
  { key: 'iOS', label: 'iOS', icon: 'Smartphone' },
  { key: 'Android', label: 'Android', icon: 'Smartphone' },
];

// Error codes specific to tools functionality
export const TOOL_ERROR_CODES = {
  TOOL_NOT_FOUND: 'TOOL_NOT_FOUND',
  TOOL_ALREADY_EXISTS: 'TOOL_ALREADY_EXISTS',
  INVALID_CATEGORY: 'INVALID_CATEGORY',
  INVALID_PLATFORM: 'INVALID_PLATFORM',
  USER_TOOL_NOT_FOUND: 'USER_TOOL_NOT_FOUND',
  USER_TOOL_ALREADY_EXISTS: 'USER_TOOL_ALREADY_EXISTS',
  UNAUTHORIZED_TOOL_OPERATION: 'UNAUTHORIZED_TOOL_OPERATION',
} as const;

export type ToolErrorCode = typeof TOOL_ERROR_CODES[keyof typeof TOOL_ERROR_CODES];
