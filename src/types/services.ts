// 服务与报价模块类型定义

export type ServiceType = 'embedded' | 'custom';
export type ServiceStatus = 'active' | 'inactive';

// 服务数据模型
export interface Service {
  id: string;
  type: ServiceType;
  title: string;
  description: string;
  priceMin: number;
  priceMax: number;
  status: ServiceStatus;
  createdAt: string;
  updatedAt: string;
}

// 创作者信息
export interface CreatorInfo {
  id: string;
  fullName: string;
  avatarUrl: string;
}

// ============= API 请求类型 =============

export interface CreateServiceRequest {
  type: ServiceType;
  title: string;
  description: string;
  priceMin: number;
  priceMax: number;
}

export interface UpdateServiceRequest {
  title?: string;
  description?: string;
  priceMin?: number;
  priceMax?: number;
}

export interface UpdateServiceStatusRequest {
  status: ServiceStatus;
}

// ============= API 响应类型 =============

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: string;
  };
}

export interface MyServicesResponse {
  embedded: Service[];
  custom: Service[];
}

export interface PublicServicesResponse {
  creatorInfo: CreatorInfo;
  services: {
    embedded: Omit<Service, 'status' | 'createdAt' | 'updatedAt'>[];
    custom: Omit<Service, 'status' | 'createdAt' | 'updatedAt'>[];
  };
}

export interface ServiceStatusUpdateResponse {
  id: string;
  status: ServiceStatus;
  updatedAt: string;
}

// ============= API 查询参数类型 =============

export interface MyServicesQueryParams {
  type?: ServiceType;
  status?: ServiceStatus;
}

export interface PublicServicesQueryParams {
  type?: ServiceType;
}

// ============= 错误代码枚举 =============

export enum ApiErrorCode {
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DUPLICATE_SERVICE = 'DUPLICATE_SERVICE',
  PRICE_RANGE_INVALID = 'PRICE_RANGE_INVALID'
} 