// 代币池相关类型定义
export interface TokenPoolData {
  totalAssets: number;
  change24h: number;
  tokens: {
    symbol: string;
    amount: number;
    usdValue: number;
    percentage: number;
    change24h?: number;
  }[];
  recentTransactions: {
    type: "in" | "out";
    amount: number;
    symbol: string;
    timestamp: string;
    status: "success" | "pending" | "failed";
  }[];
  taskPool: {
    total: number;
    used: number;
    byType: {
      type: string;
      amount: number;
      percentage: number;
    }[];
  };
}

// 悬赏任务类型
export interface BountyTask {
  id: string;
  title: string;
  description: string;
  reward: number;
  progress: number;
  progressTotal: number;
  tag: string;
  cta: string;
  timeLeft: string;
}

// 职业赏金任务类型
export interface CareerTask {
  id: string;
  title: string;
  description: string;
  reward: number;
  progress: number;
  progressTotal: number;
  tag: string;
  cta: string;
  timeLeft: string;
  icon: "youtube";
}

// 活动概览类型
export interface ActivityOverview {
  completedTasks: number;
  totalRpx: number;
  nextGoal: string;
}
