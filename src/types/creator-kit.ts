export type CreatorKitCategory = "account" | "tools" | "guide";

export interface CreatorKitStep {
  title: string;
  content: string;
  tips?: string[];
  links?: {
    text: string;
    url: string;
  }[];
}

export interface CreatorKitTask {
  id: string;
  title: string;
  description: string;
  category: CreatorKitCategory;
  steps: CreatorKitStep[];
  progress: number;
  totalSteps: number;
  reward: number;
  icon: string;
  isCompleted: boolean;
}

export interface CreatorKitProgress {
  completedTasks: number;
  totalTasks: number;
  currentLevel: number;
  xpProgress: number;
  nextLevelXp: number;
}

export interface CreatorKitState {
  tasks: CreatorKitTask[];
  progress: CreatorKitProgress;
  isExpanded: boolean;
  activeTaskId: string | null;
}
