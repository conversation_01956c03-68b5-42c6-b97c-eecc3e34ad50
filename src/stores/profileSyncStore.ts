import { create } from "zustand";
import type { UserProfile } from "@/hooks/useProfile";

interface ProfileSyncResult {
  success: boolean;
  message: string;
  profile?: UserProfile;
}

interface ProfileSyncState {
  // 全局同步状态
  hasSynced: boolean;
  currentUserId: string | null;
  isSyncing: boolean;
  syncError: string | null;
  syncResult: ProfileSyncResult | null;
  
  // Actions
  setHasSynced: (synced: boolean) => void;
  setCurrentUserId: (userId: string | null) => void;
  setIsSyncing: (syncing: boolean) => void;
  setSyncError: (error: string | null) => void;
  setSyncResult: (result: ProfileSyncResult | null) => void;
  resetSyncState: () => void;
  resetForNewUser: (userId: string) => void;
}

export const useProfileSyncStore = create<ProfileSyncState>((set) => ({
  // 初始状态
  hasSynced: false,
  currentUserId: null,
  isSyncing: false,
  syncError: null,
  syncResult: null,

  // Actions
  setHasSynced: (synced) => set({ hasSynced: synced }),
  setCurrentUserId: (userId) => set({ currentUserId: userId }),
  setIsSyncing: (syncing) => set({ isSyncing: syncing }),
  setSyncError: (error) => set({ syncError: error }),
  setSyncResult: (result) => set({ syncResult: result }),

  // 重置所有同步状态
  resetSyncState: () => set({
    hasSynced: false,
    currentUserId: null,
    isSyncing: false,
    syncError: null,
    syncResult: null,
  }),

  // 为新用户重置状态
  resetForNewUser: (userId) => set({
    hasSynced: false,
    currentUserId: userId,
    isSyncing: false,
    syncError: null,
    syncResult: null,
  }),
})); 